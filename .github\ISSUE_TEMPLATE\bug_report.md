---
name: Bug Report
about: Create a report to help us improve
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ''
---

## 🐛 Bug Description

A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce

Steps to reproduce the behavior:

1. Go to '...'
2. Click on '....'
3. <PERSON>roll down to '....'
4. See error

## ✅ Expected Behavior

A clear and concise description of what you expected to happen.

## ❌ Actual Behavior

A clear and concise description of what actually happened.

## 📸 Screenshots

If applicable, add screenshots to help explain your problem.

## 🌍 Environment

**Desktop (please complete the following information):**
- OS: [e.g. Windows 10, macOS 12.0, Ubuntu 20.04]
- Browser: [e.g. Chrome 96, Firefox 95, Safari 15]
- Version: [e.g. 2.0.1]

**Server Environment:**
- Python Version: [e.g. 3.11.0]
- Node.js Version: [e.g. 18.12.0]
- Docker Version: [e.g. 20.10.17]
- Kubernetes Version: [e.g. 1.25.0]

**Additional Context:**
- Database: [e.g. PostgreSQL 15.0]
- Redis Version: [e.g. 7.0.5]
- GPU: [e.g. NVIDIA RTX 4090, None]

## 📋 Additional Context

Add any other context about the problem here.

## 🔍 Error Logs

```
Paste any relevant error logs here
```

## 🧪 Minimal Reproducible Example

If possible, provide a minimal code example that reproduces the issue:

```python
# Your code here
```

## 🔧 Possible Solution

If you have ideas on how to fix the issue, please describe them here.

## 📝 Checklist

- [ ] I have searched for existing issues
- [ ] I have provided all the requested information
- [ ] I have tested this on the latest version
- [ ] I have included error logs (if applicable)
- [ ] I have provided a minimal reproducible example (if applicable)
