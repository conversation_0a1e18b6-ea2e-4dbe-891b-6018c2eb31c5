# Pull Request

## 📋 Description

Brief description of what this PR does.

Fixes #(issue number)

## 🔄 Type of Change

- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Refactoring (no functional changes)
- [ ] ⚡ Performance improvement
- [ ] 🧪 Test addition or improvement
- [ ] 🔒 Security improvement

## 🧪 Testing

- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] E2E tests pass
- [ ] Manual testing completed
- [ ] Performance testing (if applicable)

**Test Coverage:**
- Current coverage: __%
- Coverage after changes: __%

**Manual Testing Steps:**
1. Step 1
2. Step 2
3. Step 3

## 📸 Screenshots (if applicable)

| Before | After |
|--------|-------|
| ![before](url) | ![after](url) |

## 🔍 Code Quality

- [ ] Code follows the project's style guidelines
- [ ] Self-review of code completed
- [ ] Code is properly commented
- [ ] No console.log or print statements left in code
- [ ] No hardcoded values (use environment variables)
- [ ] Error handling implemented where appropriate

**Linting Results:**
- [ ] Python: Black, isort, flake8, mypy all pass
- [ ] TypeScript: ESLint, Prettier all pass
- [ ] No new linting warnings introduced

## 🔒 Security

- [ ] No sensitive information exposed
- [ ] Input validation implemented
- [ ] Authentication/authorization considered
- [ ] SQL injection prevention (if applicable)
- [ ] XSS prevention (if applicable)
- [ ] CSRF protection (if applicable)

## 📚 Documentation

- [ ] README updated (if needed)
- [ ] API documentation updated (if needed)
- [ ] Inline code comments added
- [ ] CHANGELOG.md updated
- [ ] Migration guide provided (for breaking changes)

## 🚀 Deployment

- [ ] Database migrations included (if needed)
- [ ] Environment variables documented
- [ ] Deployment instructions updated
- [ ] Rollback plan considered
- [ ] Feature flags used (if applicable)

## ⚡ Performance

- [ ] Performance impact assessed
- [ ] No significant performance regression
- [ ] Database queries optimized (if applicable)
- [ ] Caching strategy considered
- [ ] Memory usage optimized

## 🔗 Dependencies

- [ ] No new dependencies added
- [ ] New dependencies justified and documented
- [ ] Dependencies are up to date
- [ ] License compatibility checked
- [ ] Security vulnerabilities checked

## 🧩 Backwards Compatibility

- [ ] Changes are backwards compatible
- [ ] Breaking changes documented
- [ ] Migration path provided
- [ ] Deprecation warnings added (if applicable)

## 📋 Checklist

- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## 🔍 Review Focus Areas

Please pay special attention to:
- [ ] Security implications
- [ ] Performance impact
- [ ] Error handling
- [ ] Edge cases
- [ ] User experience
- [ ] API design
- [ ] Database schema changes

## 📝 Additional Notes

Add any additional notes for reviewers here.

## 🏷️ Related Issues

- Closes #
- Related to #
- Depends on #

---

**For Reviewers:**
- [ ] Code review completed
- [ ] Security review completed
- [ ] Performance review completed
- [ ] Documentation review completed
- [ ] Testing strategy approved
