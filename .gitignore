# Enterprise AI/ML Platform - Git Ignore File
# Comprehensive .gitignore for Python, Node.js, Docker, and AI/ML projects

# =============================================================================
# ENVIRONMENT & SECRETS
# =============================================================================
.env
.env.local
.env.development
.env.test
.env.production
.env.staging
*.env

# Environment variables
.envrc
.direnv/

# =============================================================================
# PYTHON
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =============================================================================
# NODE.JS / FRONTEND
# =============================================================================

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# React build output
build/
dist/

# =============================================================================
# DOCKER & CONTAINERS
# =============================================================================

# Docker
.dockerignore
docker-compose.override.yml
.docker/

# =============================================================================
# AI/ML SPECIFIC
# =============================================================================

# Model files
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.pb
*.pth
*.pt
*.ckpt
*.safetensors
*.bin

# Large datasets
*.csv
*.json
*.parquet
*.feather
*.arrow
*.npy
*.npz
*.mat

# Weights and model checkpoints
checkpoints/
models/
weights/
saved_models/
artifacts/

# MLflow
mlruns/
mlartifacts/

# Weights & Biases
wandb/

# TensorBoard logs
logs/
tensorboard_logs/

# Jupyter notebook checkpoints
.ipynb_checkpoints/

# Data directories
data/
datasets/
raw_data/
processed_data/
temp_data/

# =============================================================================
# CLOUD & INFRASTRUCTURE
# =============================================================================

# Terraform
*.tfstate
*.tfstate.*
*.tfvars
.terraform/
.terraform.lock.hcl
terraform.tfplan
terraform.tfplan.*

# Kubernetes
*.kubeconfig
kubeconfig*

# AWS
.aws/
*.pem

# Google Cloud
*.json
service-account*.json
gcloud-service-key.json

# Azure
*.publishsettings

# =============================================================================
# DATABASES
# =============================================================================

# SQLite
*.sqlite
*.sqlite3
*.db

# PostgreSQL
*.dump
*.sql

# MongoDB
*.bson

# =============================================================================
# OPERATING SYSTEM
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# IDEs & EDITORS
# =============================================================================

# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# TEMPORARY & CACHE FILES
# =============================================================================

# General
*.tmp
*.temp
*.cache
*.bak
*.backup
*.orig
*.rej

# Thumbnails
*.thumb

# =============================================================================
# SECURITY & SENSITIVE DATA
# =============================================================================

# API Keys and secrets
secrets/
.secrets/
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.der

# Configuration files with sensitive data
config.ini
config.yaml
config.json
settings.ini
settings.yaml
settings.json

# =============================================================================
# LOGS & MONITORING
# =============================================================================

# Application logs
*.log
logs/
log/

# Monitoring data
prometheus_data/
grafana_data/
elasticsearch_data/

# =============================================================================
# CUSTOM PROJECT EXCLUSIONS
# =============================================================================

# Virtual environments
venv/
.venv/
env/
.env/

# Local development
.local/
local/

# Test outputs
test-results/
test_results/
.coverage
htmlcov/

# Documentation builds
docs/_build/
site/

# Backup files
*.bak
*.backup
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
