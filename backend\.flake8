[flake8]
# Flake8 configuration for Enterprise AI/ML Platform Backend

# Maximum line length
max-line-length = 88

# Maximum complexity
max-complexity = 10

# Exclude directories
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    .env,
    build,
    dist,
    *.egg-info,
    .pytest_cache,
    .coverage,
    htmlcov,
    .mypy_cache,
    migrations,
    alembic

# Ignore specific error codes
ignore = 
    E203,  # whitespace before ':'
    E501,  # line too long (handled by black)
    W503,  # line break before binary operator
    W504,  # line break after binary operator
    F401,  # imported but unused (handled by isort)
    E402,  # module level import not at top of file

# Select specific error codes to check
select = 
    E,     # pycodestyle errors
    W,     # pycodestyle warnings
    F,     # pyflakes
    C,     # mccabe complexity
    B,     # flake8-bugbear
    I,     # isort

# Per-file ignores
per-file-ignores =
    __init__.py:F401
    tests/*:S101,S106
    conftest.py:F401,F403

# Import order style
import-order-style = google

# Application import names
application-import-names = app

# Docstring conventions
docstring-convention = google
