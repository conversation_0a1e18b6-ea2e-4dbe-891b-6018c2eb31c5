# NeuroFlowAI GPU Orchestrator Dockerfile
# =======================================
# Specialized Docker image for the GPU Orchestrator with CUDA support

FROM nvidia/cuda:11.8-devel-ubuntu20.04

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3.9 \
    python3.9-dev \
    python3-pip \
    build-essential \
    curl \
    git \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Create symlink for python
RUN ln -s /usr/bin/python3.9 /usr/bin/python

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install GPU-specific dependencies
RUN pip install --no-cache-dir \
    torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118 \
    tensorflow[and-cuda] \
    ray[default] \
    kubernetes \
    nvidia-ml-py3

# Copy application code
COPY . .

# Create non-root user
RUN groupadd -r gpu-orchestrator && useradd -r -g gpu-orchestrator gpu-orchestrator
RUN chown -R gpu-orchestrator:gpu-orchestrator /app
USER gpu-orchestrator

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the GPU orchestrator
CMD ["python", "-m", "app.services.k8s_gpu_orchestrator"]
