# NeuroFlowAI Agent Orchestrator Dockerfile
# =========================================
# Specialized Docker image for the Agent Orchestrator

FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libpq-dev \
    redis-tools \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional orchestrator dependencies
RUN pip install --no-cache-dir \
    celery[redis] \
    flower \
    prometheus-client

# Copy application code
COPY . .

# Create non-root user
RUN groupadd -r orchestrator && useradd -r -g orchestrator orchestrator
RUN chown -R orchestrator:orchestrator /app
USER orchestrator

# Expose ports
EXPOSE 8080 5555

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the orchestrator
CMD ["python", "-m", "app.services.agent_orchestrator"]
