# Enterprise AI/ML Platform - Backend

## Overview

High-performance FastAPI backend with async support, enterprise security, and comprehensive AI/ML capabilities.

## Quick Start

### Prerequisites

- Python 3.11+
- [uv](https://github.com/astral-sh/uv) (Python package manager)
- PostgreSQL 15+
- Redis 7+
- MongoDB 6+

### Installation

```bash
# Install uv if not already installed
pip install uv

# Create virtual environment and install dependencies
uv venv
uv pip sync pyproject.toml

# Activate virtual environment
# On Windows
.venv\Scripts\activate
# On Unix/MacOS
source .venv/bin/activate
```

### Development

```bash
# Install with development dependencies
uv pip install -e ".[dev]"

# Run development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Or use the CLI
aiml-platform start-server --reload
```

### Testing

```bash
# Run tests
pytest

# Run tests with coverage
pytest --cov=app --cov-report=html

# Run specific test types
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m "not slow"    # Exclude slow tests
```

### Code Quality

```bash
# Format code
black .
isort .

# Lint code
flake8 .
mypy .

# Security scan
bandit -r app/

# Pre-commit hooks
pre-commit install
pre-commit run --all-files
```

## Architecture

### Core Components

- **FastAPI** - Modern, fast web framework
- **SQLAlchemy 2.0** - Async ORM with type safety
- **Alembic** - Database migrations
- **Celery** - Distributed task queue
- **Redis** - Caching and message broker
- **PostgreSQL** - Primary database
- **MongoDB** - Document storage

### Security

- JWT authentication with refresh tokens
- Role-based access control (RBAC)
- Password hashing with bcrypt
- Rate limiting and API protection
- Comprehensive audit logging

### Monitoring

- Prometheus metrics
- Structured logging with structlog
- Health checks and readiness probes
- Performance monitoring

## API Documentation

Once the server is running, visit:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI Schema**: http://localhost:8000/openapi.json

## Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Database
DATABASE_URL=postgresql+asyncpg://user:pass@localhost/dbname
REDIS_URL=redis://localhost:6379/0
MONGODB_URL=mongodb://localhost:27017/dbname

# Security
SECRET_KEY=your-super-secret-key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# External Services
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Monitoring
PROMETHEUS_ENABLED=true
SENTRY_DSN=your-sentry-dsn
```

## Production Deployment

### Docker

```bash
# Build image
docker build -t aiml-platform-backend .

# Run container
docker run -p 8000:8000 aiml-platform-backend
```

### Kubernetes

```bash
# Deploy to Kubernetes
kubectl apply -f ../kubernetes/manifests/backend/
```

## Contributing

Please read the [Contributing Guide](../CONTRIBUTING.md) for development guidelines.

## License

MIT License - see [LICENSE](../LICENSE) for details.

