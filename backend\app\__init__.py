"""
Enterprise AI/ML Platform Backend Application.

This package contains the FastAPI backend application for the
Enterprise AI/ML Platform, providing comprehensive AI/ML capabilities
through a production-ready REST API.
"""

__version__ = "2.0.0"
__title__ = "Enterprise AI/ML Platform"
__description__ = "Production-ready no-code AI/ML platform for building intelligent workflows"
__author__ = "Enterprise AI/ML Platform Team"
__email__ = "<EMAIL>"
__license__ = "MIT"
__url__ = "https://github.com/aiml-platform/platform"

# Version info tuple
VERSION = tuple(map(int, __version__.split('.')))

# Export main components
from .main import app

__all__ = [
    "app",
    "__version__",
    "__title__",
    "__description__",
    "__author__",
    "__email__",
    "__license__",
    "__url__",
    "VERSION",
]
