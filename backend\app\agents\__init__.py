"""
NeuroFlowAI Agent Framework
==========================

Autonomous AI agents for building, deploying, and managing AI workflows.
Each agent is specialized for specific tasks and operates with minimal human intervention.

Core Agents:
- ArchitectAgent: Designs optimal model pipelines
- EngineerAgent: Selects models, tuning methods, data splits
- TrainerAgent: Handles GPU-based training jobs
- DevOpsAgent: Manages deployment, rollback, scaling
- SentinelAgent: Monitors drift, performance, security
- ProductizerAgent: Converts pipelines into monetizable APIs

Author: NeuroFlowAI Team
Version: 1.0.0
License: MIT
"""

from .base_agent import BaseAgent, AgentState, AgentMessage, AgentTask
from .architect_agent import ArchitectAgent
from .engineer_agent import EngineerAgent
from .trainer_agent import TrainerAgent
from .devops_agent import DevOpsAgent
from .sentinel_agent import SentinelAgent
from .productizer_agent import ProductizerAgent

__all__ = [
    "BaseAgent",
    "AgentState", 
    "AgentMessage",
    "AgentTask",
    "ArchitectAgent",
    "EngineerAgent", 
    "TrainerAgent",
    "DevOpsAgent",
    "SentinelAgent",
    "ProductizerAgent"
]

__version__ = "1.0.0"
