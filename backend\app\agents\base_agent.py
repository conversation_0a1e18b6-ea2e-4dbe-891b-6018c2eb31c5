"""
Base Agent Framework for NeuroFlowAI
====================================

Core agent architecture with Goal/Planner/Executor/Memory components.
All specialized agents inherit from this base class.
"""

import asyncio
import json
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Callable
from pydantic import BaseModel, Field

import logging
logger = logging.getLogger(__name__)


class AgentState(str, Enum):
    """Agent execution states."""
    IDLE = "idle"
    PLANNING = "planning"
    EXECUTING = "executing"
    WAITING = "waiting"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"


class MessageType(str, Enum):
    """Inter-agent message types."""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    STATUS_UPDATE = "status_update"
    RESOURCE_REQUEST = "resource_request"
    ALERT = "alert"
    COORDINATION = "coordination"


@dataclass
class AgentMessage:
    """Message structure for inter-agent communication."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender_id: str = ""
    receiver_id: str = ""
    message_type: MessageType = MessageType.TASK_REQUEST
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    priority: int = 1  # 1=low, 5=high
    requires_response: bool = False
    correlation_id: Optional[str] = None


@dataclass
class AgentTask:
    """Task structure for agent execution."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    objectives: List[str] = field(default_factory=list)
    priority: int = 1
    deadline: Optional[datetime] = None
    dependencies: List[str] = field(default_factory=list)
    status: str = "pending"
    progress: float = 0.0
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class AgentMemory:
    """Long-term memory system for agents."""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.experiences: List[Dict[str, Any]] = []
        self.learned_patterns: Dict[str, Any] = {}
        self.performance_history: List[Dict[str, Any]] = []
        self.preferences: Dict[str, Any] = {}
    
    async def store_experience(self, experience: Dict[str, Any]) -> None:
        """Store a new experience in memory."""
        experience["timestamp"] = datetime.utcnow().isoformat()
        experience["agent_id"] = self.agent_id
        self.experiences.append(experience)
        
        # Keep only last 1000 experiences
        if len(self.experiences) > 1000:
            self.experiences = self.experiences[-1000:]
    
    async def recall_similar_experiences(self, context: Dict[str, Any], limit: int = 5) -> List[Dict[str, Any]]:
        """Recall similar past experiences based on context."""
        # Simple similarity matching - can be enhanced with embeddings
        similar = []
        for exp in self.experiences[-100:]:  # Check recent experiences
            similarity_score = self._calculate_similarity(context, exp)
            if similarity_score > 0.5:
                similar.append({"experience": exp, "similarity": similarity_score})
        
        # Sort by similarity and return top matches
        similar.sort(key=lambda x: x["similarity"], reverse=True)
        return [item["experience"] for item in similar[:limit]]
    
    def _calculate_similarity(self, context1: Dict[str, Any], context2: Dict[str, Any]) -> float:
        """Calculate similarity between two contexts."""
        # Simple keyword-based similarity
        keys1 = set(str(context1).lower().split())
        keys2 = set(str(context2).lower().split())
        
        if not keys1 or not keys2:
            return 0.0
        
        intersection = keys1.intersection(keys2)
        union = keys1.union(keys2)
        
        return len(intersection) / len(union) if union else 0.0


class BaseAgent(ABC):
    """
    Base class for all NeuroFlowAI agents.
    
    Each agent has four core components:
    1. Goal Module: Defines desired outcomes
    2. Planner: Breaks tasks into subgoals
    3. Executor: Chooses and executes tools
    4. Memory: Long-term learning and adaptation
    """
    
    def __init__(
        self,
        agent_id: str,
        name: str,
        description: str,
        capabilities: List[str],
        max_concurrent_tasks: int = 3,
        **kwargs
    ):
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.capabilities = capabilities
        self.max_concurrent_tasks = max_concurrent_tasks
        
        # Core components
        self.state = AgentState.IDLE
        self.memory = AgentMemory(agent_id)
        self.current_tasks: Dict[str, AgentTask] = {}
        self.message_queue: List[AgentMessage] = []
        
        # Performance tracking
        self.tasks_completed = 0
        self.tasks_failed = 0
        self.average_completion_time = 0.0
        self.last_activity = datetime.utcnow()
        
        # Configuration
        self.config = kwargs
        self.is_running = False
        self.feedback_callbacks: List[Callable] = []
    
    async def start(self) -> None:
        """Start the agent's main execution loop."""
        self.is_running = True
        self.state = AgentState.IDLE
        logger.info(f"Agent {self.name} ({self.agent_id}) started")
        
        # Start main execution loop
        asyncio.create_task(self._main_loop())
    
    async def stop(self) -> None:
        """Stop the agent gracefully."""
        self.is_running = False
        self.state = AgentState.IDLE
        logger.info(f"Agent {self.name} ({self.agent_id}) stopped")
    
    async def _main_loop(self) -> None:
        """Main agent execution loop."""
        while self.is_running:
            try:
                # Process incoming messages
                await self._process_messages()
                
                # Execute pending tasks
                await self._execute_tasks()
                
                # Perform self-monitoring and adaptation
                await self._self_monitor()
                
                # Brief pause to prevent CPU spinning
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in agent {self.name} main loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _process_messages(self) -> None:
        """Process incoming messages from other agents."""
        while self.message_queue:
            message = self.message_queue.pop(0)
            try:
                await self.handle_message(message)
            except Exception as e:
                logger.error(f"Error processing message {message.id}: {e}")
    
    async def _execute_tasks(self) -> None:
        """Execute current tasks based on priority and resources."""
        if len(self.current_tasks) >= self.max_concurrent_tasks:
            return
        
        # Get pending tasks sorted by priority
        pending_tasks = [
            task for task in self.current_tasks.values() 
            if task.status == "pending"
        ]
        pending_tasks.sort(key=lambda t: t.priority, reverse=True)
        
        # Execute highest priority tasks
        for task in pending_tasks[:self.max_concurrent_tasks - len([
            t for t in self.current_tasks.values() if t.status == "running"
        ])]:
            await self._execute_single_task(task)
    
    async def _execute_single_task(self, task: AgentTask) -> None:
        """Execute a single task."""
        try:
            task.status = "running"
            task.started_at = datetime.utcnow()
            self.state = AgentState.EXECUTING
            
            # Plan the task execution
            plan = await self.plan_task(task)
            
            # Execute the plan
            result = await self.execute_plan(plan, task)
            
            # Store result and mark complete
            task.result = result
            task.status = "completed"
            task.completed_at = datetime.utcnow()
            task.progress = 1.0
            
            # Update performance metrics
            self.tasks_completed += 1
            completion_time = (task.completed_at - task.started_at).total_seconds()
            self._update_average_completion_time(completion_time)
            
            # Store experience in memory
            await self.memory.store_experience({
                "task_type": task.name,
                "parameters": task.parameters,
                "result": result,
                "completion_time": completion_time,
                "success": True
            })
            
            logger.info(f"Task {task.id} completed successfully by {self.name}")
            
        except Exception as e:
            task.status = "failed"
            task.error = str(e)
            task.completed_at = datetime.utcnow()
            self.tasks_failed += 1
            
            # Store failure experience
            await self.memory.store_experience({
                "task_type": task.name,
                "parameters": task.parameters,
                "error": str(e),
                "success": False
            })
            
            logger.error(f"Task {task.id} failed in {self.name}: {e}")
        
        finally:
            self.state = AgentState.IDLE
            self.last_activity = datetime.utcnow()
    
    def _update_average_completion_time(self, completion_time: float) -> None:
        """Update average completion time with new data point."""
        total_tasks = self.tasks_completed + self.tasks_failed
        if total_tasks == 1:
            self.average_completion_time = completion_time
        else:
            # Exponential moving average
            alpha = 0.1
            self.average_completion_time = (
                alpha * completion_time + 
                (1 - alpha) * self.average_completion_time
            )
    
    async def _self_monitor(self) -> None:
        """Self-monitoring and adaptation logic."""
        # Check if agent needs to adapt based on performance
        if self.tasks_completed > 0:
            success_rate = self.tasks_completed / (self.tasks_completed + self.tasks_failed)
            
            # If success rate is low, trigger self-improvement
            if success_rate < 0.8:
                await self.self_improve()
    
    # Abstract methods that must be implemented by specialized agents
    
    @abstractmethod
    async def plan_task(self, task: AgentTask) -> Dict[str, Any]:
        """Plan how to execute a task. Returns execution plan."""
        pass
    
    @abstractmethod
    async def execute_plan(self, plan: Dict[str, Any], task: AgentTask) -> Dict[str, Any]:
        """Execute a planned task. Returns execution result."""
        pass
    
    @abstractmethod
    async def handle_message(self, message: AgentMessage) -> None:
        """Handle incoming messages from other agents."""
        pass
    
    @abstractmethod
    async def self_improve(self) -> None:
        """Self-improvement logic based on performance feedback."""
        pass
    
    # Public interface methods
    
    async def assign_task(self, task: AgentTask) -> str:
        """Assign a new task to the agent."""
        self.current_tasks[task.id] = task
        logger.info(f"Task {task.id} assigned to agent {self.name}")
        return task.id
    
    async def send_message(self, message: AgentMessage) -> None:
        """Send a message to another agent (via message bus)."""
        # This will be handled by the AgentOrchestrator
        pass
    
    async def receive_message(self, message: AgentMessage) -> None:
        """Receive a message from another agent."""
        self.message_queue.append(message)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status and metrics."""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "state": self.state.value,
            "current_tasks": len(self.current_tasks),
            "tasks_completed": self.tasks_completed,
            "tasks_failed": self.tasks_failed,
            "success_rate": self.tasks_completed / max(1, self.tasks_completed + self.tasks_failed),
            "average_completion_time": self.average_completion_time,
            "last_activity": self.last_activity.isoformat(),
            "capabilities": self.capabilities
        }
