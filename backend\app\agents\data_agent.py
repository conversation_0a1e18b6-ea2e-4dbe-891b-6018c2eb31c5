"""
Data Agent for NeuroFlowAI
==========================

Specialized agent for data management, preprocessing, and quality assurance.
Handles data ingestion, validation, transformation, and lineage tracking.
"""

import asyncio
import json
import pandas as pd
import numpy as np
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
import hashlib
import boto3
from sqlalchemy import create_engine
import dask.dataframe as dd
from great_expectations import DataContext
import apache_beam as beam

from .base_agent import BaseAgent, AgentTask, AgentMessage
import logging

logger = logging.getLogger(__name__)


class DataSourceType(str, Enum):
    """Data source types."""
    CSV = "csv"
    JSON = "json"
    PARQUET = "parquet"
    DATABASE = "database"
    API = "api"
    STREAMING = "streaming"
    S3 = "s3"
    GCS = "gcs"
    AZURE_BLOB = "azure_blob"


class DataQualityStatus(str, Enum):
    """Data quality assessment status."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    FAILED = "failed"


@dataclass
class DataSource:
    """Data source configuration."""
    source_id: str
    name: str
    source_type: DataSourceType
    connection_config: Dict[str, Any]
    
    # Schema information
    schema: Optional[Dict[str, Any]] = None
    estimated_size_gb: float = 0.0
    record_count: Optional[int] = None
    
    # Quality metrics
    quality_score: float = 0.0
    quality_status: DataQualityStatus = DataQualityStatus.FAIR
    last_quality_check: Optional[datetime] = None
    
    # Lineage tracking
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    lineage_hash: Optional[str] = None


@dataclass
class DataPipeline:
    """Data processing pipeline configuration."""
    pipeline_id: str
    name: str
    source_ids: List[str]
    
    # Processing steps
    transformations: List[Dict[str, Any]] = field(default_factory=list)
    validation_rules: List[Dict[str, Any]] = field(default_factory=list)
    
    # Output configuration
    output_format: str = "parquet"
    output_location: str = ""
    
    # Execution settings
    batch_size: int = 10000
    parallel_workers: int = 4
    
    # Status
    status: str = "draft"
    last_run: Optional[datetime] = None
    success_rate: float = 0.0


class DataAgent(BaseAgent):
    """
    Agent responsible for comprehensive data management.
    
    Capabilities:
    - Multi-source data ingestion
    - Automated data quality assessment
    - Schema inference and validation
    - Data transformation and preprocessing
    - Lineage tracking and versioning
    - Real-time data monitoring
    - Automated data pipeline creation
    """
    
    def __init__(self, agent_id: str = "data_001", **kwargs):
        super().__init__(
            agent_id=agent_id,
            name="Data Agent",
            description="Manages data ingestion, quality, and preprocessing",
            capabilities=[
                "data_ingestion",
                "quality_assessment",
                "schema_inference",
                "data_transformation",
                "lineage_tracking",
                "pipeline_automation",
                "real_time_monitoring"
            ],
            max_concurrent_tasks=10,
            **kwargs
        )
        
        # Data management
        self.data_sources: Dict[str, DataSource] = {}
        self.data_pipelines: Dict[str, DataPipeline] = {}
        self.quality_profiles: Dict[str, Dict[str, Any]] = {}
        
        # Processing engines
        self.spark_session = None
        self.dask_client = None
        
        # Cloud storage clients
        self.s3_client = None
        self.gcs_client = None
        self.azure_client = None
        
        # Data quality framework
        self.ge_context = None
        
        # Monitoring
        self.data_metrics: Dict[str, Any] = {
            "total_sources": 0,
            "total_records_processed": 0,
            "average_quality_score": 0.0,
            "failed_pipelines": 0,
            "data_freshness_hours": 0.0
        }
    
    async def start(self) -> None:
        """Start the data agent."""
        await super().start()
        
        # Initialize processing engines
        await self._initialize_processing_engines()
        
        # Initialize cloud clients
        await self._initialize_cloud_clients()
        
        # Initialize data quality framework
        await self._initialize_quality_framework()
        
        # Start monitoring tasks
        asyncio.create_task(self._data_monitoring_loop())
        asyncio.create_task(self._quality_assessment_loop())
        
        logger.info("Data Agent started successfully")
    
    async def plan_task(self, task: AgentTask) -> Dict[str, Any]:
        """Plan how to execute a data task."""
        task_type = task.parameters.get("type", "data_ingestion")
        
        if task_type == "data_ingestion":
            return await self._plan_data_ingestion(task)
        elif task_type == "quality_assessment":
            return await self._plan_quality_assessment(task)
        elif task_type == "data_transformation":
            return await self._plan_data_transformation(task)
        elif task_type == "pipeline_creation":
            return await self._plan_pipeline_creation(task)
        else:
            raise ValueError(f"Unknown task type: {task_type}")
    
    async def _plan_data_ingestion(self, task: AgentTask) -> Dict[str, Any]:
        """Plan data ingestion task."""
        return {
            "steps": [
                "validate_data_source",
                "infer_schema",
                "assess_data_quality",
                "create_ingestion_pipeline",
                "execute_ingestion",
                "validate_results",
                "update_lineage"
            ],
            "estimated_time": 600,  # 10 minutes
            "required_resources": ["processing_engine", "storage"],
            "dependencies": ["data_source_accessible"]
        }
    
    async def _plan_quality_assessment(self, task: AgentTask) -> Dict[str, Any]:
        """Plan data quality assessment task."""
        return {
            "steps": [
                "load_data_sample",
                "run_quality_checks",
                "analyze_data_distribution",
                "detect_anomalies",
                "generate_quality_report",
                "update_quality_metrics"
            ],
            "estimated_time": 300,  # 5 minutes
            "required_resources": ["quality_framework"],
            "dependencies": ["data_available"]
        }
    
    async def execute_plan(self, plan: Dict[str, Any], task: AgentTask) -> Dict[str, Any]:
        """Execute the planned data task."""
        results = {}
        
        for step in plan["steps"]:
            try:
                step_result = await self._execute_data_step(step, task.parameters, plan)
                results[step] = step_result
                
                # Update task progress
                progress = len([s for s in plan["steps"] if s in results]) / len(plan["steps"])
                task.progress = progress
                
            except Exception as e:
                logger.error(f"Error executing step {step}: {e}")
                results[step] = {"error": str(e)}
        
        # Generate data report
        data_report = await self._generate_data_report(results, task.parameters)
        results["data_report"] = data_report
        
        return results
    
    async def _execute_data_step(
        self, 
        step: str, 
        parameters: Dict[str, Any], 
        plan: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a single data processing step."""
        
        if step == "validate_data_source":
            return await self._validate_data_source(parameters)
        elif step == "infer_schema":
            return await self._infer_schema(parameters)
        elif step == "assess_data_quality":
            return await self._assess_data_quality(parameters)
        elif step == "create_ingestion_pipeline":
            return await self._create_ingestion_pipeline(parameters)
        elif step == "execute_ingestion":
            return await self._execute_ingestion(parameters)
        elif step == "load_data_sample":
            return await self._load_data_sample(parameters)
        elif step == "run_quality_checks":
            return await self._run_quality_checks(parameters)
        elif step == "analyze_data_distribution":
            return await self._analyze_data_distribution(parameters)
        elif step == "detect_anomalies":
            return await self._detect_anomalies(parameters)
        else:
            return {"status": "completed", "message": f"Step {step} executed"}
    
    async def _validate_data_source(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data source accessibility and configuration."""
        
        source_config = parameters.get("source_config", {})
        source_type = DataSourceType(source_config.get("type", "csv"))
        
        validation_results = {
            "accessible": False,
            "estimated_size": 0,
            "record_count": 0,
            "schema_detected": False,
            "errors": []
        }
        
        try:
            if source_type == DataSourceType.CSV:
                # Validate CSV file
                file_path = source_config.get("file_path", "")
                if file_path.startswith("s3://"):
                    validation_results.update(await self._validate_s3_source(file_path))
                else:
                    validation_results.update(await self._validate_local_file(file_path))
            
            elif source_type == DataSourceType.DATABASE:
                # Validate database connection
                validation_results.update(await self._validate_database_source(source_config))
            
            elif source_type == DataSourceType.API:
                # Validate API endpoint
                validation_results.update(await self._validate_api_source(source_config))
            
            validation_results["accessible"] = len(validation_results["errors"]) == 0
            
        except Exception as e:
            validation_results["errors"].append(str(e))
        
        return validation_results
    
    async def _validate_s3_source(self, s3_path: str) -> Dict[str, Any]:
        """Validate S3 data source."""
        
        if not self.s3_client:
            return {"errors": ["S3 client not initialized"]}
        
        try:
            # Parse S3 path
            bucket, key = s3_path.replace("s3://", "").split("/", 1)
            
            # Check if object exists
            response = self.s3_client.head_object(Bucket=bucket, Key=key)
            
            return {
                "estimated_size": response.get("ContentLength", 0) / (1024**3),  # GB
                "last_modified": response.get("LastModified"),
                "errors": []
            }
            
        except Exception as e:
            return {"errors": [f"S3 validation failed: {e}"]}
    
    async def _validate_database_source(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate database data source."""
        
        try:
            # Create database connection
            connection_string = self._build_connection_string(config)
            engine = create_engine(connection_string)
            
            # Test connection
            with engine.connect() as conn:
                # Get table info
                table_name = config.get("table_name", "")
                result = conn.execute(f"SELECT COUNT(*) FROM {table_name}")
                record_count = result.scalar()
                
                return {
                    "record_count": record_count,
                    "errors": []
                }
                
        except Exception as e:
            return {"errors": [f"Database validation failed: {e}"]}
    
    async def _infer_schema(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Infer schema from data source."""
        
        source_config = parameters.get("source_config", {})
        source_type = DataSourceType(source_config.get("type", "csv"))
        
        schema_info = {
            "columns": [],
            "data_types": {},
            "nullable_columns": [],
            "primary_key_candidates": [],
            "foreign_key_candidates": []
        }
        
        try:
            if source_type == DataSourceType.CSV:
                # Load sample data
                sample_df = await self._load_csv_sample(source_config)
                schema_info.update(self._analyze_dataframe_schema(sample_df))
            
            elif source_type == DataSourceType.DATABASE:
                # Query database schema
                schema_info.update(await self._query_database_schema(source_config))
            
        except Exception as e:
            logger.error(f"Schema inference failed: {e}")
            schema_info["error"] = str(e)
        
        return schema_info
    
    def _analyze_dataframe_schema(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze pandas DataFrame schema."""
        
        schema_info = {
            "columns": list(df.columns),
            "data_types": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "nullable_columns": list(df.columns[df.isnull().any()]),
            "row_count": len(df),
            "column_count": len(df.columns)
        }
        
        # Detect potential primary keys (unique, non-null columns)
        for col in df.columns:
            if df[col].nunique() == len(df) and not df[col].isnull().any():
                schema_info.setdefault("primary_key_candidates", []).append(col)
        
        # Basic statistics for numeric columns
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            schema_info["numeric_stats"] = df[numeric_cols].describe().to_dict()
        
        return schema_info
    
    async def _assess_data_quality(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Assess data quality using multiple metrics."""
        
        source_id = parameters.get("source_id", "")
        sample_data = parameters.get("sample_data")
        
        if sample_data is None:
            # Load sample data
            sample_data = await self._load_data_sample(parameters)
            sample_data = sample_data.get("sample_df")
        
        if sample_data is None:
            return {"error": "Could not load sample data for quality assessment"}
        
        quality_metrics = {
            "completeness": self._calculate_completeness(sample_data),
            "uniqueness": self._calculate_uniqueness(sample_data),
            "validity": self._calculate_validity(sample_data),
            "consistency": self._calculate_consistency(sample_data),
            "accuracy": self._estimate_accuracy(sample_data),
            "timeliness": self._assess_timeliness(sample_data)
        }
        
        # Calculate overall quality score
        overall_score = np.mean(list(quality_metrics.values()))
        quality_status = self._determine_quality_status(overall_score)
        
        quality_assessment = {
            "overall_score": overall_score,
            "quality_status": quality_status.value,
            "metrics": quality_metrics,
            "recommendations": self._generate_quality_recommendations(quality_metrics),
            "assessment_timestamp": datetime.now().isoformat()
        }
        
        # Store quality profile
        if source_id:
            self.quality_profiles[source_id] = quality_assessment
        
        return quality_assessment
    
    def _calculate_completeness(self, df: pd.DataFrame) -> float:
        """Calculate data completeness (non-null ratio)."""
        total_cells = df.size
        non_null_cells = df.count().sum()
        return non_null_cells / total_cells if total_cells > 0 else 0.0
    
    def _calculate_uniqueness(self, df: pd.DataFrame) -> float:
        """Calculate data uniqueness (unique values ratio)."""
        uniqueness_scores = []
        
        for col in df.columns:
            total_values = len(df[col].dropna())
            unique_values = df[col].nunique()
            if total_values > 0:
                uniqueness_scores.append(unique_values / total_values)
        
        return np.mean(uniqueness_scores) if uniqueness_scores else 0.0
    
    def _calculate_validity(self, df: pd.DataFrame) -> float:
        """Calculate data validity (format compliance)."""
        validity_scores = []
        
        for col in df.columns:
            col_data = df[col].dropna()
            if len(col_data) == 0:
                continue
            
            # Check data type consistency
            if col_data.dtype == 'object':
                # For string columns, check for consistent patterns
                if col.lower() in ['email', 'e-mail']:
                    valid_emails = col_data.str.contains(r'^[^@]+@[^@]+\.[^@]+$', na=False).sum()
                    validity_scores.append(valid_emails / len(col_data))
                elif col.lower() in ['phone', 'telephone']:
                    valid_phones = col_data.str.contains(r'^\+?[\d\s\-\(\)]+$', na=False).sum()
                    validity_scores.append(valid_phones / len(col_data))
                else:
                    # General string validity (non-empty, reasonable length)
                    valid_strings = ((col_data.str.len() > 0) & (col_data.str.len() < 1000)).sum()
                    validity_scores.append(valid_strings / len(col_data))
            else:
                # For numeric columns, check for reasonable ranges
                if np.isfinite(col_data).all():
                    validity_scores.append(1.0)
                else:
                    valid_numbers = np.isfinite(col_data).sum()
                    validity_scores.append(valid_numbers / len(col_data))
        
        return np.mean(validity_scores) if validity_scores else 0.0
    
    def _calculate_consistency(self, df: pd.DataFrame) -> float:
        """Calculate data consistency across columns."""
        consistency_scores = []
        
        # Check for consistent formatting within columns
        for col in df.columns:
            if df[col].dtype == 'object':
                col_data = df[col].dropna()
                if len(col_data) > 0:
                    # Check case consistency
                    if col_data.str.islower().all() or col_data.str.isupper().all():
                        consistency_scores.append(1.0)
                    else:
                        # Mixed case - check for consistent patterns
                        title_case = col_data.str.istitle().sum()
                        consistency_scores.append(title_case / len(col_data))
        
        return np.mean(consistency_scores) if consistency_scores else 0.8  # Default good score
    
    def _estimate_accuracy(self, df: pd.DataFrame) -> float:
        """Estimate data accuracy using heuristics."""
        accuracy_indicators = []
        
        # Check for obvious data entry errors
        for col in df.columns:
            if df[col].dtype in ['int64', 'float64']:
                # Check for outliers using IQR method
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
                accuracy_indicators.append(1 - (outliers / len(df[col])))
        
        return np.mean(accuracy_indicators) if accuracy_indicators else 0.8  # Default good score
    
    def _assess_timeliness(self, df: pd.DataFrame) -> float:
        """Assess data timeliness."""
        # Look for date/timestamp columns
        date_columns = []
        for col in df.columns:
            if 'date' in col.lower() or 'time' in col.lower() or df[col].dtype == 'datetime64[ns]':
                date_columns.append(col)
        
        if not date_columns:
            return 0.8  # Default score if no date columns
        
        timeliness_scores = []
        current_time = datetime.now()
        
        for col in date_columns:
            try:
                # Convert to datetime if not already
                if df[col].dtype != 'datetime64[ns]':
                    dates = pd.to_datetime(df[col], errors='coerce')
                else:
                    dates = df[col]
                
                # Calculate how recent the data is
                latest_date = dates.max()
                if pd.notna(latest_date):
                    days_old = (current_time - latest_date).days
                    # Score based on recency (1.0 for today, decreasing over time)
                    timeliness_score = max(0.0, 1.0 - (days_old / 365))  # Decrease over a year
                    timeliness_scores.append(timeliness_score)
                    
            except Exception:
                continue
        
        return np.mean(timeliness_scores) if timeliness_scores else 0.8
    
    def _determine_quality_status(self, score: float) -> DataQualityStatus:
        """Determine quality status based on score."""
        if score >= 0.9:
            return DataQualityStatus.EXCELLENT
        elif score >= 0.8:
            return DataQualityStatus.GOOD
        elif score >= 0.6:
            return DataQualityStatus.FAIR
        elif score >= 0.4:
            return DataQualityStatus.POOR
        else:
            return DataQualityStatus.FAILED
    
    def _generate_quality_recommendations(self, metrics: Dict[str, float]) -> List[str]:
        """Generate recommendations based on quality metrics."""
        recommendations = []
        
        if metrics.get("completeness", 1.0) < 0.8:
            recommendations.append("Address missing data through imputation or collection")
        
        if metrics.get("uniqueness", 1.0) < 0.7:
            recommendations.append("Investigate and remove duplicate records")
        
        if metrics.get("validity", 1.0) < 0.8:
            recommendations.append("Implement data validation rules and format standardization")
        
        if metrics.get("consistency", 1.0) < 0.8:
            recommendations.append("Standardize data formats and naming conventions")
        
        if metrics.get("accuracy", 1.0) < 0.8:
            recommendations.append("Review data entry processes and implement quality controls")
        
        if metrics.get("timeliness", 1.0) < 0.6:
            recommendations.append("Establish more frequent data refresh cycles")
        
        if not recommendations:
            recommendations.append("Data quality is good - maintain current processes")
        
        return recommendations
