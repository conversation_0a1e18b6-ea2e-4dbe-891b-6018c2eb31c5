"""
DevOps Agent for NeuroFlowAI
============================

Specialized agent for deployment, scaling, and infrastructure management.
Handles model deployment, API generation, scaling, and rollback operations.
"""

import asyncio
import json
from typing import Any, Dict, List
from datetime import datetime, timed<PERSON>ta
from enum import Enum

from .base_agent import BaseAgent, AgentTask, AgentMessage
import logging

logger = logging.getLogger(__name__)


class DeploymentStatus(str, Enum):
    """Deployment status enumeration."""
    PENDING = "pending"
    DEPLOYING = "deploying"
    DEPLOYED = "deployed"
    SCALING = "scaling"
    UPDATING = "updating"
    ROLLING_BACK = "rolling_back"
    FAILED = "failed"
    TERMINATED = "terminated"


class ScalingStrategy(str, Enum):
    """Auto-scaling strategies."""
    FIXED = "fixed"
    AUTO_CPU = "auto_cpu"
    AUTO_MEMORY = "auto_memory"
    AUTO_REQUESTS = "auto_requests"
    PREDICTIVE = "predictive"


class DevOpsAgent(BaseAgent):
    """
    Agent responsible for deployment and infrastructure management.
    
    Capabilities:
    - Model deployment and API generation
    - Auto-scaling and load balancing
    - Blue-green and canary deployments
    - Rollback and disaster recovery
    - Infrastructure monitoring
    - Cost optimization
    """
    
    def __init__(self, agent_id: str = "devops_001", **kwargs):
        super().__init__(
            agent_id=agent_id,
            name="DevOps Agent",
            description="Manages deployment, scaling, and infrastructure operations",
            capabilities=[
                "model_deployment",
                "api_generation",
                "auto_scaling",
                "load_balancing",
                "blue_green_deployment",
                "canary_deployment",
                "rollback_management",
                "infrastructure_monitoring",
                "cost_optimization"
            ],
            max_concurrent_tasks=8,
            **kwargs
        )
        
        # Deployment configurations
        self.deployment_environments = {
            "development": {
                "replicas": 1,
                "cpu_limit": "500m",
                "memory_limit": "1Gi",
                "auto_scaling": False
            },
            "staging": {
                "replicas": 2,
                "cpu_limit": "1000m", 
                "memory_limit": "2Gi",
                "auto_scaling": True,
                "min_replicas": 1,
                "max_replicas": 5
            },
            "production": {
                "replicas": 3,
                "cpu_limit": "2000m",
                "memory_limit": "4Gi", 
                "auto_scaling": True,
                "min_replicas": 2,
                "max_replicas": 20
            }
        }
        
        # Active deployments tracking
        self.active_deployments = {}
        self.deployment_history = []
        
        # Infrastructure metrics
        self.infrastructure_metrics = {
            "total_deployments": 0,
            "successful_deployments": 0,
            "failed_deployments": 0,
            "average_deployment_time": 0.0,
            "total_uptime": 0.0,
            "cost_per_deployment": 0.0
        }
    
    async def plan_task(self, task: AgentTask) -> Dict[str, Any]:
        """Plan how to execute a DevOps task."""
        task_type = task.parameters.get("type", "model_deployment")
        
        if task_type == "model_deployment":
            return await self._plan_model_deployment(task)
        elif task_type == "api_generation":
            return await self._plan_api_generation(task)
        elif task_type == "scaling_operation":
            return await self._plan_scaling_operation(task)
        elif task_type == "rollback_operation":
            return await self._plan_rollback_operation(task)
        else:
            raise ValueError(f"Unknown task type: {task_type}")
    
    async def _plan_model_deployment(self, task: AgentTask) -> Dict[str, Any]:
        """Plan model deployment task."""
        environment = task.parameters.get("environment", "staging")
        deployment_strategy = task.parameters.get("strategy", "rolling")
        
        return {
            "steps": [
                "validate_model_artifacts",
                "generate_deployment_config",
                "create_api_endpoints",
                "setup_monitoring",
                "deploy_to_kubernetes",
                "configure_load_balancer",
                "run_health_checks",
                "update_service_registry"
            ],
            "estimated_time": 600,  # 10 minutes
            "required_resources": ["kubernetes", "docker_registry", "load_balancer"],
            "environment": environment,
            "strategy": deployment_strategy,
            "dependencies": ["model_training_completed"]
        }
    
    async def _plan_api_generation(self, task: AgentTask) -> Dict[str, Any]:
        """Plan API generation task."""
        return {
            "steps": [
                "analyze_model_interface",
                "generate_api_schema",
                "create_fastapi_endpoints",
                "setup_authentication",
                "configure_rate_limiting",
                "generate_documentation",
                "setup_monitoring_endpoints"
            ],
            "estimated_time": 300,  # 5 minutes
            "required_resources": ["api_gateway", "documentation_service"],
            "dependencies": ["model_validation_passed"]
        }
    
    async def execute_plan(self, plan: Dict[str, Any], task: AgentTask) -> Dict[str, Any]:
        """Execute the planned DevOps task."""
        deployment_id = f"deploy_{task.id}_{int(datetime.now().timestamp())}"
        
        # Create deployment record
        deployment = {
            "deployment_id": deployment_id,
            "task_id": task.id,
            "status": DeploymentStatus.PENDING,
            "environment": plan.get("environment", "staging"),
            "strategy": plan.get("strategy", "rolling"),
            "start_time": datetime.now(),
            "end_time": None,
            "progress": 0.0,
            "health_status": "unknown",
            "endpoints": [],
            "metrics": {},
            "logs": []
        }
        
        self.active_deployments[deployment_id] = deployment
        
        try:
            results = {}
            
            for step in plan["steps"]:
                step_result = await self._execute_deployment_step(
                    step, task.parameters, plan, deployment
                )
                results[step] = step_result
                
                # Update progress
                progress = len([s for s in plan["steps"] if s in results]) / len(plan["steps"])
                task.progress = progress
                deployment["progress"] = progress
                
                # Log step completion
                deployment["logs"].append({
                    "timestamp": datetime.now().isoformat(),
                    "step": step,
                    "status": "completed",
                    "details": step_result
                })
            
            # Mark deployment as successful
            deployment["status"] = DeploymentStatus.DEPLOYED
            deployment["end_time"] = datetime.now()
            deployment["health_status"] = "healthy"
            
            # Generate deployment report
            deployment_report = await self._generate_deployment_report(deployment, results)
            results["deployment_report"] = deployment_report
            
            # Update metrics
            await self._update_deployment_metrics(deployment)
            
            return results
            
        except Exception as e:
            deployment["status"] = DeploymentStatus.FAILED
            deployment["error"] = str(e)
            deployment["end_time"] = datetime.now()
            logger.error(f"Deployment {deployment_id} failed: {e}")
            raise
        
        finally:
            # Move to deployment history
            self.deployment_history.append(deployment)
    
    async def _execute_deployment_step(
        self,
        step: str,
        parameters: Dict[str, Any],
        plan: Dict[str, Any],
        deployment: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a single deployment step."""
        
        if step == "validate_model_artifacts":
            return await self._validate_model_artifacts(parameters, deployment)
        elif step == "generate_deployment_config":
            return await self._generate_deployment_config(parameters, plan, deployment)
        elif step == "create_api_endpoints":
            return await self._create_api_endpoints(parameters, deployment)
        elif step == "deploy_to_kubernetes":
            return await self._deploy_to_kubernetes(deployment)
        elif step == "configure_load_balancer":
            return await self._configure_load_balancer(deployment)
        elif step == "run_health_checks":
            return await self._run_health_checks(deployment)
        else:
            return {"status": "completed", "message": f"Step {step} executed"}
    
    async def _validate_model_artifacts(
        self,
        parameters: Dict[str, Any],
        deployment: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate model artifacts before deployment."""
        
        model_path = parameters.get("model_path", "")
        
        validation_results = {
            "model_file_exists": True,  # Simulate validation
            "model_size_mb": 250.5,
            "model_format": "pytorch",
            "dependencies_satisfied": True,
            "security_scan_passed": True,
            "performance_benchmarks": {
                "inference_time_ms": 45.2,
                "memory_usage_mb": 512,
                "throughput_rps": 100
            }
        }
        
        deployment["model_info"] = validation_results
        
        return validation_results
    
    async def _generate_deployment_config(
        self,
        parameters: Dict[str, Any],
        plan: Dict[str, Any],
        deployment: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate Kubernetes deployment configuration."""
        
        environment = plan.get("environment", "staging")
        env_config = self.deployment_environments[environment]
        
        deployment_config = {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": {
                "name": f"model-{deployment['deployment_id']}",
                "namespace": f"neuroflow-{environment}",
                "labels": {
                    "app": "neuroflow-model",
                    "deployment_id": deployment["deployment_id"],
                    "environment": environment
                }
            },
            "spec": {
                "replicas": env_config["replicas"],
                "selector": {
                    "matchLabels": {
                        "app": "neuroflow-model",
                        "deployment_id": deployment["deployment_id"]
                    }
                },
                "template": {
                    "spec": {
                        "containers": [{
                            "name": "model-server",
                            "image": f"neuroflow/model-server:{deployment['deployment_id']}",
                            "ports": [{"containerPort": 8000}],
                            "resources": {
                                "limits": {
                                    "cpu": env_config["cpu_limit"],
                                    "memory": env_config["memory_limit"]
                                },
                                "requests": {
                                    "cpu": str(int(env_config["cpu_limit"].rstrip('m')) // 2) + 'm',
                                    "memory": str(int(env_config["memory_limit"].rstrip('Gi')) // 2) + 'Gi'
                                }
                            },
                            "env": [
                                {"name": "MODEL_PATH", "value": "/models/model.pt"},
                                {"name": "ENVIRONMENT", "value": environment}
                            ]
                        }]
                    }
                }
            }
        }
        
        deployment["kubernetes_config"] = deployment_config
        
        return {"config_generated": True, "config": deployment_config}
    
    async def _create_api_endpoints(
        self,
        parameters: Dict[str, Any],
        deployment: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create API endpoints for the deployed model."""
        
        model_type = parameters.get("model_type", "classification")
        
        # Generate API endpoints based on model type
        endpoints = {
            "predict": {
                "path": f"/api/v1/models/{deployment['deployment_id']}/predict",
                "method": "POST",
                "description": "Make predictions using the deployed model"
            },
            "health": {
                "path": f"/api/v1/models/{deployment['deployment_id']}/health",
                "method": "GET", 
                "description": "Check model health status"
            },
            "metrics": {
                "path": f"/api/v1/models/{deployment['deployment_id']}/metrics",
                "method": "GET",
                "description": "Get model performance metrics"
            }
        }
        
        if model_type == "generative":
            endpoints["generate"] = {
                "path": f"/api/v1/models/{deployment['deployment_id']}/generate",
                "method": "POST",
                "description": "Generate content using the model"
            }
        
        deployment["endpoints"] = endpoints
        
        return {"endpoints_created": True, "endpoints": endpoints}

    async def _deploy_to_kubernetes(self, deployment: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy the model to Kubernetes cluster."""

        # Simulate Kubernetes deployment
        await asyncio.sleep(0.2)  # Simulate deployment time

        deployment_result = {
            "deployment_created": True,
            "service_created": True,
            "pods_running": 3,
            "cluster_ip": "**********",
            "external_ip": "************",
            "namespace": f"neuroflow-{deployment['environment']}",
            "deployment_name": f"model-{deployment['deployment_id']}"
        }

        deployment["kubernetes_deployment"] = deployment_result
        deployment["status"] = DeploymentStatus.DEPLOYING

        return deployment_result

    async def _configure_load_balancer(self, deployment: Dict[str, Any]) -> Dict[str, Any]:
        """Configure load balancer for the deployment."""

        load_balancer_config = {
            "type": "Application Load Balancer",
            "dns_name": f"model-{deployment['deployment_id']}.neuroflow.ai",
            "health_check_path": "/health",
            "health_check_interval": 30,
            "target_groups": [
                {
                    "name": f"model-{deployment['deployment_id']}-tg",
                    "port": 8000,
                    "protocol": "HTTP",
                    "health_check_enabled": True
                }
            ],
            "ssl_certificate": "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"
        }

        deployment["load_balancer"] = load_balancer_config

        return {"load_balancer_configured": True, "config": load_balancer_config}

    async def _run_health_checks(self, deployment: Dict[str, Any]) -> Dict[str, Any]:
        """Run comprehensive health checks on the deployment."""

        health_checks = {
            "endpoint_health": {
                "status": "healthy",
                "response_time_ms": 25.3,
                "status_code": 200
            },
            "resource_usage": {
                "cpu_usage_percent": 15.2,
                "memory_usage_percent": 45.8,
                "disk_usage_percent": 12.1
            },
            "model_performance": {
                "inference_latency_ms": 42.1,
                "throughput_rps": 95.3,
                "error_rate_percent": 0.1
            },
            "dependencies": {
                "database_connection": "healthy",
                "external_apis": "healthy",
                "storage_access": "healthy"
            }
        }

        # Overall health status
        overall_health = "healthy"
        if health_checks["resource_usage"]["cpu_usage_percent"] > 80:
            overall_health = "warning"
        if health_checks["model_performance"]["error_rate_percent"] > 5:
            overall_health = "unhealthy"

        deployment["health_status"] = overall_health
        deployment["health_checks"] = health_checks

        return {"health_status": overall_health, "checks": health_checks}

    async def handle_message(self, message: AgentMessage) -> None:
        """Handle incoming messages from other agents."""
        if message.message_type.value == "task_request":
            # Convert message to deployment task
            task = AgentTask(
                name=message.content.get("task_name", "model_deployment"),
                description=message.content.get("description", ""),
                parameters=message.content.get("parameters", {}),
                priority=message.priority
            )
            await self.assign_task(task)

        elif message.message_type.value == "alert":
            # Handle infrastructure alerts
            await self._handle_infrastructure_alert(message)

    async def self_improve(self) -> None:
        """Self-improvement based on deployment performance and reliability."""
        if len(self.deployment_history) < 5:
            return

        # Analyze recent deployments
        recent_deployments = self.deployment_history[-20:]

        # Calculate success rate
        successful = [d for d in recent_deployments if d["status"] == DeploymentStatus.DEPLOYED]
        success_rate = len(successful) / len(recent_deployments)

        # Calculate average deployment time
        deployment_times = []
        for deployment in successful:
            if deployment.get("start_time") and deployment.get("end_time"):
                duration = (deployment["end_time"] - deployment["start_time"]).total_seconds()
                deployment_times.append(duration)

        avg_deployment_time = sum(deployment_times) / len(deployment_times) if deployment_times else 0

        # Adjust strategies based on performance
        if success_rate < 0.9:
            # Increase validation steps for better reliability
            self.memory.preferences["validation_strictness"] = "high"
            logger.info(f"DevOps agent {self.agent_id} increased validation strictness")

        if avg_deployment_time > 900:  # More than 15 minutes
            # Optimize deployment process
            self.memory.preferences["deployment_optimization"] = "aggressive"
            logger.info(f"DevOps agent {self.agent_id} enabled aggressive deployment optimization")

    # Helper methods for deployment management

    async def _generate_deployment_report(
        self,
        deployment: Dict[str, Any],
        results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comprehensive deployment report."""

        duration = (deployment["end_time"] - deployment["start_time"]).total_seconds()

        report = {
            "deployment_summary": {
                "deployment_id": deployment["deployment_id"],
                "status": deployment["status"].value,
                "environment": deployment["environment"],
                "duration_seconds": duration,
                "health_status": deployment["health_status"]
            },
            "infrastructure": {
                "kubernetes_deployment": deployment.get("kubernetes_deployment", {}),
                "load_balancer": deployment.get("load_balancer", {}),
                "endpoints": deployment.get("endpoints", {})
            },
            "performance_metrics": {
                "health_checks": deployment.get("health_checks", {}),
                "resource_utilization": self._calculate_resource_utilization(deployment),
                "cost_estimate": self._estimate_deployment_cost(deployment)
            },
            "recommendations": {
                "scaling_suggestions": self._get_scaling_suggestions(deployment),
                "optimization_opportunities": self._identify_optimizations(deployment),
                "monitoring_alerts": self._setup_monitoring_alerts(deployment)
            }
        }

        return report

    def _calculate_resource_utilization(self, deployment: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate resource utilization for the deployment."""
        health_checks = deployment.get("health_checks", {})
        resource_usage = health_checks.get("resource_usage", {})

        return {
            "cpu_efficiency": 100 - resource_usage.get("cpu_usage_percent", 0),
            "memory_efficiency": 100 - resource_usage.get("memory_usage_percent", 0),
            "overall_efficiency": (
                (100 - resource_usage.get("cpu_usage_percent", 0)) +
                (100 - resource_usage.get("memory_usage_percent", 0))
            ) / 2
        }

    def _estimate_deployment_cost(self, deployment: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate the cost of running the deployment."""
        environment = deployment["environment"]
        env_config = self.deployment_environments[environment]

        # Simplified cost calculation
        cpu_cost_per_hour = 0.05  # $0.05 per CPU hour
        memory_cost_per_gb_hour = 0.01  # $0.01 per GB hour

        cpu_cores = float(env_config["cpu_limit"].rstrip('m')) / 1000
        memory_gb = float(env_config["memory_limit"].rstrip('Gi'))
        replicas = env_config["replicas"]

        hourly_cost = (cpu_cores * cpu_cost_per_hour + memory_gb * memory_cost_per_gb_hour) * replicas

        return {
            "hourly_cost_usd": hourly_cost,
            "daily_cost_usd": hourly_cost * 24,
            "monthly_cost_usd": hourly_cost * 24 * 30,
            "cost_breakdown": {
                "cpu_cost": cpu_cores * cpu_cost_per_hour * replicas,
                "memory_cost": memory_gb * memory_cost_per_gb_hour * replicas,
                "load_balancer_cost": 0.025,  # Fixed cost
                "storage_cost": 0.10  # Estimated storage cost
            }
        }

    def _get_scaling_suggestions(self, deployment: Dict[str, Any]) -> List[str]:
        """Get scaling suggestions based on deployment metrics."""
        suggestions = []

        health_checks = deployment.get("health_checks", {})
        resource_usage = health_checks.get("resource_usage", {})
        performance = health_checks.get("model_performance", {})

        cpu_usage = resource_usage.get("cpu_usage_percent", 0)
        memory_usage = resource_usage.get("memory_usage_percent", 0)
        response_time = performance.get("inference_latency_ms", 0)

        if cpu_usage > 70:
            suggestions.append("Consider horizontal scaling to reduce CPU load")

        if memory_usage > 80:
            suggestions.append("Consider increasing memory limits or scaling horizontally")

        if response_time > 100:
            suggestions.append("Consider adding more replicas to improve response times")

        if cpu_usage < 20 and memory_usage < 30:
            suggestions.append("Consider reducing resource allocation to optimize costs")

        return suggestions

    def _identify_optimizations(self, deployment: Dict[str, Any]) -> List[str]:
        """Identify optimization opportunities."""
        optimizations = []

        environment = deployment["environment"]

        if environment == "production":
            optimizations.append("Enable auto-scaling for production workloads")
            optimizations.append("Implement blue-green deployment for zero-downtime updates")

        optimizations.append("Set up comprehensive monitoring and alerting")
        optimizations.append("Implement request caching to improve performance")

        return optimizations

    def _setup_monitoring_alerts(self, deployment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Setup monitoring alerts for the deployment."""
        alerts = [
            {
                "name": "High CPU Usage",
                "condition": "cpu_usage > 80%",
                "severity": "warning",
                "action": "scale_horizontally"
            },
            {
                "name": "High Memory Usage",
                "condition": "memory_usage > 85%",
                "severity": "warning",
                "action": "scale_vertically"
            },
            {
                "name": "High Error Rate",
                "condition": "error_rate > 5%",
                "severity": "critical",
                "action": "investigate_and_rollback"
            },
            {
                "name": "Slow Response Time",
                "condition": "response_time > 200ms",
                "severity": "warning",
                "action": "check_performance"
            }
        ]

        return alerts

    async def _update_deployment_metrics(self, deployment: Dict[str, Any]) -> None:
        """Update global deployment metrics."""
        self.infrastructure_metrics["total_deployments"] += 1

        if deployment["status"] == DeploymentStatus.DEPLOYED:
            self.infrastructure_metrics["successful_deployments"] += 1
        else:
            self.infrastructure_metrics["failed_deployments"] += 1

        # Update average deployment time
        if deployment.get("start_time") and deployment.get("end_time"):
            duration = (deployment["end_time"] - deployment["start_time"]).total_seconds()
            total_deployments = self.infrastructure_metrics["total_deployments"]
            current_avg = self.infrastructure_metrics["average_deployment_time"]

            self.infrastructure_metrics["average_deployment_time"] = (
                (current_avg * (total_deployments - 1) + duration) / total_deployments
            )

    async def _handle_infrastructure_alert(self, message: AgentMessage) -> None:
        """Handle infrastructure alerts and take appropriate action."""
        alert_type = message.content.get("alert_type")
        deployment_id = message.content.get("deployment_id")

        if alert_type == "high_cpu_usage":
            await self._handle_high_resource_usage(deployment_id, "cpu")
        elif alert_type == "high_memory_usage":
            await self._handle_high_resource_usage(deployment_id, "memory")
        elif alert_type == "high_error_rate":
            await self._handle_high_error_rate(deployment_id)

        logger.info(f"Handled infrastructure alert: {alert_type} for deployment {deployment_id}")

    async def _handle_high_resource_usage(self, deployment_id: str, resource_type: str) -> None:
        """Handle high resource usage by scaling."""
        if deployment_id in self.active_deployments:
            deployment = self.active_deployments[deployment_id]

            # Trigger horizontal scaling
            current_replicas = deployment.get("kubernetes_deployment", {}).get("pods_running", 1)
            new_replicas = min(current_replicas + 1, 10)  # Max 10 replicas

            logger.info(f"Scaling deployment {deployment_id} from {current_replicas} to {new_replicas} replicas")

            # Update deployment record
            deployment["kubernetes_deployment"]["pods_running"] = new_replicas
            deployment["status"] = DeploymentStatus.SCALING

    async def _handle_high_error_rate(self, deployment_id: str) -> None:
        """Handle high error rate by investigating and potentially rolling back."""
        if deployment_id in self.active_deployments:
            deployment = self.active_deployments[deployment_id]

            logger.warning(f"High error rate detected for deployment {deployment_id}, investigating...")

            # Mark for investigation
            deployment["health_status"] = "investigating"
            deployment["status"] = DeploymentStatus.ROLLING_BACK
