"""
Sentinel Agent for NeuroFlowAI
==============================

Specialized agent for monitoring, drift detection, and security.
Continuously monitors model performance and system health.
"""

import asyncio
import json
import numpy as np
from typing import Any, Dict, List, Optional
from datetime import datetime, timed<PERSON>ta
from enum import Enum

from .base_agent import BaseAgent, AgentTask, AgentMessage
import logging

logger = logging.getLogger(__name__)


class AlertSeverity(str, Enum):
    """Alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class MonitoringMetric(str, Enum):
    """Types of monitoring metrics."""
    MODEL_ACCURACY = "model_accuracy"
    DATA_DRIFT = "data_drift"
    CONCEPT_DRIFT = "concept_drift"
    LATENCY = "latency"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    RESOURCE_USAGE = "resource_usage"
    SECURITY_THREAT = "security_threat"


class SentinelAgent(BaseAgent):
    """
    Agent responsible for monitoring and security.
    
    Capabilities:
    - Model performance monitoring
    - Data and concept drift detection
    - Security threat detection
    - Anomaly detection
    - Alert generation and escalation
    - Automated remediation
    """
    
    def __init__(self, agent_id: str = "sentinel_001", **kwargs):
        super().__init__(
            agent_id=agent_id,
            name="Sentinel Agent",
            description="Monitors model performance, detects drift, and ensures security",
            capabilities=[
                "performance_monitoring",
                "drift_detection",
                "anomaly_detection",
                "security_monitoring",
                "alert_management",
                "automated_remediation",
                "compliance_checking"
            ],
            max_concurrent_tasks=15,  # Can handle many monitoring tasks
            **kwargs
        )
        
        # Monitoring configuration
        self.monitoring_intervals = {
            MonitoringMetric.MODEL_ACCURACY: 300,  # 5 minutes
            MonitoringMetric.DATA_DRIFT: 600,      # 10 minutes
            MonitoringMetric.CONCEPT_DRIFT: 1800,  # 30 minutes
            MonitoringMetric.LATENCY: 60,          # 1 minute
            MonitoringMetric.THROUGHPUT: 60,       # 1 minute
            MonitoringMetric.ERROR_RATE: 120,      # 2 minutes
            MonitoringMetric.RESOURCE_USAGE: 30,   # 30 seconds
            MonitoringMetric.SECURITY_THREAT: 60,  # 1 minute
        }
        
        # Alert thresholds
        self.alert_thresholds = {
            MonitoringMetric.MODEL_ACCURACY: {"warning": 0.05, "critical": 0.10},  # % drop
            MonitoringMetric.DATA_DRIFT: {"warning": 0.1, "critical": 0.2},        # drift score
            MonitoringMetric.CONCEPT_DRIFT: {"warning": 0.15, "critical": 0.25},   # drift score
            MonitoringMetric.LATENCY: {"warning": 200, "critical": 500},           # ms
            MonitoringMetric.THROUGHPUT: {"warning": 0.2, "critical": 0.5},        # % drop
            MonitoringMetric.ERROR_RATE: {"warning": 0.05, "critical": 0.10},      # error rate
            MonitoringMetric.RESOURCE_USAGE: {"warning": 0.8, "critical": 0.95},   # utilization
        }
        
        # Monitoring state
        self.active_monitors: Dict[str, Dict[str, Any]] = {}
        self.metric_history: Dict[str, List[Dict[str, Any]]] = {}
        self.active_alerts: List[Dict[str, Any]] = []
        self.alert_history: List[Dict[str, Any]] = []
        
        # Baseline models for drift detection
        self.baseline_models: Dict[str, Any] = {}
        
        # Security monitoring
        self.security_rules: List[Dict[str, Any]] = [
            {
                "rule_id": "suspicious_requests",
                "description": "Detect suspicious request patterns",
                "pattern": "high_frequency_from_single_ip",
                "threshold": 100,  # requests per minute
                "severity": AlertSeverity.MEDIUM
            },
            {
                "rule_id": "data_exfiltration",
                "description": "Detect potential data exfiltration",
                "pattern": "large_response_sizes",
                "threshold": 10485760,  # 10MB
                "severity": AlertSeverity.HIGH
            },
            {
                "rule_id": "model_poisoning",
                "description": "Detect potential model poisoning attacks",
                "pattern": "adversarial_inputs",
                "threshold": 0.8,  # confidence score
                "severity": AlertSeverity.CRITICAL
            }
        ]
    
    async def plan_task(self, task: AgentTask) -> Dict[str, Any]:
        """Plan how to execute a monitoring task."""
        task_type = task.parameters.get("type", "performance_monitoring")
        
        if task_type == "performance_monitoring":
            return await self._plan_performance_monitoring(task)
        elif task_type == "drift_detection":
            return await self._plan_drift_detection(task)
        elif task_type == "security_monitoring":
            return await self._plan_security_monitoring(task)
        elif task_type == "anomaly_detection":
            return await self._plan_anomaly_detection(task)
        else:
            raise ValueError(f"Unknown task type: {task_type}")
    
    async def _plan_performance_monitoring(self, task: AgentTask) -> Dict[str, Any]:
        """Plan performance monitoring task."""
        return {
            "steps": [
                "collect_performance_metrics",
                "analyze_metric_trends",
                "detect_performance_anomalies",
                "generate_performance_alerts",
                "update_baselines"
            ],
            "estimated_time": 180,  # 3 minutes
            "required_resources": ["monitoring_data", "baseline_models"],
            "dependencies": []
        }
    
    async def _plan_drift_detection(self, task: AgentTask) -> Dict[str, Any]:
        """Plan drift detection task."""
        return {
            "steps": [
                "collect_recent_data",
                "compare_with_baseline",
                "calculate_drift_scores",
                "analyze_drift_patterns",
                "generate_drift_alerts",
                "recommend_retraining"
            ],
            "estimated_time": 600,  # 10 minutes
            "required_resources": ["baseline_data", "statistical_models"],
            "dependencies": ["baseline_established"]
        }
    
    async def execute_plan(self, plan: Dict[str, Any], task: AgentTask) -> Dict[str, Any]:
        """Execute the planned monitoring task."""
        results = {}
        
        for step in plan["steps"]:
            try:
                step_result = await self._execute_monitoring_step(step, task.parameters, plan)
                results[step] = step_result
                
                # Update task progress
                progress = len([s for s in plan["steps"] if s in results]) / len(plan["steps"])
                task.progress = progress
                
            except Exception as e:
                logger.error(f"Error executing step {step}: {e}")
                results[step] = {"error": str(e)}
        
        # Generate monitoring report
        monitoring_report = await self._generate_monitoring_report(results, task.parameters)
        results["monitoring_report"] = monitoring_report
        
        return results
    
    async def _execute_monitoring_step(
        self, 
        step: str, 
        parameters: Dict[str, Any], 
        plan: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a single monitoring step."""
        
        if step == "collect_performance_metrics":
            return await self._collect_performance_metrics(parameters)
        elif step == "analyze_metric_trends":
            return await self._analyze_metric_trends(parameters)
        elif step == "detect_performance_anomalies":
            return await self._detect_performance_anomalies(parameters)
        elif step == "generate_performance_alerts":
            return await self._generate_performance_alerts(parameters)
        elif step == "collect_recent_data":
            return await self._collect_recent_data(parameters)
        elif step == "compare_with_baseline":
            return await self._compare_with_baseline(parameters)
        elif step == "calculate_drift_scores":
            return await self._calculate_drift_scores(parameters)
        elif step == "generate_drift_alerts":
            return await self._generate_drift_alerts(parameters)
        else:
            return {"status": "completed", "message": f"Step {step} executed"}
    
    async def _collect_performance_metrics(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Collect current performance metrics."""
        
        model_id = parameters.get("model_id", "default")
        
        # Simulate collecting metrics from various sources
        metrics = {
            "accuracy": np.random.uniform(0.85, 0.95),
            "precision": np.random.uniform(0.80, 0.90),
            "recall": np.random.uniform(0.75, 0.85),
            "f1_score": np.random.uniform(0.78, 0.88),
            "latency_ms": np.random.uniform(45, 85),
            "throughput_rps": np.random.uniform(80, 120),
            "error_rate": np.random.uniform(0.01, 0.05),
            "cpu_usage": np.random.uniform(0.3, 0.7),
            "memory_usage": np.random.uniform(0.4, 0.8),
            "gpu_usage": np.random.uniform(0.6, 0.9),
        }
        
        # Store metrics in history
        timestamp = datetime.now()
        metric_entry = {
            "timestamp": timestamp,
            "model_id": model_id,
            "metrics": metrics
        }
        
        if model_id not in self.metric_history:
            self.metric_history[model_id] = []
        
        self.metric_history[model_id].append(metric_entry)
        
        # Keep only last 1000 entries
        if len(self.metric_history[model_id]) > 1000:
            self.metric_history[model_id] = self.metric_history[model_id][-1000:]
        
        return {
            "metrics_collected": len(metrics),
            "current_metrics": metrics,
            "collection_timestamp": timestamp.isoformat()
        }
    
    async def _analyze_metric_trends(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze trends in performance metrics."""
        
        model_id = parameters.get("model_id", "default")
        
        if model_id not in self.metric_history or len(self.metric_history[model_id]) < 2:
            return {"trend_analysis": "insufficient_data"}
        
        recent_metrics = self.metric_history[model_id][-10:]  # Last 10 entries
        
        trends = {}
        for metric_name in ["accuracy", "latency_ms", "throughput_rps", "error_rate"]:
            values = [entry["metrics"].get(metric_name, 0) for entry in recent_metrics]
            
            if len(values) >= 2:
                # Simple trend calculation
                trend_slope = (values[-1] - values[0]) / len(values)
                trend_direction = "increasing" if trend_slope > 0 else "decreasing" if trend_slope < 0 else "stable"
                
                trends[metric_name] = {
                    "direction": trend_direction,
                    "slope": trend_slope,
                    "current_value": values[-1],
                    "change_from_start": values[-1] - values[0]
                }
        
        return {"trends": trends, "analysis_period": len(recent_metrics)}
    
    async def _detect_performance_anomalies(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Detect anomalies in performance metrics."""
        
        model_id = parameters.get("model_id", "default")
        
        if model_id not in self.metric_history:
            return {"anomalies": []}
        
        recent_metrics = self.metric_history[model_id][-50:]  # Last 50 entries
        anomalies = []
        
        for metric_name, thresholds in self.alert_thresholds.items():
            if metric_name.value in ["model_accuracy", "latency", "throughput", "error_rate"]:
                values = []
                for entry in recent_metrics:
                    if metric_name == MonitoringMetric.MODEL_ACCURACY:
                        values.append(entry["metrics"].get("accuracy", 0))
                    elif metric_name == MonitoringMetric.LATENCY:
                        values.append(entry["metrics"].get("latency_ms", 0))
                    elif metric_name == MonitoringMetric.THROUGHPUT:
                        values.append(entry["metrics"].get("throughput_rps", 0))
                    elif metric_name == MonitoringMetric.ERROR_RATE:
                        values.append(entry["metrics"].get("error_rate", 0))
                
                if len(values) >= 5:
                    # Calculate baseline (mean of first 80% of values)
                    baseline_size = int(len(values) * 0.8)
                    baseline = np.mean(values[:baseline_size])
                    current_value = values[-1]
                    
                    # Check for anomalies
                    if metric_name == MonitoringMetric.MODEL_ACCURACY:
                        # For accuracy, check for drops
                        drop_percentage = (baseline - current_value) / baseline
                        if drop_percentage > thresholds["critical"]:
                            anomalies.append({
                                "metric": metric_name.value,
                                "severity": AlertSeverity.CRITICAL,
                                "description": f"Accuracy dropped by {drop_percentage:.2%}",
                                "current_value": current_value,
                                "baseline_value": baseline
                            })
                        elif drop_percentage > thresholds["warning"]:
                            anomalies.append({
                                "metric": metric_name.value,
                                "severity": AlertSeverity.MEDIUM,
                                "description": f"Accuracy dropped by {drop_percentage:.2%}",
                                "current_value": current_value,
                                "baseline_value": baseline
                            })
                    
                    elif metric_name == MonitoringMetric.LATENCY:
                        # For latency, check for increases
                        if current_value > thresholds["critical"]:
                            anomalies.append({
                                "metric": metric_name.value,
                                "severity": AlertSeverity.CRITICAL,
                                "description": f"Latency exceeded {thresholds['critical']}ms",
                                "current_value": current_value,
                                "threshold": thresholds["critical"]
                            })
                        elif current_value > thresholds["warning"]:
                            anomalies.append({
                                "metric": metric_name.value,
                                "severity": AlertSeverity.MEDIUM,
                                "description": f"Latency exceeded {thresholds['warning']}ms",
                                "current_value": current_value,
                                "threshold": thresholds["warning"]
                            })
        
        return {"anomalies": anomalies, "total_anomalies": len(anomalies)}
    
    async def _generate_performance_alerts(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate alerts based on detected anomalies."""
        
        # Get anomalies from previous step
        anomalies = parameters.get("anomalies", [])
        
        alerts_generated = []
        
        for anomaly in anomalies:
            alert = {
                "alert_id": f"alert_{int(datetime.now().timestamp())}_{len(self.active_alerts)}",
                "type": "performance_anomaly",
                "severity": anomaly["severity"],
                "metric": anomaly["metric"],
                "description": anomaly["description"],
                "model_id": parameters.get("model_id", "default"),
                "timestamp": datetime.now(),
                "status": "active",
                "auto_resolve": True,
                "escalation_level": 0
            }
            
            # Add to active alerts
            self.active_alerts.append(alert)
            alerts_generated.append(alert)
            
            # Send alert message to other agents
            await self._send_alert_message(alert)
        
        return {
            "alerts_generated": len(alerts_generated),
            "alerts": alerts_generated
        }
    
    async def _collect_recent_data(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Collect recent data for drift detection."""
        
        # Simulate collecting recent inference data
        recent_data = {
            "sample_count": np.random.randint(1000, 5000),
            "feature_statistics": {
                f"feature_{i}": {
                    "mean": np.random.normal(0, 1),
                    "std": np.random.uniform(0.5, 2.0),
                    "min": np.random.uniform(-3, -1),
                    "max": np.random.uniform(1, 3)
                }
                for i in range(10)
            },
            "prediction_distribution": {
                "class_0": np.random.uniform(0.3, 0.7),
                "class_1": np.random.uniform(0.3, 0.7)
            },
            "collection_period": "last_24_hours"
        }
        
        return recent_data
    
    async def _compare_with_baseline(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Compare recent data with baseline."""
        
        model_id = parameters.get("model_id", "default")
        
        # Get or create baseline
        if model_id not in self.baseline_models:
            # Create baseline from recent data (simplified)
            self.baseline_models[model_id] = {
                "feature_statistics": parameters.get("feature_statistics", {}),
                "prediction_distribution": parameters.get("prediction_distribution", {}),
                "created_at": datetime.now(),
                "sample_count": parameters.get("sample_count", 0)
            }
            
            return {"comparison": "baseline_created", "baseline_established": True}
        
        baseline = self.baseline_models[model_id]
        recent_data = parameters
        
        # Compare feature statistics
        feature_comparisons = {}
        for feature_name in baseline["feature_statistics"]:
            if feature_name in recent_data.get("feature_statistics", {}):
                baseline_stats = baseline["feature_statistics"][feature_name]
                recent_stats = recent_data["feature_statistics"][feature_name]
                
                # Calculate differences
                mean_diff = abs(recent_stats["mean"] - baseline_stats["mean"])
                std_diff = abs(recent_stats["std"] - baseline_stats["std"])
                
                feature_comparisons[feature_name] = {
                    "mean_difference": mean_diff,
                    "std_difference": std_diff,
                    "significant_change": mean_diff > 0.5 or std_diff > 0.3
                }
        
        return {
            "comparison_completed": True,
            "feature_comparisons": feature_comparisons,
            "baseline_age_days": (datetime.now() - baseline["created_at"]).days
        }
    
    async def _calculate_drift_scores(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate drift scores for different types of drift."""
        
        feature_comparisons = parameters.get("feature_comparisons", {})
        
        # Calculate data drift score
        data_drift_scores = []
        for feature_name, comparison in feature_comparisons.items():
            if comparison.get("significant_change", False):
                # Simple drift score calculation
                drift_score = min(1.0, comparison["mean_difference"] + comparison["std_difference"])
                data_drift_scores.append(drift_score)
        
        overall_data_drift = np.mean(data_drift_scores) if data_drift_scores else 0.0
        
        # Simulate concept drift detection
        concept_drift_score = np.random.uniform(0.0, 0.3)  # Usually low
        
        # Simulate prediction drift
        prediction_drift_score = np.random.uniform(0.0, 0.2)
        
        drift_scores = {
            "data_drift": overall_data_drift,
            "concept_drift": concept_drift_score,
            "prediction_drift": prediction_drift_score,
            "overall_drift": (overall_data_drift + concept_drift_score + prediction_drift_score) / 3
        }
        
        return {"drift_scores": drift_scores}
    
    async def _generate_drift_alerts(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate alerts for detected drift."""
        
        drift_scores = parameters.get("drift_scores", {})
        alerts_generated = []
        
        for drift_type, score in drift_scores.items():
            if drift_type == "overall_drift":
                continue
            
            # Check thresholds
            metric_key = MonitoringMetric.DATA_DRIFT if drift_type == "data_drift" else MonitoringMetric.CONCEPT_DRIFT
            thresholds = self.alert_thresholds.get(metric_key, {"warning": 0.1, "critical": 0.2})
            
            severity = None
            if score > thresholds["critical"]:
                severity = AlertSeverity.CRITICAL
            elif score > thresholds["warning"]:
                severity = AlertSeverity.MEDIUM
            
            if severity:
                alert = {
                    "alert_id": f"drift_alert_{int(datetime.now().timestamp())}",
                    "type": "drift_detection",
                    "severity": severity,
                    "drift_type": drift_type,
                    "drift_score": score,
                    "description": f"{drift_type.replace('_', ' ').title()} detected with score {score:.3f}",
                    "model_id": parameters.get("model_id", "default"),
                    "timestamp": datetime.now(),
                    "status": "active",
                    "recommended_action": "model_retraining" if score > 0.15 else "investigation"
                }
                
                self.active_alerts.append(alert)
                alerts_generated.append(alert)
                
                await self._send_alert_message(alert)
        
        return {
            "drift_alerts_generated": len(alerts_generated),
            "alerts": alerts_generated
        }
    
    async def _send_alert_message(self, alert: Dict[str, Any]) -> None:
        """Send alert message to relevant agents."""
        
        # Determine which agents should receive the alert
        recipients = []
        
        if alert["type"] == "performance_anomaly":
            recipients = ["devops_001", "trainer_001"]
        elif alert["type"] == "drift_detection":
            recipients = ["architect_001", "engineer_001", "trainer_001"]
        
        # Send alert messages
        for recipient in recipients:
            message = AgentMessage(
                sender_id=self.agent_id,
                receiver_id=recipient,
                message_type="alert",
                content={
                    "alert": alert,
                    "priority": 5 if alert["severity"] == AlertSeverity.CRITICAL else 3,
                    "requires_action": True
                },
                priority=5 if alert["severity"] == AlertSeverity.CRITICAL else 3
            )
            
            await self.send_message(message)
    
    async def _generate_monitoring_report(
        self, 
        results: Dict[str, Any], 
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comprehensive monitoring report."""
        
        report = {
            "monitoring_summary": {
                "model_id": parameters.get("model_id", "default"),
                "monitoring_type": parameters.get("type", "performance_monitoring"),
                "timestamp": datetime.now().isoformat(),
                "status": "completed"
            },
            "metrics_collected": results.get("collect_performance_metrics", {}),
            "trend_analysis": results.get("analyze_metric_trends", {}),
            "anomalies_detected": results.get("detect_performance_anomalies", {}),
            "alerts_generated": results.get("generate_performance_alerts", {}),
            "drift_analysis": {
                "drift_scores": results.get("calculate_drift_scores", {}),
                "drift_alerts": results.get("generate_drift_alerts", {})
            },
            "recommendations": self._generate_recommendations(results),
            "next_monitoring_schedule": self._calculate_next_monitoring_time(parameters)
        }
        
        return report
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on monitoring results."""
        
        recommendations = []
        
        # Check for performance issues
        anomalies = results.get("detect_performance_anomalies", {}).get("anomalies", [])
        if anomalies:
            critical_anomalies = [a for a in anomalies if a["severity"] == AlertSeverity.CRITICAL]
            if critical_anomalies:
                recommendations.append("Immediate investigation required for critical performance issues")
                recommendations.append("Consider scaling resources or rolling back recent changes")
        
        # Check for drift
        drift_scores = results.get("calculate_drift_scores", {}).get("drift_scores", {})
        if drift_scores.get("data_drift", 0) > 0.15:
            recommendations.append("Data drift detected - consider retraining model with recent data")
        
        if drift_scores.get("concept_drift", 0) > 0.2:
            recommendations.append("Concept drift detected - model architecture may need updating")
        
        # General recommendations
        if not recommendations:
            recommendations.append("System operating within normal parameters")
            recommendations.append("Continue regular monitoring schedule")
        
        return recommendations
    
    def _calculate_next_monitoring_time(self, parameters: Dict[str, Any]) -> datetime:
        """Calculate when next monitoring should occur."""
        
        monitoring_type = parameters.get("type", "performance_monitoring")
        
        if monitoring_type == "performance_monitoring":
            interval = self.monitoring_intervals[MonitoringMetric.MODEL_ACCURACY]
        elif monitoring_type == "drift_detection":
            interval = self.monitoring_intervals[MonitoringMetric.DATA_DRIFT]
        else:
            interval = 300  # Default 5 minutes
        
        return datetime.now() + timedelta(seconds=interval)
    
    async def handle_message(self, message: AgentMessage) -> None:
        """Handle incoming messages from other agents."""
        
        if message.message_type.value == "task_request":
            # Convert message to monitoring task
            task = AgentTask(
                name=message.content.get("task_name", "monitoring"),
                description=message.content.get("description", ""),
                parameters=message.content.get("parameters", {}),
                priority=message.priority
            )
            await self.assign_task(task)
            
        elif message.message_type.value == "alert":
            # Handle alerts from other agents
            await self._handle_external_alert(message)
    
    async def _handle_external_alert(self, message: AgentMessage) -> None:
        """Handle alerts received from other agents."""
        
        alert_content = message.content.get("alert", {})
        
        # Log the external alert
        logger.info(f"Received external alert from {message.sender_id}: {alert_content.get('description', 'Unknown alert')}")
        
        # Add to alert history
        self.alert_history.append({
            "source": message.sender_id,
            "alert": alert_content,
            "received_at": datetime.now()
        })
    
    async def self_improve(self) -> None:
        """Self-improvement based on monitoring effectiveness."""
        
        if len(self.alert_history) < 10:
            return
        
        # Analyze alert patterns
        recent_alerts = self.alert_history[-50:]
        
        # Check for false positive rate
        false_positives = 0
        total_alerts = len(recent_alerts)
        
        # Simplified false positive detection
        for alert in recent_alerts:
            if alert.get("resolved_as_false_positive", False):
                false_positives += 1
        
        false_positive_rate = false_positives / total_alerts if total_alerts > 0 else 0
        
        # Adjust thresholds if too many false positives
        if false_positive_rate > 0.3:  # More than 30% false positives
            for metric, thresholds in self.alert_thresholds.items():
                # Make thresholds less sensitive
                thresholds["warning"] *= 1.1
                thresholds["critical"] *= 1.1
            
            logger.info(f"Sentinel agent {self.agent_id} adjusted alert thresholds due to high false positive rate")
        
        # Adjust monitoring intervals based on system stability
        stable_periods = sum(1 for alert in recent_alerts if alert.get("severity") in ["low", "medium"])
        if stable_periods / total_alerts > 0.8:  # System is stable
            # Increase monitoring intervals slightly
            for metric in self.monitoring_intervals:
                self.monitoring_intervals[metric] = min(
                    self.monitoring_intervals[metric] * 1.1,
                    3600  # Max 1 hour
                )
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status."""
        
        return {
            "active_monitors": len(self.active_monitors),
            "active_alerts": len(self.active_alerts),
            "total_alerts_today": len([
                alert for alert in self.alert_history 
                if alert.get("timestamp", datetime.min).date() == datetime.now().date()
            ]),
            "monitoring_intervals": self.monitoring_intervals,
            "alert_thresholds": self.alert_thresholds,
            "models_monitored": list(self.metric_history.keys()),
            "last_monitoring_run": max([
                entry["timestamp"] for entries in self.metric_history.values() 
                for entry in entries
            ], default=None)
        }
