"""
Trainer Agent for NeuroFlowAI
=============================

Specialized agent for GPU-based training orchestration and management.
Handles distributed training, resource allocation, and training optimization.
"""

import asyncio
import json
import time
import numpy as np
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum

from .base_agent import BaseAgent, AgentTask, AgentMessage, AgentState
import logging

logger = logging.getLogger(__name__)


class TrainingStatus(str, Enum):
    """Training job status enumeration."""
    QUEUED = "queued"
    INITIALIZING = "initializing"
    TRAINING = "training"
    VALIDATING = "validating"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class GPUType(str, Enum):
    """GPU types available for training."""
    T4 = "nvidia-tesla-t4"
    V100 = "nvidia-tesla-v100"
    A100 = "nvidia-a100"
    H100 = "nvidia-h100"
    RTX3090 = "nvidia-rtx-3090"
    RTX4090 = "nvidia-rtx-4090"


class TrainerAgent(BaseAgent):
    """
    Agent responsible for GPU-based training orchestration.
    
    Capabilities:
    - GPU resource allocation and scheduling
    - Distributed training coordination
    - Training job monitoring and optimization
    - Automatic checkpointing and recovery
    - Resource cost optimization
    - Training performance analysis
    """
    
    def __init__(self, agent_id: str = "trainer_001", **kwargs):
        super().__init__(
            agent_id=agent_id,
            name="Trainer Agent",
            description="Orchestrates GPU-based training with optimal resource allocation",
            capabilities=[
                "gpu_scheduling",
                "distributed_training",
                "training_optimization",
                "checkpoint_management",
                "resource_monitoring",
                "cost_optimization",
                "training_analytics",
                "fault_tolerance"
            ],
            max_concurrent_tasks=10,  # Can handle multiple training jobs
            **kwargs
        )
        
        # GPU resource management
        self.available_gpus = {
            GPUType.T4: {"count": 4, "memory_gb": 16, "cost_per_hour": 0.35},
            GPUType.V100: {"count": 2, "memory_gb": 32, "cost_per_hour": 2.48},
            GPUType.A100: {"count": 1, "memory_gb": 80, "cost_per_hour": 3.20},
        }
        
        self.training_queue = []
        self.active_training_jobs = {}
        self.completed_jobs_history = []
        
        # Training optimization strategies
        self.optimization_strategies = {
            "mixed_precision": {"speedup": 1.6, "memory_reduction": 0.5},
            "gradient_accumulation": {"memory_reduction": 0.3, "batch_size_multiplier": 4},
            "model_parallelism": {"memory_reduction": 0.7, "setup_overhead": 0.2},
            "data_parallelism": {"speedup_per_gpu": 0.85, "communication_overhead": 0.1},
            "gradient_checkpointing": {"memory_reduction": 0.4, "compute_overhead": 0.2}
        }
        
        # Performance tracking
        self.training_metrics = {
            "total_jobs_completed": 0,
            "total_gpu_hours_used": 0.0,
            "average_training_time": 0.0,
            "cost_efficiency_score": 0.0,
            "resource_utilization": 0.0
        }
    
    async def plan_task(self, task: AgentTask) -> Dict[str, Any]:
        """Plan how to execute a training task."""
        task_type = task.parameters.get("type", "model_training")
        
        if task_type == "model_training":
            return await self._plan_model_training(task)
        elif task_type == "distributed_training":
            return await self._plan_distributed_training(task)
        elif task_type == "hyperparameter_sweep":
            return await self._plan_hyperparameter_sweep(task)
        elif task_type == "training_optimization":
            return await self._plan_training_optimization(task)
        else:
            raise ValueError(f"Unknown task type: {task_type}")
    
    async def _plan_model_training(self, task: AgentTask) -> Dict[str, Any]:
        """Plan single model training task."""
        model_config = task.parameters.get("model_config", {})
        data_config = task.parameters.get("data_config", {})
        
        # Estimate resource requirements
        resource_estimate = await self._estimate_training_resources(model_config, data_config)
        
        # Select optimal GPU configuration
        gpu_config = await self._select_gpu_configuration(resource_estimate)
        
        # Estimate training time and cost
        time_estimate = await self._estimate_training_time(model_config, gpu_config)
        cost_estimate = await self._estimate_training_cost(gpu_config, time_estimate)
        
        return {
            "steps": [
                "allocate_gpu_resources",
                "setup_training_environment",
                "initialize_model_and_data",
                "execute_training_loop",
                "monitor_training_progress",
                "save_checkpoints",
                "evaluate_final_model",
                "cleanup_resources"
            ],
            "estimated_time": time_estimate["total_hours"] * 3600,  # Convert to seconds
            "required_resources": gpu_config,
            "estimated_cost": cost_estimate,
            "optimization_strategies": await self._recommend_optimizations(model_config, gpu_config),
            "dependencies": ["data_preprocessing", "feature_engineering"]
        }
    
    async def _plan_distributed_training(self, task: AgentTask) -> Dict[str, Any]:
        """Plan distributed training across multiple GPUs."""
        model_config = task.parameters.get("model_config", {})
        
        # Determine optimal distribution strategy
        distribution_strategy = await self._select_distribution_strategy(model_config)
        
        # Calculate resource requirements for distributed setup
        gpu_count = distribution_strategy.get("gpu_count", 2)
        gpu_type = distribution_strategy.get("gpu_type", GPUType.V100)
        
        return {
            "steps": [
                "setup_distributed_environment",
                "allocate_multi_gpu_resources",
                "configure_data_parallelism",
                "initialize_distributed_model",
                "execute_distributed_training",
                "synchronize_gradients",
                "aggregate_results",
                "cleanup_distributed_resources"
            ],
            "estimated_time": 7200,  # 2 hours
            "required_resources": {
                "gpu_type": gpu_type,
                "gpu_count": gpu_count,
                "memory_per_gpu": self.available_gpus[gpu_type]["memory_gb"],
                "network_bandwidth": "high"
            },
            "distribution_strategy": distribution_strategy,
            "dependencies": ["model_architecture_ready"]
        }
    
    async def execute_plan(self, plan: Dict[str, Any], task: AgentTask) -> Dict[str, Any]:
        """Execute the planned training task."""
        training_job_id = f"job_{task.id}_{int(time.time())}"
        
        # Create training job record
        training_job = {
            "job_id": training_job_id,
            "task_id": task.id,
            "status": TrainingStatus.QUEUED,
            "start_time": None,
            "end_time": None,
            "progress": 0.0,
            "current_epoch": 0,
            "total_epochs": task.parameters.get("epochs", 100),
            "best_metric": None,
            "resource_usage": {},
            "cost_accumulated": 0.0,
            "checkpoints": [],
            "logs": []
        }
        
        self.active_training_jobs[training_job_id] = training_job
        
        try:
            results = {}
            
            for step in plan["steps"]:
                step_result = await self._execute_training_step(
                    step, task.parameters, plan, training_job
                )
                results[step] = step_result
                
                # Update task progress
                progress = len([s for s in plan["steps"] if s in results]) / len(plan["steps"])
                task.progress = progress
                training_job["progress"] = progress
                
                # Check for early stopping conditions
                if await self._should_stop_early(training_job, step_result):
                    logger.info(f"Early stopping triggered for job {training_job_id}")
                    break
            
            # Mark job as completed
            training_job["status"] = TrainingStatus.COMPLETED
            training_job["end_time"] = datetime.utcnow()
            
            # Generate training report
            training_report = await self._generate_training_report(training_job, results)
            results["training_report"] = training_report
            
            # Update metrics
            await self._update_training_metrics(training_job)
            
            return results
            
        except Exception as e:
            training_job["status"] = TrainingStatus.FAILED
            training_job["error"] = str(e)
            training_job["end_time"] = datetime.utcnow()
            logger.error(f"Training job {training_job_id} failed: {e}")
            raise
        
        finally:
            # Move to completed jobs history
            self.completed_jobs_history.append(training_job)
            if training_job_id in self.active_training_jobs:
                del self.active_training_jobs[training_job_id]
    
    async def _execute_training_step(
        self, 
        step: str, 
        parameters: Dict[str, Any], 
        plan: Dict[str, Any],
        training_job: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a single step in the training process."""
        
        if step == "allocate_gpu_resources":
            return await self._allocate_gpu_resources(plan["required_resources"], training_job)
        elif step == "setup_training_environment":
            return await self._setup_training_environment(parameters, training_job)
        elif step == "initialize_model_and_data":
            return await self._initialize_model_and_data(parameters, training_job)
        elif step == "execute_training_loop":
            return await self._execute_training_loop(parameters, training_job)
        elif step == "monitor_training_progress":
            return await self._monitor_training_progress(training_job)
        elif step == "save_checkpoints":
            return await self._save_checkpoints(training_job)
        elif step == "evaluate_final_model":
            return await self._evaluate_final_model(training_job)
        elif step == "cleanup_resources":
            return await self._cleanup_resources(training_job)
        else:
            return {"status": "completed", "message": f"Step {step} executed"}
    
    async def _allocate_gpu_resources(
        self, 
        required_resources: Dict[str, Any], 
        training_job: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Allocate GPU resources for training."""
        gpu_type = required_resources.get("gpu_type", GPUType.T4)
        gpu_count = required_resources.get("gpu_count", 1)
        
        # Check availability
        available_count = self.available_gpus[gpu_type]["count"]
        if available_count < gpu_count:
            # Try to find alternative GPU configuration
            alternative = await self._find_alternative_gpu_config(required_resources)
            if alternative:
                gpu_type = alternative["gpu_type"]
                gpu_count = alternative["gpu_count"]
            else:
                raise RuntimeError(f"Insufficient GPU resources: need {gpu_count} {gpu_type}, have {available_count}")
        
        # Allocate resources
        self.available_gpus[gpu_type]["count"] -= gpu_count
        
        allocation = {
            "allocated_gpus": {
                "type": gpu_type,
                "count": gpu_count,
                "memory_per_gpu": self.available_gpus[gpu_type]["memory_gb"],
                "cost_per_hour": self.available_gpus[gpu_type]["cost_per_hour"]
            },
            "allocation_time": datetime.utcnow().isoformat(),
            "estimated_cost_per_hour": self.available_gpus[gpu_type]["cost_per_hour"] * gpu_count
        }
        
        training_job["resource_allocation"] = allocation
        training_job["status"] = TrainingStatus.INITIALIZING
        
        logger.info(f"Allocated {gpu_count} {gpu_type} GPUs for job {training_job['job_id']}")
        
        return allocation

    async def _setup_training_environment(
        self,
        parameters: Dict[str, Any],
        training_job: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Setup the training environment with required dependencies."""

        environment_config = {
            "framework": parameters.get("framework", "pytorch"),
            "python_version": "3.11",
            "cuda_version": "12.1",
            "libraries": [
                "torch>=2.0.0",
                "transformers>=4.30.0",
                "datasets>=2.12.0",
                "accelerate>=0.20.0",
                "wandb>=0.15.0"
            ],
            "environment_variables": {
                "CUDA_VISIBLE_DEVICES": "0,1,2,3",
                "NCCL_DEBUG": "INFO",
                "TORCH_DISTRIBUTED_DEBUG": "DETAIL"
            }
        }

        # Simulate environment setup
        await asyncio.sleep(0.1)  # Simulate setup time

        training_job["environment"] = environment_config

        return {
            "status": "completed",
            "environment": environment_config,
            "setup_time": 30  # seconds
        }

    async def _initialize_model_and_data(
        self,
        parameters: Dict[str, Any],
        training_job: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Initialize model and data loaders for training."""

        model_config = parameters.get("model_config", {})
        data_config = parameters.get("data_config", {})

        initialization_result = {
            "model": {
                "architecture": model_config.get("architecture", "transformer"),
                "parameters": model_config.get("num_parameters", 125000000),
                "memory_usage_mb": model_config.get("memory_usage", 2048),
                "initialization_time": 15
            },
            "data": {
                "train_samples": data_config.get("train_samples", 100000),
                "val_samples": data_config.get("val_samples", 10000),
                "batch_size": data_config.get("batch_size", 32),
                "num_workers": data_config.get("num_workers", 4),
                "loading_time": 10
            },
            "optimizer": {
                "type": parameters.get("optimizer", "adamw"),
                "learning_rate": parameters.get("learning_rate", 0.001),
                "weight_decay": parameters.get("weight_decay", 0.01)
            }
        }

        training_job["model_config"] = initialization_result

        return initialization_result

    async def _execute_training_loop(
        self,
        parameters: Dict[str, Any],
        training_job: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute the main training loop with monitoring."""

        total_epochs = parameters.get("epochs", 100)
        training_job["status"] = TrainingStatus.TRAINING
        training_job["start_time"] = datetime.utcnow()

        training_metrics = {
            "epochs_completed": 0,
            "best_validation_loss": float('inf'),
            "best_validation_accuracy": 0.0,
            "training_loss_history": [],
            "validation_loss_history": [],
            "learning_rate_history": [],
            "gpu_utilization": [],
            "memory_usage": []
        }

        # Simulate training loop
        for epoch in range(total_epochs):
            # Simulate epoch training
            epoch_start = time.time()

            # Simulate training metrics
            train_loss = 2.0 * (0.95 ** epoch) + 0.1 * (1 + 0.1 * epoch % 10)
            val_loss = train_loss + 0.1 + 0.05 * (epoch % 5)
            val_accuracy = min(0.95, 0.5 + 0.4 * (1 - 0.98 ** epoch))

            # Update metrics
            training_metrics["training_loss_history"].append(train_loss)
            training_metrics["validation_loss_history"].append(val_loss)
            training_metrics["gpu_utilization"].append(85 + 10 * (epoch % 3))
            training_metrics["memory_usage"].append(70 + 5 * (epoch % 4))

            # Check for best model
            if val_loss < training_metrics["best_validation_loss"]:
                training_metrics["best_validation_loss"] = val_loss
                training_metrics["best_validation_accuracy"] = val_accuracy

                # Save checkpoint
                checkpoint = {
                    "epoch": epoch,
                    "model_state": f"checkpoint_epoch_{epoch}",
                    "optimizer_state": f"optimizer_epoch_{epoch}",
                    "validation_loss": val_loss,
                    "validation_accuracy": val_accuracy,
                    "timestamp": datetime.utcnow().isoformat()
                }
                training_job["checkpoints"].append(checkpoint)

            # Update progress
            training_job["current_epoch"] = epoch + 1
            training_job["progress"] = (epoch + 1) / total_epochs

            # Log progress
            if epoch % 10 == 0:
                logger.info(f"Job {training_job['job_id']} - Epoch {epoch}: "
                          f"train_loss={train_loss:.4f}, val_loss={val_loss:.4f}, "
                          f"val_acc={val_accuracy:.4f}")

            # Simulate epoch time
            await asyncio.sleep(0.01)  # Very short sleep to simulate time

            # Check for early stopping
            if await self._check_early_stopping(training_metrics, epoch):
                logger.info(f"Early stopping at epoch {epoch}")
                break

        training_metrics["epochs_completed"] = training_job["current_epoch"]
        training_job["training_metrics"] = training_metrics

        return training_metrics

    async def _monitor_training_progress(self, training_job: Dict[str, Any]) -> Dict[str, Any]:
        """Monitor training progress and resource usage."""

        monitoring_data = {
            "current_status": training_job["status"].value,
            "progress_percentage": training_job["progress"] * 100,
            "current_epoch": training_job["current_epoch"],
            "total_epochs": training_job["total_epochs"],
            "elapsed_time": self._calculate_elapsed_time(training_job),
            "estimated_remaining_time": self._estimate_remaining_time(training_job),
            "resource_utilization": {
                "gpu_utilization": 87.5,
                "memory_usage": 75.2,
                "cpu_usage": 45.0,
                "network_io": 12.3
            },
            "cost_so_far": self._calculate_current_cost(training_job),
            "performance_metrics": training_job.get("training_metrics", {})
        }

        return monitoring_data

    async def _save_checkpoints(self, training_job: Dict[str, Any]) -> Dict[str, Any]:
        """Save model checkpoints during training."""

        checkpoints = training_job.get("checkpoints", [])

        checkpoint_info = {
            "total_checkpoints": len(checkpoints),
            "latest_checkpoint": checkpoints[-1] if checkpoints else None,
            "best_checkpoint": max(checkpoints, key=lambda x: x.get("validation_accuracy", 0)) if checkpoints else None,
            "storage_location": f"s3://neuroflow-checkpoints/{training_job['job_id']}/",
            "checkpoint_size_mb": 250.5,
            "save_frequency": "every_epoch_if_improved"
        }

        return checkpoint_info

    async def _evaluate_final_model(self, training_job: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate the final trained model."""

        training_metrics = training_job.get("training_metrics", {})

        evaluation_results = {
            "final_validation_accuracy": training_metrics.get("best_validation_accuracy", 0.0),
            "final_validation_loss": training_metrics.get("best_validation_loss", float('inf')),
            "training_stability": self._assess_training_stability(training_metrics),
            "convergence_analysis": self._analyze_convergence(training_metrics),
            "model_quality_score": self._calculate_model_quality_score(training_metrics),
            "recommended_checkpoint": self._select_best_checkpoint(training_job),
            "performance_summary": {
                "epochs_trained": training_metrics.get("epochs_completed", 0),
                "best_epoch": self._find_best_epoch(training_metrics),
                "improvement_rate": self._calculate_improvement_rate(training_metrics)
            }
        }

        return evaluation_results

    async def _cleanup_resources(self, training_job: Dict[str, Any]) -> Dict[str, Any]:
        """Clean up allocated resources after training."""

        resource_allocation = training_job.get("resource_allocation", {})
        allocated_gpus = resource_allocation.get("allocated_gpus", {})

        # Release GPU resources
        gpu_type = GPUType(allocated_gpus.get("type", GPUType.T4))
        gpu_count = allocated_gpus.get("count", 1)

        self.available_gpus[gpu_type]["count"] += gpu_count

        cleanup_summary = {
            "resources_released": {
                "gpu_type": gpu_type.value,
                "gpu_count": gpu_count,
                "release_time": datetime.now().isoformat()
            },
            "storage_cleanup": {
                "temp_files_removed": True,
                "logs_archived": True,
                "checkpoints_preserved": True
            },
            "final_cost": self._calculate_final_cost(training_job),
            "resource_utilization_efficiency": self._calculate_efficiency(training_job)
        }

        logger.info(f"Released {gpu_count} {gpu_type} GPUs from job {training_job['job_id']}")

        return cleanup_summary

    # Helper methods for resource estimation and optimization

    async def _estimate_training_resources(
        self,
        model_config: Dict[str, Any],
        data_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Estimate required training resources."""

        model_size = model_config.get("num_parameters", 125000000)
        batch_size = data_config.get("batch_size", 32)
        sequence_length = model_config.get("sequence_length", 512)

        # Estimate memory requirements (simplified calculation)
        model_memory = model_size * 4 / (1024**3)  # 4 bytes per parameter, convert to GB
        activation_memory = batch_size * sequence_length * model_config.get("hidden_size", 768) * 4 / (1024**3)
        gradient_memory = model_memory  # Gradients same size as model
        optimizer_memory = model_memory * 2  # Adam optimizer states

        total_memory_gb = model_memory + activation_memory + gradient_memory + optimizer_memory

        return {
            "estimated_memory_gb": total_memory_gb,
            "recommended_gpu_memory": max(16, int(total_memory_gb * 1.5)),  # 50% buffer
            "compute_intensity": "high" if model_size > 1e9 else "medium",
            "parallelization_potential": "high" if model_size > 1e8 else "low"
        }

    async def _select_gpu_configuration(self, resource_estimate: Dict[str, Any]) -> Dict[str, Any]:
        """Select optimal GPU configuration based on resource estimates."""

        required_memory = resource_estimate["estimated_memory_gb"]

        # Find suitable GPU type
        suitable_gpus = []
        for gpu_type, specs in self.available_gpus.items():
            if specs["memory_gb"] >= required_memory and specs["count"] > 0:
                suitable_gpus.append({
                    "gpu_type": gpu_type,
                    "memory_gb": specs["memory_gb"],
                    "cost_per_hour": specs["cost_per_hour"],
                    "available_count": specs["count"]
                })

        if not suitable_gpus:
            raise RuntimeError(f"No suitable GPUs available for {required_memory}GB memory requirement")

        # Select most cost-effective option
        best_gpu = min(suitable_gpus, key=lambda x: x["cost_per_hour"])

        return {
            "gpu_type": best_gpu["gpu_type"],
            "gpu_count": 1,
            "memory_per_gpu": best_gpu["memory_gb"],
            "total_cost_per_hour": best_gpu["cost_per_hour"]
        }

    async def _estimate_training_time(
        self,
        model_config: Dict[str, Any],
        gpu_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Estimate training time based on model and GPU configuration."""

        model_size = model_config.get("num_parameters", 125000000)
        epochs = model_config.get("epochs", 100)

        # Simplified time estimation
        base_time_per_epoch = 0.1  # hours

        # Adjust for model size
        if model_size > 1e9:
            base_time_per_epoch *= 5
        elif model_size > 1e8:
            base_time_per_epoch *= 2

        # Adjust for GPU type
        gpu_type = gpu_config["gpu_type"]
        if gpu_type == GPUType.H100:
            base_time_per_epoch *= 0.3
        elif gpu_type == GPUType.A100:
            base_time_per_epoch *= 0.5
        elif gpu_type == GPUType.V100:
            base_time_per_epoch *= 1.0
        else:
            base_time_per_epoch *= 2.0

        total_hours = base_time_per_epoch * epochs

        return {
            "time_per_epoch_hours": base_time_per_epoch,
            "total_hours": total_hours,
            "estimated_completion": datetime.now() + timedelta(hours=total_hours)
        }

    async def handle_message(self, message: AgentMessage) -> None:
        """Handle incoming messages from other agents."""
        if message.message_type.value == "task_request":
            # Convert message to training task
            task = AgentTask(
                name=message.content.get("task_name", "model_training"),
                description=message.content.get("description", ""),
                parameters=message.content.get("parameters", {}),
                priority=message.priority
            )
            await self.assign_task(task)

        elif message.message_type.value == "resource_request":
            # Handle GPU resource requests
            await self._handle_gpu_resource_request(message)

        elif message.message_type.value == "status_update":
            # Log training status updates
            logger.info(f"Training status update from {message.sender_id}: {message.content}")

    async def self_improve(self) -> None:
        """Self-improvement based on training performance and resource efficiency."""
        if len(self.completed_jobs_history) < 5:
            return

        # Analyze recent training jobs
        recent_jobs = self.completed_jobs_history[-20:]

        # Calculate efficiency metrics
        successful_jobs = [job for job in recent_jobs if job["status"] == TrainingStatus.COMPLETED]
        failed_jobs = [job for job in recent_jobs if job["status"] == TrainingStatus.FAILED]

        success_rate = len(successful_jobs) / len(recent_jobs) if recent_jobs else 0

        # Analyze resource utilization patterns
        gpu_utilization_avg = sum(
            job.get("training_metrics", {}).get("gpu_utilization", [85])[-1]
            for job in successful_jobs
        ) / len(successful_jobs) if successful_jobs else 85

        # Adjust optimization strategies based on performance
        if success_rate < 0.8:
            # Increase conservative resource allocation
            for gpu_type in self.available_gpus:
                self.available_gpus[gpu_type]["cost_per_hour"] *= 1.1  # Slight cost increase for stability

        if gpu_utilization_avg < 70:
            # Improve resource allocation efficiency
            self.memory.preferences["resource_optimization"] = "aggressive"

        logger.info(f"Trainer agent {self.agent_id} improved based on {len(recent_jobs)} recent jobs")

    # Additional helper methods

    def _calculate_elapsed_time(self, training_job: Dict[str, Any]) -> float:
        """Calculate elapsed training time in hours."""
        start_time = training_job.get("start_time")
        if not start_time:
            return 0.0

        if isinstance(start_time, str):
            start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))

        elapsed = datetime.now() - start_time.replace(tzinfo=None)
        return elapsed.total_seconds() / 3600

    def _estimate_remaining_time(self, training_job: Dict[str, Any]) -> float:
        """Estimate remaining training time in hours."""
        progress = training_job.get("progress", 0.0)
        elapsed = self._calculate_elapsed_time(training_job)

        if progress <= 0:
            return 0.0

        total_estimated = elapsed / progress
        return max(0, total_estimated - elapsed)

    def _calculate_current_cost(self, training_job: Dict[str, Any]) -> float:
        """Calculate current accumulated cost for the training job."""
        elapsed_hours = self._calculate_elapsed_time(training_job)

        resource_allocation = training_job.get("resource_allocation", {})
        cost_per_hour = resource_allocation.get("estimated_cost_per_hour", 2.50)

        return elapsed_hours * cost_per_hour

    def _calculate_final_cost(self, training_job: Dict[str, Any]) -> float:
        """Calculate final cost for completed training job."""
        return self._calculate_current_cost(training_job)

    def _calculate_efficiency(self, training_job: Dict[str, Any]) -> float:
        """Calculate resource utilization efficiency."""
        training_metrics = training_job.get("training_metrics", {})
        gpu_utilization = training_metrics.get("gpu_utilization", [])

        if not gpu_utilization:
            return 0.75  # Default efficiency

        avg_utilization = sum(gpu_utilization) / len(gpu_utilization)
        return avg_utilization / 100.0

    async def _check_early_stopping(self, training_metrics: Dict[str, Any], current_epoch: int) -> bool:
        """Check if early stopping should be triggered."""
        val_losses = training_metrics.get("validation_loss_history", [])

        if len(val_losses) < 10:  # Need at least 10 epochs
            return False

        # Check if validation loss hasn't improved in last 5 epochs
        recent_losses = val_losses[-5:]
        best_recent = min(recent_losses)
        best_overall = min(val_losses)

        # Stop if no improvement in recent epochs
        if best_recent > best_overall * 1.01:  # 1% tolerance
            return True

        return False

    def _assess_training_stability(self, training_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Assess training stability based on loss curves."""
        train_losses = training_metrics.get("training_loss_history", [])
        val_losses = training_metrics.get("validation_loss_history", [])

        if not train_losses or not val_losses:
            return {"stability": "unknown", "score": 0.5}

        # Calculate loss variance (lower is more stable)
        train_variance = np.var(train_losses[-20:]) if len(train_losses) >= 20 else np.var(train_losses)
        val_variance = np.var(val_losses[-20:]) if len(val_losses) >= 20 else np.var(val_losses)

        # Stability score (0-1, higher is better)
        stability_score = 1.0 / (1.0 + train_variance + val_variance)

        if stability_score > 0.8:
            stability = "high"
        elif stability_score > 0.6:
            stability = "medium"
        else:
            stability = "low"

        return {
            "stability": stability,
            "score": stability_score,
            "train_variance": train_variance,
            "val_variance": val_variance
        }

    # Additional helper methods for training analysis

    def _analyze_convergence(self, training_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze training convergence patterns."""
        val_losses = training_metrics.get("validation_loss_history", [])

        if len(val_losses) < 5:
            return {"converged": False, "convergence_epoch": None}

        # Simple convergence detection: loss improvement < 1% for last 5 epochs
        recent_improvement = (val_losses[-5] - val_losses[-1]) / val_losses[-5] if val_losses[-5] > 0 else 0

        converged = recent_improvement < 0.01
        convergence_epoch = len(val_losses) - 5 if converged else None

        return {
            "converged": converged,
            "convergence_epoch": convergence_epoch,
            "recent_improvement": recent_improvement
        }

    def _calculate_model_quality_score(self, training_metrics: Dict[str, Any]) -> float:
        """Calculate overall model quality score."""
        accuracy = training_metrics.get("best_validation_accuracy", 0.0)
        stability = self._assess_training_stability(training_metrics)["score"]
        convergence = self._analyze_convergence(training_metrics)

        # Weighted combination of factors
        quality_score = (
            0.5 * accuracy +  # 50% accuracy
            0.3 * stability +  # 30% stability
            0.2 * (1.0 if convergence["converged"] else 0.5)  # 20% convergence
        )

        return min(1.0, quality_score)

    def _select_best_checkpoint(self, training_job: Dict[str, Any]) -> Dict[str, Any]:
        """Select the best checkpoint based on validation metrics."""
        checkpoints = training_job.get("checkpoints", [])

        if not checkpoints:
            return {"checkpoint": None, "reason": "no_checkpoints"}

        # Select checkpoint with best validation accuracy
        best_checkpoint = max(checkpoints, key=lambda x: x.get("validation_accuracy", 0))

        return {
            "checkpoint": best_checkpoint,
            "reason": "best_validation_accuracy",
            "epoch": best_checkpoint.get("epoch", 0),
            "accuracy": best_checkpoint.get("validation_accuracy", 0.0)
        }

    def _find_best_epoch(self, training_metrics: Dict[str, Any]) -> int:
        """Find the epoch with best validation performance."""
        val_losses = training_metrics.get("validation_loss_history", [])

        if not val_losses:
            return 0

        best_epoch = val_losses.index(min(val_losses))
        return best_epoch + 1  # Convert to 1-based indexing

    def _calculate_improvement_rate(self, training_metrics: Dict[str, Any]) -> float:
        """Calculate the rate of improvement during training."""
        val_losses = training_metrics.get("validation_loss_history", [])

        if len(val_losses) < 2:
            return 0.0

        initial_loss = val_losses[0]
        final_loss = min(val_losses)

        if initial_loss <= 0:
            return 0.0

        improvement_rate = (initial_loss - final_loss) / initial_loss
        return max(0.0, improvement_rate)

    async def _generate_training_report(
        self,
        training_job: Dict[str, Any],
        results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comprehensive training report."""

        training_metrics = training_job.get("training_metrics", {})

        report = {
            "job_summary": {
                "job_id": training_job["job_id"],
                "status": training_job["status"].value,
                "duration_hours": self._calculate_elapsed_time(training_job),
                "total_cost": self._calculate_final_cost(training_job),
                "epochs_completed": training_metrics.get("epochs_completed", 0),
                "best_accuracy": training_metrics.get("best_validation_accuracy", 0.0)
            },
            "performance_metrics": {
                "final_accuracy": training_metrics.get("best_validation_accuracy", 0.0),
                "final_loss": training_metrics.get("best_validation_loss", float('inf')),
                "training_stability": self._assess_training_stability(training_metrics),
                "convergence_analysis": self._analyze_convergence(training_metrics),
                "model_quality_score": self._calculate_model_quality_score(training_metrics)
            },
            "resource_utilization": {
                "gpu_efficiency": self._calculate_efficiency(training_job),
                "average_gpu_utilization": np.mean(training_metrics.get("gpu_utilization", [85])),
                "peak_memory_usage": max(training_metrics.get("memory_usage", [70])),
                "cost_efficiency": training_job.get("cost_accumulated", 0.0) / max(1, training_metrics.get("best_validation_accuracy", 0.1))
            },
            "recommendations": {
                "model_readiness": "production_ready" if training_metrics.get("best_validation_accuracy", 0) > 0.85 else "needs_improvement",
                "suggested_improvements": self._suggest_improvements(training_metrics),
                "deployment_recommendations": self._get_deployment_recommendations(training_job),
                "cost_optimization_tips": self._get_cost_optimization_tips(training_job)
            },
            "artifacts": {
                "best_checkpoint": self._select_best_checkpoint(training_job),
                "model_artifacts": f"s3://neuroflow-models/{training_job['job_id']}/",
                "logs_location": f"s3://neuroflow-logs/{training_job['job_id']}/",
                "tensorboard_url": f"https://tensorboard.neuroflow.ai/{training_job['job_id']}"
            }
        }

        return report

    def _suggest_improvements(self, training_metrics: Dict[str, Any]) -> List[str]:
        """Suggest improvements based on training metrics."""
        suggestions = []

        accuracy = training_metrics.get("best_validation_accuracy", 0.0)
        stability = self._assess_training_stability(training_metrics)["score"]

        if accuracy < 0.8:
            suggestions.append("Consider data augmentation or feature engineering")

        if stability < 0.6:
            suggestions.append("Reduce learning rate or add regularization")

        if len(training_metrics.get("validation_loss_history", [])) > 50:
            suggestions.append("Consider early stopping to prevent overfitting")

        return suggestions

    def _get_deployment_recommendations(self, training_job: Dict[str, Any]) -> List[str]:
        """Get deployment recommendations based on training results."""
        recommendations = []

        model_quality = self._calculate_model_quality_score(training_job.get("training_metrics", {}))

        if model_quality > 0.9:
            recommendations.append("Ready for production deployment")
        elif model_quality > 0.7:
            recommendations.append("Suitable for staging environment testing")
        else:
            recommendations.append("Requires further training before deployment")

        return recommendations

    def _get_cost_optimization_tips(self, training_job: Dict[str, Any]) -> List[str]:
        """Get cost optimization tips based on resource usage."""
        tips = []

        efficiency = self._calculate_efficiency(training_job)

        if efficiency < 0.7:
            tips.append("Consider using smaller batch sizes to improve GPU utilization")

        if self._calculate_elapsed_time(training_job) > 10:
            tips.append("Consider using spot instances for long training jobs")

        return tips

    async def _update_training_metrics(self, training_job: Dict[str, Any]) -> None:
        """Update global training metrics based on completed job."""
        self.training_metrics["total_jobs_completed"] += 1

        duration = self._calculate_elapsed_time(training_job)
        self.training_metrics["total_gpu_hours_used"] += duration

        # Update average training time
        total_jobs = self.training_metrics["total_jobs_completed"]
        current_avg = self.training_metrics["average_training_time"]
        self.training_metrics["average_training_time"] = (
            (current_avg * (total_jobs - 1) + duration) / total_jobs
        )

        # Update efficiency metrics
        efficiency = self._calculate_efficiency(training_job)
        self.training_metrics["resource_utilization"] = (
            (self.training_metrics["resource_utilization"] * (total_jobs - 1) + efficiency) / total_jobs
        )

    async def _handle_gpu_resource_request(self, message: AgentMessage) -> None:
        """Handle GPU resource requests from other agents."""
        requested_gpus = message.content.get("gpu_count", 1)
        gpu_type = GPUType(message.content.get("gpu_type", GPUType.T4))

        available = self.available_gpus[gpu_type]["count"]

        response_content = {
            "available_gpus": available,
            "can_fulfill": available >= requested_gpus,
            "alternative_options": self._get_alternative_gpu_options(requested_gpus)
        }

        response = AgentMessage(
            sender_id=self.agent_id,
            receiver_id=message.sender_id,
            message_type="task_response",
            content=response_content,
            correlation_id=message.id
        )

        await self.send_message(response)

    def _get_alternative_gpu_options(self, requested_count: int) -> List[Dict[str, Any]]:
        """Get alternative GPU options if primary choice is unavailable."""
        alternatives = []

        for gpu_type, specs in self.available_gpus.items():
            if specs["count"] >= requested_count:
                alternatives.append({
                    "gpu_type": gpu_type.value,
                    "available_count": specs["count"],
                    "memory_gb": specs["memory_gb"],
                    "cost_per_hour": specs["cost_per_hour"]
                })

        # Sort by cost efficiency
        alternatives.sort(key=lambda x: x["cost_per_hour"])

        return alternatives[:3]  # Return top 3 alternatives
