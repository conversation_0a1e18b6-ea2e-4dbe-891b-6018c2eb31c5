"""
NeuroFlowAI API v1
==================

Complete API module for NeuroFlowAI platform.
"""

from fastapi import APIRouter
from .auth import router as auth_router
from .workflows import router as workflows_router
from .agents import router as agents_router
from .models import router as models_router
from .marketplace import router as marketplace_router
from .monitoring import router as monitoring_router
from .data import router as data_router

# Create main API router
api_router = APIRouter(prefix="/api/v1")

# Include all sub-routers
api_router.include_router(auth_router, tags=["authentication"])
api_router.include_router(workflows_router, tags=["workflows"])
api_router.include_router(agents_router, tags=["agents"])
api_router.include_router(models_router, tags=["models"])
api_router.include_router(marketplace_router, tags=["marketplace"])
api_router.include_router(monitoring_router, tags=["monitoring"])
api_router.include_router(data_router, tags=["data"])

__all__ = ["api_router"]
