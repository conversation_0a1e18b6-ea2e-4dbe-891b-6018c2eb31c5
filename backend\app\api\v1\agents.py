"""
Agents API Endpoints for NeuroFlowAI
====================================

REST API endpoints for agent management and monitoring.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel
from typing import Any, Dict, List, Optional
from datetime import datetime

from ...services.agent_orchestrator import AgentOrchestrator
from ...core.auth import get_current_user
from ...models.user import User

import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/agents")

# Initialize orchestrator (would be dependency injected in production)
orchestrator = AgentOrchestrator()


class AgentStatus(BaseModel):
    """Agent status response model."""
    agent_id: str
    name: str
    status: str
    current_tasks: int
    completed_tasks: int
    success_rate: float
    last_activity: datetime
    capabilities: List[str]
    performance_metrics: Dict[str, Any]


class TaskRequest(BaseModel):
    """Task request model."""
    agent_type: str
    task_name: str
    description: str
    parameters: Dict[str, Any]
    priority: int = 1


@router.get("/", response_model=List[AgentStatus])
async def list_agents(current_user: User = Depends(get_current_user)):
    """Get list of all agents and their status."""
    
    try:
        agents_status = []
        
        for agent_id, agent in orchestrator.agents.items():
            status = AgentStatus(
                agent_id=agent_id,
                name=agent.name,
                status=agent.status.value,
                current_tasks=len(agent.current_tasks),
                completed_tasks=len(agent.task_history),
                success_rate=agent.calculate_success_rate(),
                last_activity=agent.last_activity or datetime.now(),
                capabilities=agent.capabilities,
                performance_metrics=agent.get_performance_metrics()
            )
            agents_status.append(status)
        
        return agents_status
        
    except Exception as e:
        logger.error(f"Failed to list agents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{agent_id}", response_model=AgentStatus)
async def get_agent_status(
    agent_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get detailed status of a specific agent."""
    
    try:
        if agent_id not in orchestrator.agents:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        agent = orchestrator.agents[agent_id]
        
        return AgentStatus(
            agent_id=agent_id,
            name=agent.name,
            status=agent.status.value,
            current_tasks=len(agent.current_tasks),
            completed_tasks=len(agent.task_history),
            success_rate=agent.calculate_success_rate(),
            last_activity=agent.last_activity or datetime.now(),
            capabilities=agent.capabilities,
            performance_metrics=agent.get_performance_metrics()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{agent_id}/tasks")
async def assign_task_to_agent(
    agent_id: str,
    task_request: TaskRequest,
    current_user: User = Depends(get_current_user)
):
    """Assign a task to a specific agent."""
    
    try:
        if agent_id not in orchestrator.agents:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Create task
        from ...agents.base_agent import AgentTask
        
        task = AgentTask(
            name=task_request.task_name,
            description=task_request.description,
            parameters=task_request.parameters,
            priority=task_request.priority
        )
        
        # Assign task
        agent = orchestrator.agents[agent_id]
        await agent.assign_task(task)
        
        return {
            "task_id": task.task_id,
            "agent_id": agent_id,
            "status": "assigned",
            "message": f"Task assigned to {agent.name}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to assign task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{agent_id}/tasks")
async def get_agent_tasks(
    agent_id: str,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Get tasks for a specific agent."""
    
    try:
        if agent_id not in orchestrator.agents:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        agent = orchestrator.agents[agent_id]
        
        # Get current tasks
        current_tasks = [
            {
                "task_id": task.task_id,
                "name": task.name,
                "status": "running",
                "progress": task.progress,
                "created_at": task.created_at.isoformat(),
                "priority": task.priority
            }
            for task in agent.current_tasks
        ]
        
        # Get task history
        historical_tasks = [
            {
                "task_id": task.task_id,
                "name": task.name,
                "status": "completed",
                "progress": 1.0,
                "created_at": task.created_at.isoformat(),
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "priority": task.priority,
                "success": task.success
            }
            for task in agent.task_history[-10:]  # Last 10 tasks
        ]
        
        all_tasks = current_tasks + historical_tasks
        
        # Filter by status if provided
        if status:
            all_tasks = [task for task in all_tasks if task["status"] == status]
        
        return {
            "agent_id": agent_id,
            "tasks": all_tasks,
            "total": len(all_tasks)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{agent_id}/restart")
async def restart_agent(
    agent_id: str,
    current_user: User = Depends(get_current_user)
):
    """Restart a specific agent."""
    
    try:
        if agent_id not in orchestrator.agents:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        agent = orchestrator.agents[agent_id]
        
        # Stop agent
        await agent.stop()
        
        # Start agent
        await agent.start()
        
        return {
            "agent_id": agent_id,
            "status": "restarted",
            "message": f"Agent {agent.name} restarted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to restart agent: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{agent_id}/metrics")
async def get_agent_metrics(
    agent_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get detailed metrics for a specific agent."""
    
    try:
        if agent_id not in orchestrator.agents:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        agent = orchestrator.agents[agent_id]
        
        return {
            "agent_id": agent_id,
            "performance_metrics": agent.get_performance_metrics(),
            "resource_usage": {
                "cpu_usage": 0.0,  # Would be implemented with actual monitoring
                "memory_usage": 0.0,
                "task_queue_size": len(agent.task_queue)
            },
            "health_status": {
                "status": agent.status.value,
                "last_heartbeat": agent.last_activity.isoformat() if agent.last_activity else None,
                "error_count": 0,  # Would track actual errors
                "uptime_seconds": 0  # Would calculate actual uptime
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/orchestrator/optimize")
async def optimize_orchestrator(
    current_user: User = Depends(get_current_user)
):
    """Trigger orchestrator optimization."""
    
    try:
        # Trigger self-improvement for all agents
        for agent in orchestrator.agents.values():
            await agent.self_improve()
        
        # Optimize task distribution
        await orchestrator.optimize_task_distribution()
        
        return {
            "status": "optimization_triggered",
            "message": "Orchestrator optimization initiated",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to optimize orchestrator: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/orchestrator/status")
async def get_orchestrator_status(
    current_user: User = Depends(get_current_user)
):
    """Get overall orchestrator status."""
    
    try:
        total_agents = len(orchestrator.agents)
        active_agents = len([a for a in orchestrator.agents.values() if a.status.value == "active"])
        total_tasks = sum(len(a.current_tasks) for a in orchestrator.agents.values())
        
        return {
            "total_agents": total_agents,
            "active_agents": active_agents,
            "total_active_tasks": total_tasks,
            "orchestrator_status": "healthy" if active_agents > 0 else "degraded",
            "message_bus_status": "connected",  # Would check actual status
            "last_optimization": datetime.now().isoformat(),  # Would track actual optimization
            "performance_summary": {
                "average_task_completion_time": 0.0,  # Would calculate from metrics
                "success_rate": 0.95,  # Would calculate from actual data
                "throughput_tasks_per_hour": 0.0  # Would calculate from metrics
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get orchestrator status: {e}")
        raise HTTPException(status_code=500, detail=str(e))
