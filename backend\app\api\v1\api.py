"""
API v1 router for the Enterprise AI/ML Platform.

This module aggregates all v1 API endpoints into a single router.
"""

from fastapi import APIRouter

from .auth import router as auth_router
from .agents import router as agents_router
from .workflows import router as workflows_router
from .models import router as models_router
from .data import router as data_router
from .marketplace import router as marketplace_router
from .monitoring import router as monitoring_router

# Create main API router
api_router = APIRouter()

# Include all sub-routers
api_router.include_router(
    auth_router,
    prefix="/auth",
    tags=["authentication"]
)

api_router.include_router(
    agents_router,
    prefix="/agents",
    tags=["agents"]
)

api_router.include_router(
    workflows_router,
    prefix="/workflows",
    tags=["workflows"]
)

api_router.include_router(
    models_router,
    prefix="/models",
    tags=["models"]
)

api_router.include_router(
    data_router,
    prefix="/data",
    tags=["data"]
)

api_router.include_router(
    marketplace_router,
    prefix="/marketplace",
    tags=["marketplace"]
)

api_router.include_router(
    monitoring_router,
    prefix="/monitoring",
    tags=["monitoring"]
)


@api_router.get("/")
async def api_info():
    """Get API information."""
    return {
        "name": "Enterprise AI/ML Platform API",
        "version": "v1",
        "description": "Production-ready API for comprehensive AI/ML operations",
        "endpoints": {
            "auth": "/api/v1/auth",
            "agents": "/api/v1/agents",
            "workflows": "/api/v1/workflows",
            "models": "/api/v1/models",
            "data": "/api/v1/data",
            "marketplace": "/api/v1/marketplace",
            "monitoring": "/api/v1/monitoring"
        },
        "documentation": {
            "swagger": "/api/docs",
            "redoc": "/api/redoc",
            "openapi": "/api/openapi.json"
        }
    }
