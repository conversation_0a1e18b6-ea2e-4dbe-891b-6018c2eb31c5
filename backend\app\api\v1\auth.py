"""
Authentication API Endpoints
============================

Comprehensive authentication endpoints with JWT, OAuth2, and security features.
"""

from datetime import timedelta
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_postgres_session
from app.core.security import security_manager, get_current_user, get_current_active_user
from app.models.user import (
    User, UserCreate, UserLogin, UserResponse, TokenResponse, 
    UserPasswordChange, AuditLogResponse
)
from app.services.user_service import UserService
from app.services.email_service import EmailService
from app.services.audit_service import AuditService
from app.core.logging import logger


router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    request: Request,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_postgres_session)
):
    """Register a new user."""
    user_service = UserService(db)
    audit_service = AuditService(db)
    email_service = EmailService()
    
    # Check if user already exists
    existing_user = await user_service.get_user_by_email(user_data.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    existing_username = await user_service.get_user_by_username(user_data.username)
    if existing_username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already taken"
        )
    
    try:
        # Create user
        user = await user_service.create_user(user_data)
        
        # Send verification email
        background_tasks.add_task(
            email_service.send_verification_email,
            user.email,
            user.first_name,
            user.id
        )
        
        # Log registration
        await audit_service.log_event(
            user_id=str(user.id),
            event_type="user_registration",
            action="create",
            resource_type="user",
            resource_id=str(user.id),
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent"),
            success=True
        )
        
        logger.info(f"User registered: {user.email}")
        return user
        
    except Exception as e:
        logger.error(f"Registration failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=TokenResponse)
async def login(
    request: Request,
    user_credentials: UserLogin,
    db: AsyncSession = Depends(get_postgres_session)
):
    """Authenticate user and return tokens."""
    user_service = UserService(db)
    audit_service = AuditService(db)
    
    # Get user
    user = await user_service.get_user_by_username(user_credentials.username)
    if not user:
        await audit_service.log_event(
            event_type="login_failed",
            action="authenticate",
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent"),
            success=False,
            metadata={"reason": "user_not_found", "username": user_credentials.username}
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials"
        )
    
    # Verify password
    if not security_manager.verify_password(user_credentials.password, user.hashed_password):
        await user_service.increment_failed_login_attempts(user.id)
        await audit_service.log_event(
            user_id=str(user.id),
            event_type="login_failed",
            action="authenticate",
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent"),
            success=False,
            metadata={"reason": "invalid_password"}
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials"
        )
    
    # Check if user is active
    if not user.is_active:
        await audit_service.log_event(
            user_id=str(user.id),
            event_type="login_failed",
            action="authenticate",
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent"),
            success=False,
            metadata={"reason": "user_inactive"}
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account is inactive"
        )
    
    # Check if account is locked
    if await user_service.is_account_locked(user.id):
        await audit_service.log_event(
            user_id=str(user.id),
            event_type="login_failed",
            action="authenticate",
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent"),
            success=False,
            metadata={"reason": "account_locked"}
        )
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail="Account is temporarily locked"
        )
    
    # Create tokens
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security_manager.create_access_token(
        subject=str(user.id),
        expires_delta=access_token_expires
    )
    refresh_token = security_manager.create_refresh_token(subject=str(user.id))
    
    # Update last login and reset failed attempts
    await user_service.update_last_login(user.id)
    await user_service.reset_failed_login_attempts(user.id)
    
    # Create session
    session_token = await user_service.create_session(
        user_id=user.id,
        access_token=access_token,
        refresh_token=refresh_token,
        ip_address=request.client.host,
        user_agent=request.headers.get("user-agent")
    )
    
    # Log successful login
    await audit_service.log_event(
        user_id=str(user.id),
        event_type="login_success",
        action="authenticate",
        ip_address=request.client.host,
        user_agent=request.headers.get("user-agent"),
        success=True
    )
    
    logger.info(f"User logged in: {user.email}")
    
    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=user
    )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    request: Request,
    refresh_token: str,
    db: AsyncSession = Depends(get_postgres_session)
):
    """Refresh access token using refresh token."""
    user_service = UserService(db)
    audit_service = AuditService(db)
    
    # Verify refresh token
    payload = security_manager.verify_token(refresh_token)
    if not payload or payload.get("type") != "refresh":
        await audit_service.log_event(
            event_type="token_refresh_failed",
            action="refresh",
            ip_address=request.client.host,
            success=False,
            metadata={"reason": "invalid_refresh_token"}
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    user_id = payload.get("sub")
    user = await user_service.get_user_by_id(user_id)
    if not user or not user.is_active:
        await audit_service.log_event(
            user_id=user_id,
            event_type="token_refresh_failed",
            action="refresh",
            ip_address=request.client.host,
            success=False,
            metadata={"reason": "user_not_found_or_inactive"}
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    # Create new access token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    new_access_token = security_manager.create_access_token(
        subject=str(user.id),
        expires_delta=access_token_expires
    )
    
    # Log token refresh
    await audit_service.log_event(
        user_id=str(user.id),
        event_type="token_refresh_success",
        action="refresh",
        ip_address=request.client.host,
        success=True
    )
    
    return TokenResponse(
        access_token=new_access_token,
        refresh_token=refresh_token,  # Keep the same refresh token
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=user
    )


@router.post("/logout")
async def logout(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_postgres_session)
):
    """Logout user and invalidate session."""
    user_service = UserService(db)
    audit_service = AuditService(db)
    
    # Invalidate all user sessions
    await user_service.invalidate_user_sessions(current_user.id)
    
    # Log logout
    await audit_service.log_event(
        user_id=str(current_user.id),
        event_type="logout",
        action="logout",
        ip_address=request.client.host,
        user_agent=request.headers.get("user-agent"),
        success=True
    )
    
    logger.info(f"User logged out: {current_user.email}")
    
    return {"message": "Successfully logged out"}


@router.post("/change-password")
async def change_password(
    request: Request,
    password_data: UserPasswordChange,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_postgres_session)
):
    """Change user password."""
    user_service = UserService(db)
    audit_service = AuditService(db)
    
    # Verify current password
    if not security_manager.verify_password(password_data.current_password, current_user.hashed_password):
        await audit_service.log_event(
            user_id=str(current_user.id),
            event_type="password_change_failed",
            action="update",
            ip_address=request.client.host,
            success=False,
            metadata={"reason": "invalid_current_password"}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid current password"
        )
    
    # Update password
    await user_service.update_password(current_user.id, password_data.new_password)
    
    # Invalidate all sessions except current
    await user_service.invalidate_user_sessions(current_user.id, exclude_current=True)
    
    # Log password change
    await audit_service.log_event(
        user_id=str(current_user.id),
        event_type="password_change_success",
        action="update",
        resource_type="user",
        resource_id=str(current_user.id),
        ip_address=request.client.host,
        success=True
    )
    
    logger.info(f"Password changed for user: {current_user.email}")
    
    return {"message": "Password changed successfully"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information."""
    return current_user


@router.get("/audit-logs", response_model=list[AuditLogResponse])
async def get_user_audit_logs(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_postgres_session)
):
    """Get user's audit logs."""
    audit_service = AuditService(db)
    logs = await audit_service.get_user_audit_logs(
        user_id=str(current_user.id),
        skip=skip,
        limit=limit
    )
    return logs
