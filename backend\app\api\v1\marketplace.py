"""
Marketplace API Endpoints for NeuroFlowAI
=========================================

REST API endpoints for marketplace functionality.
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Any, Dict, List, Optional
from datetime import datetime

from ...services.marketplace_service import MarketplaceService
from ...core.auth import get_current_user
from ...models.user import User
from ...core.database import get_db
from sqlalchemy.orm import Session

import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/marketplace")

# Initialize marketplace service
marketplace_service = MarketplaceService()


class ItemPublishRequest(BaseModel):
    """Item publish request model."""
    title: str
    description: str
    type: str
    category: str
    pricing_model: str
    price: Optional[float] = 0.0
    metadata: Optional[Dict[str, Any]] = {}


class PurchaseRequest(BaseModel):
    """Purchase request model."""
    payment_method: str


class ReviewRequest(BaseModel):
    """Review request model."""
    rating: int
    comment: str


@router.post("/items")
async def publish_item(
    item_request: ItemPublishRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Publish an item to the marketplace."""
    
    try:
        item_data = {
            "title": item_request.title,
            "description": item_request.description,
            "type": item_request.type,
            "category": item_request.category,
            "pricing_model": item_request.pricing_model,
            "price": item_request.price,
            "metadata": item_request.metadata
        }
        
        item_id = await marketplace_service.publish_item(
            current_user.id,
            item_data,
            db
        )
        
        return {
            "item_id": item_id,
            "status": "published",
            "message": "Item published successfully"
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to publish item: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/items")
async def search_items(
    q: Optional[str] = "",
    category: Optional[str] = None,
    pricing_model: Optional[str] = None,
    min_price: Optional[float] = None,
    max_price: Optional[float] = None,
    sort_by: str = "relevance",
    page: int = 1,
    page_size: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Search marketplace items."""
    
    try:
        filters = {}
        if category:
            filters["category"] = category
        if pricing_model:
            filters["pricing_model"] = pricing_model
        if min_price is not None:
            filters["min_price"] = min_price
        if max_price is not None:
            filters["max_price"] = max_price
        
        results = await marketplace_service.search_items(
            query=q,
            filters=filters,
            sort_by=sort_by,
            page=page,
            page_size=page_size,
            db=db
        )
        
        return results
        
    except Exception as e:
        logger.error(f"Failed to search items: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/items/{item_id}")
async def get_item(
    item_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed information about a marketplace item."""
    
    try:
        item_data = await marketplace_service._get_enriched_item_data(item_id, db)
        
        if not item_data:
            raise HTTPException(status_code=404, detail="Item not found")
        
        return item_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get item: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/items/{item_id}/purchase")
async def purchase_item(
    item_id: str,
    purchase_request: PurchaseRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Purchase a marketplace item."""
    
    try:
        result = await marketplace_service.purchase_item(
            current_user.id,
            item_id,
            purchase_request.payment_method,
            db
        )
        
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to purchase item: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/items/{item_id}/reviews")
async def submit_review(
    item_id: str,
    review_request: ReviewRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Submit a review for an item."""
    
    try:
        review_id = await marketplace_service.submit_review(
            current_user.id,
            item_id,
            review_request.rating,
            review_request.comment,
            db
        )
        
        return {
            "review_id": review_id,
            "status": "submitted",
            "message": "Review submitted successfully"
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to submit review: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recommendations")
async def get_recommendations(
    item_id: Optional[str] = None,
    limit: int = 10,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get personalized recommendations."""
    
    try:
        recommendations = await marketplace_service.get_recommendations(
            current_user.id,
            item_id,
            limit,
            db
        )
        
        return {
            "recommendations": recommendations,
            "total": len(recommendations)
        }
        
    except Exception as e:
        logger.error(f"Failed to get recommendations: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/analytics/publisher")
async def get_publisher_analytics(
    start_date: str,
    end_date: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get publisher analytics."""
    
    try:
        start_dt = datetime.fromisoformat(start_date)
        end_dt = datetime.fromisoformat(end_date)
        
        analytics = await marketplace_service.get_publisher_analytics(
            current_user.id,
            start_dt,
            end_dt,
            db
        )
        
        return analytics
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {e}")
    except Exception as e:
        logger.error(f"Failed to get publisher analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics")
async def get_marketplace_metrics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get overall marketplace metrics."""
    
    try:
        metrics = await marketplace_service.get_marketplace_metrics(db)
        
        return {
            "total_items": metrics.total_items,
            "total_publishers": metrics.total_publishers,
            "total_revenue": metrics.total_revenue,
            "monthly_active_users": metrics.monthly_active_users,
            "average_rating": metrics.average_rating,
            "top_categories": metrics.top_categories,
            "trending_items": metrics.trending_items
        }
        
    except Exception as e:
        logger.error(f"Failed to get marketplace metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/categories")
async def get_categories(current_user: User = Depends(get_current_user)):
    """Get available marketplace categories."""
    
    try:
        return {
            "categories": marketplace_service.categories
        }
        
    except Exception as e:
        logger.error(f"Failed to get categories: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/my-items")
async def get_my_items(
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get items published by the current user."""
    
    try:
        # This would be implemented in the marketplace service
        # For now, return a placeholder
        return {
            "items": [],
            "total": 0,
            "message": "No items found"
        }
        
    except Exception as e:
        logger.error(f"Failed to get user items: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/my-purchases")
async def get_my_purchases(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get items purchased by the current user."""
    
    try:
        # This would be implemented in the marketplace service
        # For now, return a placeholder
        return {
            "purchases": [],
            "total": 0,
            "message": "No purchases found"
        }
        
    except Exception as e:
        logger.error(f"Failed to get user purchases: {e}")
        raise HTTPException(status_code=500, detail=str(e))
