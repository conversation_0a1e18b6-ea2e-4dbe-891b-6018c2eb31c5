"""
Enterprise ML API Endpoints
===========================

FastAPI endpoints for the Enterprise AI/ML Platform.

Features:
- Computer Vision APIs
- NLP Processing APIs  
- Time Series Analysis APIs
- AutoML APIs
- Deep Learning APIs
- Health Monitoring APIs
- Authentication & Authorization
- Rate Limiting & Caching
- Comprehensive Error Handling
"""

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, BackgroundTasks
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Union
import asyncio
import logging
import json
import base64
from datetime import datetime
import numpy as np
from PIL import Image
import io

from app.services.enterprise_ml_service import ml_service
from app.core.security import verify_token
from app.core.rate_limiter import RateLimiter
from app.core.cache import cache_manager

# Initialize router
router = APIRouter(prefix="/api/v1/ml", tags=["Machine Learning"])
security = HTTPBearer()
rate_limiter = RateLimiter()

# Pydantic models for request/response
class MLTaskRequest(BaseModel):
    module: str = Field(..., description="ML module to use")
    task_type: str = Field(..., description="Type of task to execute")
    parameters: Dict[str, Any] = Field(..., description="Task parameters")
    cache_key: Optional[str] = Field(None, description="Cache key for result caching")

class MLTaskResponse(BaseModel):
    success: bool
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time: float
    timestamp: str
    cache_hit: bool = False

class ImageClassificationRequest(BaseModel):
    image_data: str = Field(..., description="Base64 encoded image")
    model_name: str = Field("resnet50", description="Model to use")
    top_k: int = Field(5, description="Number of top predictions")
    confidence_threshold: float = Field(0.0, description="Minimum confidence threshold")

class TextAnalysisRequest(BaseModel):
    text: str = Field(..., description="Text to analyze")
    task_type: str = Field("sentiment_analysis", description="Type of analysis")
    model_name: Optional[str] = Field(None, description="Specific model to use")

class TimeSeriesForecastRequest(BaseModel):
    data: List[float] = Field(..., description="Time series data")
    forecast_periods: int = Field(30, description="Number of periods to forecast")
    method: str = Field("prophet", description="Forecasting method")

class AutoMLRequest(BaseModel):
    X: List[List[float]] = Field(..., description="Feature matrix")
    y: List[Union[int, float]] = Field(..., description="Target values")
    task_type: str = Field("classification", description="ML task type")
    cv_folds: int = Field(5, description="Cross-validation folds")

class HealthResponse(BaseModel):
    status: str
    modules: Dict[str, Dict[str, Any]]
    system_info: Dict[str, Any]
    timestamp: str

# Authentication dependency
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify JWT token and return user info."""
    try:
        token = credentials.credentials
        user_info = verify_token(token)
        return user_info
    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid authentication token")

# Rate limiting dependency
async def check_rate_limit(user_info: dict = Depends(get_current_user)):
    """Check rate limits for the user."""
    user_id = user_info.get("user_id", "anonymous")
    if not await rate_limiter.check_limit(user_id):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    return user_info

@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Get system health status."""
    try:
        health_data = await ml_service.health_check()
        
        return HealthResponse(
            status=health_data.get("status", "unknown"),
            modules=health_data.get("modules", {}),
            system_info={
                "models_loaded": health_data.get("models_loaded", 0),
                "memory_usage": health_data.get("memory_usage", "N/A"),
                "uptime": "N/A"  # Would implement actual uptime tracking
            },
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logging.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

@router.get("/modules")
async def list_modules(user_info: dict = Depends(get_current_user)):
    """List all available ML modules and their capabilities."""
    try:
        modules_info = await ml_service.list_modules()
        return {
            "success": True,
            "modules": modules_info,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logging.error(f"Error listing modules: {e}")
        raise HTTPException(status_code=500, detail="Failed to list modules")

@router.post("/execute", response_model=MLTaskResponse)
async def execute_ml_task(
    request: MLTaskRequest,
    background_tasks: BackgroundTasks,
    user_info: dict = Depends(check_rate_limit)
):
    """Execute a general ML task."""
    try:
        # Check cache first
        cache_hit = False
        if request.cache_key:
            cached_result = await cache_manager.get(request.cache_key)
            if cached_result:
                return MLTaskResponse(
                    success=True,
                    result=cached_result,
                    processing_time=0.0,
                    timestamp=datetime.now().isoformat(),
                    cache_hit=True
                )
        
        # Execute task
        result = await ml_service.execute_task(
            module_name=request.module,
            task_type=request.task_type,
            parameters=request.parameters,
            user_id=user_info.get("user_id")
        )
        
        # Cache result if cache key provided
        if request.cache_key and result.get("success"):
            background_tasks.add_task(
                cache_manager.set,
                request.cache_key,
                result.get("result"),
                ttl=3600  # 1 hour cache
            )
        
        return MLTaskResponse(
            success=result.get("success", False),
            result=result.get("result"),
            error=result.get("error"),
            processing_time=result.get("processing_time", 0.0),
            timestamp=result.get("timestamp", datetime.now().isoformat()),
            cache_hit=cache_hit
        )
        
    except Exception as e:
        logging.error(f"Error executing ML task: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/computer-vision/classify")
async def classify_image(
    request: ImageClassificationRequest,
    user_info: dict = Depends(check_rate_limit)
):
    """Classify an image using computer vision models."""
    try:
        # Decode base64 image
        image_bytes = base64.b64decode(request.image_data)
        image = Image.open(io.BytesIO(image_bytes))
        
        # Convert PIL image to numpy array
        image_array = np.array(image)
        
        # Execute classification
        result = await ml_service.execute_task(
            module_name="computer_vision",
            task_type="classify_image",
            parameters={
                "image": image_array,
                "model": request.model_name,
                "top_k": request.top_k,
                "confidence_threshold": request.confidence_threshold
            },
            user_id=user_info.get("user_id")
        )
        
        return result
        
    except Exception as e:
        logging.error(f"Error in image classification: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/computer-vision/detect")
async def detect_objects(
    file: UploadFile = File(...),
    model: str = "yolov8n",
    confidence: float = 0.5,
    user_info: dict = Depends(check_rate_limit)
):
    """Detect objects in an uploaded image."""
    try:
        # Read and process uploaded file
        image_bytes = await file.read()
        image = Image.open(io.BytesIO(image_bytes))
        image_array = np.array(image)
        
        # Execute object detection
        result = await ml_service.execute_task(
            module_name="computer_vision",
            task_type="detect_objects",
            parameters={
                "image": image_array,
                "model": model,
                "confidence": confidence
            },
            user_id=user_info.get("user_id")
        )
        
        return result
        
    except Exception as e:
        logging.error(f"Error in object detection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/nlp/analyze")
async def analyze_text(
    request: TextAnalysisRequest,
    user_info: dict = Depends(check_rate_limit)
):
    """Analyze text using NLP models."""
    try:
        # Map task types to internal methods
        task_mapping = {
            "sentiment_analysis": "analyze_sentiment",
            "text_classification": "classify_text",
            "named_entity_recognition": "extract_entities",
            "text_summarization": "summarize_text",
            "text_generation": "generate_text"
        }
        
        internal_task = task_mapping.get(request.task_type, request.task_type)
        
        parameters = {"text": request.text}
        if request.model_name:
            parameters["model"] = request.model_name
        
        result = await ml_service.execute_task(
            module_name="nlp",
            task_type=internal_task,
            parameters=parameters,
            user_id=user_info.get("user_id")
        )
        
        return result
        
    except Exception as e:
        logging.error(f"Error in text analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/time-series/forecast")
async def forecast_time_series(
    request: TimeSeriesForecastRequest,
    user_info: dict = Depends(check_rate_limit)
):
    """Forecast time series data."""
    try:
        result = await ml_service.execute_task(
            module_name="time_series",
            task_type="forecast",
            parameters={
                "data": request.data,
                "forecast_periods": request.forecast_periods,
                "method": request.method
            },
            user_id=user_info.get("user_id")
        )
        
        return result
        
    except Exception as e:
        logging.error(f"Error in time series forecasting: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/time-series/detect-anomalies")
async def detect_anomalies(
    data: List[float],
    method: str = "isolation_forest",
    contamination: float = 0.1,
    user_info: dict = Depends(check_rate_limit)
):
    """Detect anomalies in time series data."""
    try:
        result = await ml_service.execute_task(
            module_name="time_series",
            task_type="detect_anomalies",
            parameters={
                "data": data,
                "method": method,
                "contamination": contamination
            },
            user_id=user_info.get("user_id")
        )
        
        return result
        
    except Exception as e:
        logging.error(f"Error in anomaly detection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/automl/select-model")
async def select_best_model(
    request: AutoMLRequest,
    user_info: dict = Depends(check_rate_limit)
):
    """Automatically select the best model for the given data."""
    try:
        result = await ml_service.execute_task(
            module_name="automl",
            task_type="select_model",
            parameters={
                "X": request.X,
                "y": request.y,
                "task_type": request.task_type,
                "cv_folds": request.cv_folds
            },
            user_id=user_info.get("user_id")
        )
        
        return result
        
    except Exception as e:
        logging.error(f"Error in model selection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/automl/optimize-hyperparameters")
async def optimize_hyperparameters(
    X: List[List[float]],
    y: List[Union[int, float]],
    model_name: str,
    method: str = "random_search",
    n_trials: int = 100,
    user_info: dict = Depends(check_rate_limit)
):
    """Optimize hyperparameters for a specific model."""
    try:
        result = await ml_service.execute_task(
            module_name="automl",
            task_type="optimize_hyperparameters",
            parameters={
                "X": X,
                "y": y,
                "model_name": model_name,
                "method": method,
                "n_trials": n_trials
            },
            user_id=user_info.get("user_id")
        )
        
        return result
        
    except Exception as e:
        logging.error(f"Error in hyperparameter optimization: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/deep-learning/train")
async def train_deep_model(
    model_name: str,
    X_train: List[List[float]],
    y_train: List[Union[int, float]],
    epochs: int = 10,
    batch_size: int = 32,
    learning_rate: float = 0.001,
    user_info: dict = Depends(check_rate_limit)
):
    """Train a deep learning model."""
    try:
        result = await ml_service.execute_task(
            module_name="deep_learning",
            task_type="train_model",
            parameters={
                "model_name": model_name,
                "X_train": X_train,
                "y_train": y_train,
                "epochs": epochs,
                "batch_size": batch_size,
                "learning_rate": learning_rate
            },
            user_id=user_info.get("user_id")
        )
        
        return result
        
    except Exception as e:
        logging.error(f"Error in deep learning training: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/deep-learning/inference")
async def deep_learning_inference(
    model_name: str,
    X: List[List[float]],
    user_info: dict = Depends(check_rate_limit)
):
    """Perform inference with a trained deep learning model."""
    try:
        result = await ml_service.execute_task(
            module_name="deep_learning",
            task_type="inference",
            parameters={
                "model_name": model_name,
                "X": X
            },
            user_id=user_info.get("user_id")
        )
        
        return result
        
    except Exception as e:
        logging.error(f"Error in deep learning inference: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/deep-learning/create-network")
async def create_custom_network(
    network_type: str,
    architecture_config: Dict[str, Any],
    model_name: Optional[str] = None,
    user_info: dict = Depends(check_rate_limit)
):
    """Create a custom neural network architecture."""
    try:
        result = await ml_service.execute_task(
            module_name="deep_learning",
            task_type="create_custom_network",
            parameters={
                "network_type": network_type,
                "architecture_config": architecture_config,
                "model_name": model_name
            },
            user_id=user_info.get("user_id")
        )
        
        return result
        
    except Exception as e:
        logging.error(f"Error creating custom network: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Error handlers
@router.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return {
        "success": False,
        "error": exc.detail,
        "status_code": exc.status_code,
        "timestamp": datetime.now().isoformat()
    }

@router.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logging.error(f"Unhandled exception in ML API: {exc}")
    return {
        "success": False,
        "error": "Internal server error",
        "status_code": 500,
        "timestamp": datetime.now().isoformat()
    }
