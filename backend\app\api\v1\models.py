"""
Models API Endpoints for NeuroFlowAI
====================================

REST API endpoints for AI model management and deployment.
"""

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from pydantic import BaseModel
from typing import Any, Dict, List, Optional
from datetime import datetime
import json

from ...services.inference_engine import InferenceEngine
from ...core.auth import get_current_user
from ...models.user import User

import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/models")

# Initialize inference engine
inference_engine = InferenceEngine()


class ModelInfo(BaseModel):
    """Model information response model."""
    model_id: str
    name: str
    description: str
    framework: str
    version: str
    status: str
    created_at: datetime
    updated_at: Optional[datetime]
    performance_metrics: Dict[str, Any]
    deployment_config: Dict[str, Any]


class ModelDeploymentRequest(BaseModel):
    """Model deployment request."""
    model_id: str
    deployment_config: Dict[str, Any]
    auto_scale: bool = True
    min_replicas: int = 1
    max_replicas: int = 10


class PredictionRequest(BaseModel):
    """Prediction request model."""
    inputs: Dict[str, Any]
    model_version: Optional[str] = None
    return_probabilities: bool = False


@router.get("/", response_model=List[ModelInfo])
async def list_models(
    status: Optional[str] = None,
    framework: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Get list of all models."""
    
    try:
        models = []
        
        for model_id, model_data in inference_engine.models.items():
            # Filter by status if provided
            if status and model_data.get("status") != status:
                continue
            
            # Filter by framework if provided
            if framework and model_data.get("framework") != framework:
                continue
            
            model_info = ModelInfo(
                model_id=model_id,
                name=model_data.get("name", model_id),
                description=model_data.get("description", ""),
                framework=model_data.get("framework", "unknown"),
                version=model_data.get("version", "1.0.0"),
                status=model_data.get("status", "unknown"),
                created_at=model_data.get("created_at", datetime.now()),
                updated_at=model_data.get("updated_at"),
                performance_metrics=model_data.get("performance_metrics", {}),
                deployment_config=model_data.get("deployment_config", {})
            )
            models.append(model_info)
        
        return models
        
    except Exception as e:
        logger.error(f"Failed to list models: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{model_id}", response_model=ModelInfo)
async def get_model(
    model_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get detailed information about a specific model."""
    
    try:
        if model_id not in inference_engine.models:
            raise HTTPException(status_code=404, detail="Model not found")
        
        model_data = inference_engine.models[model_id]
        
        return ModelInfo(
            model_id=model_id,
            name=model_data.get("name", model_id),
            description=model_data.get("description", ""),
            framework=model_data.get("framework", "unknown"),
            version=model_data.get("version", "1.0.0"),
            status=model_data.get("status", "unknown"),
            created_at=model_data.get("created_at", datetime.now()),
            updated_at=model_data.get("updated_at"),
            performance_metrics=model_data.get("performance_metrics", {}),
            deployment_config=model_data.get("deployment_config", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload")
async def upload_model(
    file: UploadFile = File(...),
    model_name: str = "",
    description: str = "",
    framework: str = "",
    current_user: User = Depends(get_current_user)
):
    """Upload a new model."""
    
    try:
        # Read file content
        content = await file.read()
        
        # Generate model ID
        model_id = f"model_{int(datetime.now().timestamp())}"
        
        # Store model (simplified - in production would use proper storage)
        model_data = {
            "name": model_name or file.filename,
            "description": description,
            "framework": framework,
            "version": "1.0.0",
            "status": "uploaded",
            "created_at": datetime.now(),
            "owner_id": current_user.id,
            "file_size": len(content),
            "file_name": file.filename
        }
        
        # Add to inference engine
        inference_engine.models[model_id] = model_data
        
        return {
            "model_id": model_id,
            "status": "uploaded",
            "message": f"Model {model_name} uploaded successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to upload model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{model_id}/deploy")
async def deploy_model(
    model_id: str,
    deployment_request: ModelDeploymentRequest,
    current_user: User = Depends(get_current_user)
):
    """Deploy a model for inference."""
    
    try:
        if model_id not in inference_engine.models:
            raise HTTPException(status_code=404, detail="Model not found")
        
        # Deploy model
        deployment_result = await inference_engine.deploy_model(
            model_id,
            deployment_request.deployment_config
        )
        
        # Update model status
        inference_engine.models[model_id]["status"] = "deployed"
        inference_engine.models[model_id]["deployment_config"] = deployment_request.deployment_config
        inference_engine.models[model_id]["updated_at"] = datetime.now()
        
        return {
            "model_id": model_id,
            "status": "deployed",
            "endpoint_url": f"/api/v1/models/{model_id}/predict",
            "deployment_id": deployment_result.get("deployment_id"),
            "message": "Model deployed successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to deploy model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{model_id}/predict")
async def predict(
    model_id: str,
    prediction_request: PredictionRequest,
    current_user: User = Depends(get_current_user)
):
    """Make predictions using a deployed model."""
    
    try:
        if model_id not in inference_engine.models:
            raise HTTPException(status_code=404, detail="Model not found")
        
        model_data = inference_engine.models[model_id]
        if model_data.get("status") != "deployed":
            raise HTTPException(status_code=400, detail="Model is not deployed")
        
        # Make prediction
        prediction_result = await inference_engine.predict(
            model_id,
            prediction_request.inputs,
            return_probabilities=prediction_request.return_probabilities
        )
        
        return {
            "model_id": model_id,
            "prediction": prediction_result.get("prediction"),
            "probabilities": prediction_result.get("probabilities") if prediction_request.return_probabilities else None,
            "confidence": prediction_result.get("confidence"),
            "processing_time_ms": prediction_result.get("processing_time_ms"),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to make prediction: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{model_id}")
async def delete_model(
    model_id: str,
    current_user: User = Depends(get_current_user)
):
    """Delete a model."""
    
    try:
        if model_id not in inference_engine.models:
            raise HTTPException(status_code=404, detail="Model not found")
        
        model_data = inference_engine.models[model_id]
        
        # Check ownership (simplified)
        if model_data.get("owner_id") != current_user.id and not current_user.is_admin:
            raise HTTPException(status_code=403, detail="Not authorized to delete this model")
        
        # Undeploy if deployed
        if model_data.get("status") == "deployed":
            await inference_engine.undeploy_model(model_id)
        
        # Remove from inference engine
        del inference_engine.models[model_id]
        
        return {
            "model_id": model_id,
            "status": "deleted",
            "message": "Model deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{model_id}/metrics")
async def get_model_metrics(
    model_id: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Get performance metrics for a model."""
    
    try:
        if model_id not in inference_engine.models:
            raise HTTPException(status_code=404, detail="Model not found")
        
        # Get metrics (simplified - would integrate with monitoring system)
        metrics = {
            "model_id": model_id,
            "total_predictions": 1000,  # Would get from actual metrics
            "average_latency_ms": 45.2,
            "error_rate": 0.02,
            "throughput_per_second": 23.5,
            "accuracy": 0.94,
            "resource_usage": {
                "cpu_utilization": 0.65,
                "memory_usage_mb": 512,
                "gpu_utilization": 0.78
            },
            "prediction_distribution": {
                "last_24h": 245,
                "last_7d": 1680,
                "last_30d": 7200
            }
        }
        
        return metrics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get model metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{model_id}/scale")
async def scale_model(
    model_id: str,
    replicas: int,
    current_user: User = Depends(get_current_user)
):
    """Scale model deployment."""
    
    try:
        if model_id not in inference_engine.models:
            raise HTTPException(status_code=404, detail="Model not found")
        
        model_data = inference_engine.models[model_id]
        if model_data.get("status") != "deployed":
            raise HTTPException(status_code=400, detail="Model is not deployed")
        
        if replicas < 1 or replicas > 20:
            raise HTTPException(status_code=400, detail="Replicas must be between 1 and 20")
        
        # Scale deployment (simplified)
        await inference_engine.scale_model(model_id, replicas)
        
        return {
            "model_id": model_id,
            "replicas": replicas,
            "status": "scaled",
            "message": f"Model scaled to {replicas} replicas"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to scale model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{model_id}/logs")
async def get_model_logs(
    model_id: str,
    lines: int = 100,
    current_user: User = Depends(get_current_user)
):
    """Get logs for a deployed model."""
    
    try:
        if model_id not in inference_engine.models:
            raise HTTPException(status_code=404, detail="Model not found")
        
        # Get logs (simplified - would integrate with logging system)
        logs = [
            {
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": f"Model {model_id} prediction completed",
                "request_id": "req_123"
            },
            {
                "timestamp": datetime.now().isoformat(),
                "level": "INFO", 
                "message": f"Model {model_id} health check passed",
                "request_id": "health_456"
            }
        ]
        
        return {
            "model_id": model_id,
            "logs": logs[-lines:],
            "total_lines": len(logs)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get model logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))
