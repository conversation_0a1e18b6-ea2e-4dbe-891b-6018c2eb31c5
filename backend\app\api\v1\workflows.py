"""
Workflow API Endpoints for NeuroFlowAI
======================================

REST API endpoints for workflow management and execution.
Integrates with the agent orchestrator and provides real-time updates.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Any, Dict, List, Optional
from datetime import datetime
import json
import asyncio

from ...services.agent_orchestrator import AgentOrchestrator
from ...services.inference_engine import InferenceEngine
from ...services.api_gateway import APIGateway
from ...core.auth import get_current_user
from ...models.user import User

import logging

logger = logging.getLogger(__name__)

# Initialize services (these would be dependency injected in production)
orchestrator = AgentOrchestrator()
inference_engine = InferenceEngine()
api_gateway = APIGateway(inference_engine)

router = APIRouter(prefix="/workflows", tags=["workflows"])


# Pydantic models for request/response
class WorkflowNodeConfig(BaseModel):
    """Workflow node configuration."""
    id: str
    type: str
    position: Dict[str, float]
    data: Dict[str, Any]


class WorkflowEdgeConfig(BaseModel):
    """Workflow edge configuration."""
    id: str
    source: str
    target: str
    type: Optional[str] = "default"


class WorkflowDefinition(BaseModel):
    """Complete workflow definition."""
    name: str
    description: Optional[str] = ""
    nodes: List[WorkflowNodeConfig]
    edges: List[WorkflowEdgeConfig]
    parameters: Optional[Dict[str, Any]] = {}
    tags: Optional[List[str]] = []


class WorkflowExecutionRequest(BaseModel):
    """Workflow execution request."""
    workflow_definition: WorkflowDefinition
    execution_parameters: Optional[Dict[str, Any]] = {}
    priority: Optional[int] = 1
    timeout_minutes: Optional[int] = 60


class WorkflowResponse(BaseModel):
    """Workflow response model."""
    workflow_id: str
    status: str
    progress: float
    current_stage: str
    start_time: datetime
    end_time: Optional[datetime] = None
    results: Optional[Dict[str, Any]] = {}
    error: Optional[str] = None


class WorkflowListResponse(BaseModel):
    """Workflow list response."""
    workflows: List[WorkflowResponse]
    total: int
    page: int
    page_size: int


@router.post("/", response_model=Dict[str, str])
async def create_workflow(
    workflow_request: WorkflowExecutionRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """
    Create and execute a new AI workflow.
    
    This endpoint creates a new workflow from the provided definition
    and starts its execution using the agent orchestrator.
    """
    try:
        # Validate workflow definition
        validation_result = await validate_workflow_definition(workflow_request.workflow_definition)
        if not validation_result["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid workflow definition: {validation_result['errors']}"
            )
        
        # Convert to orchestrator format
        workflow_definition = {
            "name": workflow_request.workflow_definition.name,
            "description": workflow_request.workflow_definition.description,
            "nodes": [node.dict() for node in workflow_request.workflow_definition.nodes],
            "edges": [edge.dict() for edge in workflow_request.workflow_definition.edges],
            "parameters": workflow_request.workflow_definition.parameters,
            "execution_parameters": workflow_request.execution_parameters,
            "user_id": current_user.id,
            "priority": workflow_request.priority,
            "timeout_minutes": workflow_request.timeout_minutes,
        }
        
        # Start workflow execution
        workflow_id = await orchestrator.execute_workflow(
            workflow_definition, 
            user_id=current_user.id
        )
        
        logger.info(f"Started workflow {workflow_id} for user {current_user.id}")
        
        return {
            "workflow_id": workflow_id,
            "status": "started",
            "message": "Workflow execution started successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to create workflow: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=WorkflowListResponse)
async def list_workflows(
    page: int = 1,
    page_size: int = 20,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """
    List workflows for the current user.
    
    Returns a paginated list of workflows with optional status filtering.
    """
    try:
        # Get workflows from orchestrator
        all_workflows = []
        
        # Get active workflows
        for workflow_id, workflow in orchestrator.active_workflows.items():
            if workflow["user_id"] == current_user.id:
                if not status or workflow["status"].value == status:
                    all_workflows.append(_format_workflow_response(workflow))
        
        # Get workflow history
        for workflow in orchestrator.workflow_history:
            if workflow["user_id"] == current_user.id:
                workflow_status = workflow["status"].value if hasattr(workflow["status"], "value") else workflow["status"]
                if not status or workflow_status == status:
                    all_workflows.append(_format_workflow_response(workflow))
        
        # Sort by start time (newest first)
        all_workflows.sort(key=lambda w: w.start_time, reverse=True)
        
        # Paginate
        total = len(all_workflows)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        workflows = all_workflows[start_idx:end_idx]
        
        return WorkflowListResponse(
            workflows=workflows,
            total=total,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        logger.error(f"Failed to list workflows: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow(
    workflow_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed information about a specific workflow.
    """
    try:
        workflow_status = await orchestrator.get_workflow_status(workflow_id)
        
        # Check if user has access to this workflow
        workflow_data = None
        if workflow_id in orchestrator.active_workflows:
            workflow_data = orchestrator.active_workflows[workflow_id]
        else:
            for workflow in orchestrator.workflow_history:
                if workflow["workflow_id"] == workflow_id:
                    workflow_data = workflow
                    break
        
        if not workflow_data or workflow_data["user_id"] != current_user.id:
            raise HTTPException(status_code=404, detail="Workflow not found")
        
        return _format_workflow_response(workflow_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get workflow {workflow_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{workflow_id}/stream")
async def stream_workflow_progress(
    workflow_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Stream real-time workflow execution progress.
    
    Returns a Server-Sent Events (SSE) stream with workflow updates.
    """
    try:
        # Verify workflow access
        workflow_data = None
        if workflow_id in orchestrator.active_workflows:
            workflow_data = orchestrator.active_workflows[workflow_id]
        
        if not workflow_data or workflow_data["user_id"] != current_user.id:
            raise HTTPException(status_code=404, detail="Workflow not found")
        
        async def event_stream():
            """Generate SSE events for workflow progress."""
            last_progress = -1
            last_stage = ""
            
            while True:
                try:
                    # Get current workflow status
                    if workflow_id in orchestrator.active_workflows:
                        workflow = orchestrator.active_workflows[workflow_id]
                        
                        # Check if progress or stage changed
                        current_progress = workflow.get("progress", 0)
                        current_stage = workflow.get("current_stage", "")
                        
                        if current_progress != last_progress or current_stage != last_stage:
                            event_data = {
                                "type": "progress_update",
                                "workflow_id": workflow_id,
                                "progress": current_progress,
                                "stage": current_stage,
                                "status": workflow["status"].value if hasattr(workflow["status"], "value") else workflow["status"],
                                "timestamp": datetime.now().isoformat(),
                            }
                            
                            yield f"data: {json.dumps(event_data)}\n\n"
                            
                            last_progress = current_progress
                            last_stage = current_stage
                        
                        # Check if workflow completed
                        if workflow["status"].value in ["completed", "failed", "cancelled"]:
                            completion_event = {
                                "type": "workflow_completed" if workflow["status"].value == "completed" else "workflow_failed",
                                "workflow_id": workflow_id,
                                "status": workflow["status"].value,
                                "results": workflow.get("results", {}),
                                "error": workflow.get("error"),
                                "timestamp": datetime.now().isoformat(),
                            }
                            
                            yield f"data: {json.dumps(completion_event)}\n\n"
                            break
                    else:
                        # Workflow no longer active, check if it's in history
                        for hist_workflow in orchestrator.workflow_history:
                            if hist_workflow["workflow_id"] == workflow_id:
                                completion_event = {
                                    "type": "workflow_completed",
                                    "workflow_id": workflow_id,
                                    "status": hist_workflow["status"].value if hasattr(hist_workflow["status"], "value") else hist_workflow["status"],
                                    "results": hist_workflow.get("results", {}),
                                    "timestamp": datetime.now().isoformat(),
                                }
                                
                                yield f"data: {json.dumps(completion_event)}\n\n"
                                break
                        break
                    
                    # Wait before next check
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    error_event = {
                        "type": "error",
                        "workflow_id": workflow_id,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat(),
                    }
                    
                    yield f"data: {json.dumps(error_event)}\n\n"
                    break
        
        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stream workflow {workflow_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{workflow_id}/cancel")
async def cancel_workflow(
    workflow_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Cancel a running workflow.
    """
    try:
        # Check if workflow exists and user has access
        if workflow_id not in orchestrator.active_workflows:
            raise HTTPException(status_code=404, detail="Workflow not found or not running")
        
        workflow = orchestrator.active_workflows[workflow_id]
        if workflow["user_id"] != current_user.id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Cancel workflow (simplified - in production would need proper cancellation logic)
        workflow["status"] = "cancelled"
        workflow["end_time"] = datetime.now()
        
        # Move to history
        orchestrator.workflow_history.append(workflow)
        del orchestrator.active_workflows[workflow_id]
        
        logger.info(f"Cancelled workflow {workflow_id} for user {current_user.id}")
        
        return {
            "workflow_id": workflow_id,
            "status": "cancelled",
            "message": "Workflow cancelled successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel workflow {workflow_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{workflow_id}/results")
async def get_workflow_results(
    workflow_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed results from a completed workflow.
    """
    try:
        # Find workflow
        workflow_data = None
        if workflow_id in orchestrator.active_workflows:
            workflow_data = orchestrator.active_workflows[workflow_id]
        else:
            for workflow in orchestrator.workflow_history:
                if workflow["workflow_id"] == workflow_id:
                    workflow_data = workflow
                    break
        
        if not workflow_data or workflow_data["user_id"] != current_user.id:
            raise HTTPException(status_code=404, detail="Workflow not found")
        
        # Check if workflow is completed
        status = workflow_data["status"].value if hasattr(workflow_data["status"], "value") else workflow_data["status"]
        if status not in ["completed", "failed"]:
            raise HTTPException(status_code=400, detail="Workflow not yet completed")
        
        return {
            "workflow_id": workflow_id,
            "status": status,
            "results": workflow_data.get("results", {}),
            "logs": workflow_data.get("logs", []),
            "agent_tasks": workflow_data.get("agent_tasks", {}),
            "execution_time_seconds": (
                (workflow_data["end_time"] - workflow_data["start_time"]).total_seconds()
                if workflow_data.get("end_time") and workflow_data.get("start_time")
                else None
            ),
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get workflow results {workflow_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate")
async def validate_workflow(
    workflow_definition: WorkflowDefinition,
    current_user: User = Depends(get_current_user)
):
    """
    Validate a workflow definition without executing it.
    """
    try:
        validation_result = await validate_workflow_definition(workflow_definition)
        
        return {
            "valid": validation_result["valid"],
            "errors": validation_result["errors"],
            "warnings": validation_result.get("warnings", []),
            "estimated_execution_time_minutes": validation_result.get("estimated_time", 0),
            "estimated_cost_usd": validation_result.get("estimated_cost", 0.0),
        }
        
    except Exception as e:
        logger.error(f"Failed to validate workflow: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Helper functions

async def validate_workflow_definition(workflow_def: WorkflowDefinition) -> Dict[str, Any]:
    """Validate workflow definition and return validation results."""
    
    errors = []
    warnings = []
    
    # Check for required components
    if not workflow_def.nodes:
        errors.append("Workflow must have at least one node")
    
    # Check for data sources
    data_sources = [node for node in workflow_def.nodes if node.type == "dataSource"]
    if not data_sources:
        errors.append("Workflow must have at least one data source")
    
    # Check for outputs
    outputs = [node for node in workflow_def.nodes if node.type == "output"]
    if not outputs:
        warnings.append("Workflow has no output nodes")
    
    # Check node connections
    node_ids = {node.id for node in workflow_def.nodes}
    for edge in workflow_def.edges:
        if edge.source not in node_ids:
            errors.append(f"Edge references unknown source node: {edge.source}")
        if edge.target not in node_ids:
            errors.append(f"Edge references unknown target node: {edge.target}")
    
    # Check for disconnected nodes
    connected_nodes = set()
    for edge in workflow_def.edges:
        connected_nodes.add(edge.source)
        connected_nodes.add(edge.target)
    
    disconnected = node_ids - connected_nodes
    if len(disconnected) > 1:  # Allow single disconnected node
        warnings.append(f"Disconnected nodes found: {list(disconnected)}")
    
    # Estimate execution time and cost (simplified)
    estimated_time = len(workflow_def.nodes) * 5  # 5 minutes per node
    estimated_cost = len(workflow_def.nodes) * 0.50  # $0.50 per node
    
    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "warnings": warnings,
        "estimated_time": estimated_time,
        "estimated_cost": estimated_cost,
    }


def _format_workflow_response(workflow_data: Dict[str, Any]) -> WorkflowResponse:
    """Format workflow data for API response."""
    
    status = workflow_data["status"]
    if hasattr(status, "value"):
        status = status.value
    
    return WorkflowResponse(
        workflow_id=workflow_data["workflow_id"],
        status=status,
        progress=workflow_data.get("progress", 0.0),
        current_stage=workflow_data.get("current_stage", ""),
        start_time=workflow_data["start_time"],
        end_time=workflow_data.get("end_time"),
        results=workflow_data.get("results", {}),
        error=workflow_data.get("error"),
    )


# Initialize services on startup
@router.on_event("startup")
async def startup_workflows():
    """Initialize workflow services."""
    await orchestrator.start()
    await inference_engine.start()
    logger.info("Workflow services started successfully")


@router.on_event("shutdown") 
async def shutdown_workflows():
    """Cleanup workflow services."""
    await orchestrator.stop()
    await inference_engine.stop()
    logger.info("Workflow services stopped successfully")
