"""
API Version 2
=============

Enhanced API endpoints with improved performance, better error handling,
and additional features for the AI/ML platform.
"""

from fastapi import APIRouter
from .enhanced_ml_endpoints import router as enhanced_ml_router
from .models_v2 import router as models_v2_router
from .analytics_v2 import router as analytics_v2_router

# Create v2 API router
api_v2_router = APIRouter(prefix="/v2", tags=["v2"])

# Include all v2 routers
api_v2_router.include_router(enhanced_ml_router, prefix="/ml", tags=["ml-v2"])
api_v2_router.include_router(models_v2_router, prefix="/models", tags=["models-v2"])
api_v2_router.include_router(analytics_v2_router, prefix="/analytics", tags=["analytics-v2"])

__all__ = ["api_v2_router"]
