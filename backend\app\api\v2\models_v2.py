"""
Enhanced Models API v2
=====================

Improved model management endpoints with better performance,
enhanced error handling, and additional features.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
import asyncio
from datetime import datetime

from app.core.security import get_current_user
from app.core.error_handling import handle_errors, error_context, create_circuit_breaker
from app.core.performance import performance_monitor, model_cache
from app.models.user import User
from app.services.enterprise_ml_service import ml_service

router = APIRouter()

# Enhanced request/response models
class ModelMetadataV2(BaseModel):
    """Enhanced model metadata."""
    id: str
    name: str
    version: str
    type: str
    framework: str
    created_at: datetime
    updated_at: datetime
    size_mb: float
    accuracy: Optional[float] = None
    performance_metrics: Dict[str, float] = Field(default_factory=dict)
    tags: List[str] = Field(default_factory=list)
    description: Optional[str] = None
    author: str
    status: str = "active"

class ModelListResponseV2(BaseModel):
    """Enhanced model list response."""
    models: List[ModelMetadataV2]
    total_count: int
    page: int
    page_size: int
    has_next: bool
    has_previous: bool
    filters_applied: Dict[str, Any] = Field(default_factory=dict)

class ModelDetailResponseV2(BaseModel):
    """Enhanced model detail response."""
    metadata: ModelMetadataV2
    performance_history: List[Dict[str, Any]] = Field(default_factory=list)
    usage_statistics: Dict[str, Any] = Field(default_factory=dict)
    deployment_info: Optional[Dict[str, Any]] = None
    related_models: List[str] = Field(default_factory=list)

class ModelTrainingRequestV2(BaseModel):
    """Enhanced model training request."""
    name: str = Field(..., min_length=1, max_length=100)
    type: str = Field(..., description="Model type (classification, regression, etc.)")
    framework: str = Field(default="autogluon", description="ML framework to use")
    dataset_id: str = Field(..., description="Dataset identifier")
    hyperparameters: Dict[str, Any] = Field(default_factory=dict)
    training_config: Dict[str, Any] = Field(default_factory=dict)
    tags: List[str] = Field(default_factory=list)
    description: Optional[str] = None
    auto_deploy: bool = Field(default=False, description="Auto-deploy after training")

# Circuit breaker for external model operations
model_circuit_breaker = create_circuit_breaker(
    failure_threshold=3,
    recovery_timeout=30
)

@router.get("/", response_model=ModelListResponseV2)
@performance_monitor
@handle_errors(reraise=True)
async def list_models_v2(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    model_type: Optional[str] = Query(None, description="Filter by model type"),
    framework: Optional[str] = Query(None, description="Filter by framework"),
    status: Optional[str] = Query(None, description="Filter by status"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    search: Optional[str] = Query(None, description="Search in name/description"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    current_user: User = Depends(get_current_user)
):
    """List models with enhanced filtering, pagination, and search."""
    async with error_context("list_models_v2", {"user_id": current_user.id}):
        # Build filters
        filters = {}
        if model_type:
            filters["type"] = model_type
        if framework:
            filters["framework"] = framework
        if status:
            filters["status"] = status
        if tags:
            filters["tags"] = tags
        if search:
            filters["search"] = search
        
        # Calculate offset
        offset = (page - 1) * page_size
        
        # Get models from service
        models_data = await ml_service.list_models_v2(
            user_id=current_user.id,
            filters=filters,
            offset=offset,
            limit=page_size,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        # Convert to response format
        models = [ModelMetadataV2(**model) for model in models_data["models"]]
        total_count = models_data["total_count"]
        
        return ModelListResponseV2(
            models=models,
            total_count=total_count,
            page=page,
            page_size=page_size,
            has_next=offset + page_size < total_count,
            has_previous=page > 1,
            filters_applied=filters
        )

@router.get("/{model_id}", response_model=ModelDetailResponseV2)
@performance_monitor
@handle_errors(reraise=True)
async def get_model_v2(
    model_id: str,
    include_history: bool = Query(True, description="Include performance history"),
    include_usage: bool = Query(True, description="Include usage statistics"),
    current_user: User = Depends(get_current_user)
):
    """Get detailed model information with enhanced data."""
    async with error_context("get_model_v2", {"model_id": model_id, "user_id": current_user.id}):
        # Get model from cache or service
        model_data = await model_cache.get_model(f"metadata_{model_id}")
        
        if not model_data:
            model_data = await ml_service.get_model_detail_v2(
                model_id=model_id,
                user_id=current_user.id,
                include_history=include_history,
                include_usage=include_usage
            )
            
            if not model_data:
                raise HTTPException(status_code=404, detail="Model not found")
        
        return ModelDetailResponseV2(**model_data)

@router.post("/train", response_model=Dict[str, Any])
@performance_monitor
@handle_errors(reraise=True)
async def train_model_v2(
    request: ModelTrainingRequestV2,
    background_tasks: BackgroundTasks,
    priority: str = Query("normal", regex="^(low|normal|high|urgent)$"),
    current_user: User = Depends(get_current_user)
):
    """Train a new model with enhanced configuration options."""
    async with error_context("train_model_v2", {"user_id": current_user.id}):
        # Validate training request
        await _validate_training_request(request, current_user)
        
        # Create training task
        task_id = await ml_service.create_training_task_v2(
            request=request.dict(),
            user_id=current_user.id,
            priority=priority
        )
        
        # Start background training
        background_tasks.add_task(
            _execute_training_v2,
            task_id=task_id,
            request=request,
            user_id=current_user.id
        )
        
        return {
            "task_id": task_id,
            "status": "queued",
            "message": "Model training queued successfully",
            "estimated_duration": await _estimate_training_duration(request),
            "priority": priority,
            "auto_deploy": request.auto_deploy
        }

@router.get("/{model_id}/performance", response_model=Dict[str, Any])
@performance_monitor
@handle_errors(reraise=True)
async def get_model_performance_v2(
    model_id: str,
    metric_type: Optional[str] = Query(None, description="Specific metric type"),
    time_range: str = Query("7d", regex="^(1h|1d|7d|30d|90d)$"),
    current_user: User = Depends(get_current_user)
):
    """Get detailed model performance metrics."""
    async with error_context("get_model_performance_v2", {"model_id": model_id}):
        performance_data = await ml_service.get_model_performance_v2(
            model_id=model_id,
            user_id=current_user.id,
            metric_type=metric_type,
            time_range=time_range
        )
        
        return performance_data

@router.post("/{model_id}/deploy", response_model=Dict[str, Any])
@model_circuit_breaker
@performance_monitor
@handle_errors(reraise=True)
async def deploy_model_v2(
    model_id: str,
    deployment_config: Dict[str, Any] = {},
    environment: str = Query("staging", regex="^(staging|production)$"),
    auto_scale: bool = Query(True, description="Enable auto-scaling"),
    current_user: User = Depends(get_current_user)
):
    """Deploy model with enhanced configuration options."""
    async with error_context("deploy_model_v2", {"model_id": model_id}):
        # Enhanced deployment configuration
        enhanced_config = {
            **deployment_config,
            "environment": environment,
            "auto_scale": auto_scale,
            "deployed_by": current_user.id,
            "deployment_time": datetime.now().isoformat()
        }
        
        deployment_result = await ml_service.deploy_model_v2(
            model_id=model_id,
            config=enhanced_config,
            user_id=current_user.id
        )
        
        return {
            "deployment_id": deployment_result["deployment_id"],
            "status": "deployed",
            "endpoint_url": deployment_result["endpoint_url"],
            "environment": environment,
            "auto_scale_enabled": auto_scale,
            "health_check_url": deployment_result.get("health_check_url"),
            "metrics_url": deployment_result.get("metrics_url")
        }

@router.delete("/{model_id}")
@performance_monitor
@handle_errors(reraise=True)
async def delete_model_v2(
    model_id: str,
    force: bool = Query(False, description="Force delete even if deployed"),
    current_user: User = Depends(get_current_user)
):
    """Delete model with safety checks."""
    async with error_context("delete_model_v2", {"model_id": model_id}):
        # Check if model is deployed
        if not force:
            deployment_status = await ml_service.check_model_deployment(model_id)
            if deployment_status.get("is_deployed"):
                raise HTTPException(
                    status_code=400,
                    detail="Model is currently deployed. Use force=true to delete anyway."
                )
        
        # Delete model
        await ml_service.delete_model_v2(model_id, current_user.id, force=force)
        
        return {"message": "Model deleted successfully", "model_id": model_id}

# Helper functions
async def _validate_training_request(request: ModelTrainingRequestV2, user: User):
    """Validate training request."""
    # Check dataset exists and user has access
    dataset_exists = await ml_service.check_dataset_access(request.dataset_id, user.id)
    if not dataset_exists:
        raise HTTPException(status_code=404, detail="Dataset not found or access denied")
    
    # Validate framework support
    supported_frameworks = await ml_service.get_supported_frameworks(request.type)
    if request.framework not in supported_frameworks:
        raise HTTPException(
            status_code=400,
            detail=f"Framework {request.framework} not supported for {request.type}"
        )

async def _estimate_training_duration(request: ModelTrainingRequestV2) -> str:
    """Estimate training duration based on request parameters."""
    # Simple estimation logic - in production, use more sophisticated methods
    base_time = 300  # 5 minutes base
    
    if request.framework == "autogluon":
        multiplier = 1.0
    elif request.framework == "pytorch":
        multiplier = 2.0
    else:
        multiplier = 1.5
    
    estimated_seconds = int(base_time * multiplier)
    
    if estimated_seconds < 3600:
        return f"{estimated_seconds // 60} minutes"
    else:
        return f"{estimated_seconds // 3600} hours"

async def _execute_training_v2(task_id: str, request: ModelTrainingRequestV2, user_id: str):
    """Execute model training in background."""
    try:
        # Update task status
        await ml_service.update_task_status(task_id, "training")
        
        # Simulate training process
        await asyncio.sleep(10)  # Replace with actual training logic
        
        # Update task status
        await ml_service.update_task_status(task_id, "completed")
        
        # Auto-deploy if requested
        if request.auto_deploy:
            await ml_service.auto_deploy_model(task_id, user_id)
            
    except Exception as e:
        await ml_service.update_task_status(task_id, "failed", str(e))
