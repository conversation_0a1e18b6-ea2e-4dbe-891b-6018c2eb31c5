"""
Command Line Interface for the Enterprise AI/ML Platform.

This module provides CLI commands for managing the platform,
including database operations, user management, and system administration.
"""

import asyncio
import sys
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm

from app.core.config import settings
from app.core.database import init_db, close_db
from app.core.logging import setup_logging, get_logger

# Initialize CLI app
app = typer.Typer(
    name="aiml-platform",
    help="Enterprise AI/ML Platform CLI",
    add_completion=False,
)

# Initialize console and logger
console = Console()
logger = get_logger(__name__)


@app.command()
def version():
    """Show version information."""
    console.print(f"Enterprise AI/ML Platform v{settings.VERSION}")
    console.print(f"Environment: {settings.ENVIRONMENT}")
    console.print(f"Python: {sys.version}")


@app.command()
def init_database():
    """Initialize the database with tables and initial data."""
    console.print("🚀 Initializing database...")
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Creating database tables...", total=None)
            
            # Initialize database
            asyncio.run(init_db())
            
            progress.update(task, description="Database initialized successfully!")
            
        console.print("✅ Database initialization completed!")
        
    except Exception as e:
        console.print(f"❌ Database initialization failed: {e}")
        raise typer.Exit(1)


@app.command()
def create_user(
    email: str = typer.Option(..., "--email", "-e", help="User email"),
    name: str = typer.Option(..., "--name", "-n", help="User name"),
    password: Optional[str] = typer.Option(None, "--password", "-p", help="User password"),
    role: str = typer.Option("user", "--role", "-r", help="User role"),
    admin: bool = typer.Option(False, "--admin", help="Create admin user"),
):
    """Create a new user."""
    console.print(f"👤 Creating user: {email}")
    
    # Prompt for password if not provided
    if not password:
        password = Prompt.ask("Enter password", password=True)
        confirm_password = Prompt.ask("Confirm password", password=True)
        
        if password != confirm_password:
            console.print("❌ Passwords do not match!")
            raise typer.Exit(1)
    
    # Set role to admin if admin flag is set
    if admin:
        role = "admin"
    
    try:
        # This would typically create a user in the database
        # For now, we'll just show what would be created
        
        table = Table(title="User Details")
        table.add_column("Field", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Email", email)
        table.add_row("Name", name)
        table.add_row("Role", role)
        table.add_row("Admin", "Yes" if admin else "No")
        
        console.print(table)
        
        if Confirm.ask("Create this user?"):
            # Here you would actually create the user
            console.print("✅ User created successfully!")
        else:
            console.print("❌ User creation cancelled.")
            
    except Exception as e:
        console.print(f"❌ User creation failed: {e}")
        raise typer.Exit(1)


@app.command()
def start_server(
    host: str = typer.Option("0.0.0.0", "--host", "-h", help="Host to bind to"),
    port: int = typer.Option(8000, "--port", "-p", help="Port to bind to"),
    reload: bool = typer.Option(False, "--reload", help="Enable auto-reload"),
    workers: int = typer.Option(1, "--workers", "-w", help="Number of worker processes"),
):
    """Start the FastAPI server."""
    console.print(f"🚀 Starting server on {host}:{port}")
    
    try:
        import uvicorn
        
        uvicorn.run(
            "app.main:app",
            host=host,
            port=port,
            reload=reload,
            workers=workers if not reload else 1,
            log_config=None,  # Use our custom logging
        )
        
    except Exception as e:
        console.print(f"❌ Server startup failed: {e}")
        raise typer.Exit(1)


@app.command()
def start_worker():
    """Start a Celery worker."""
    console.print("🔄 Starting Celery worker...")
    
    try:
        from app.tasks.ml_tasks import celery_app
        
        celery_app.worker_main([
            'worker',
            '--loglevel=info',
            '--concurrency=2',
        ])
        
    except Exception as e:
        console.print(f"❌ Worker startup failed: {e}")
        raise typer.Exit(1)


@app.command()
def start_scheduler():
    """Start the Celery beat scheduler."""
    console.print("⏰ Starting Celery beat scheduler...")
    
    try:
        from app.tasks.ml_tasks import celery_app
        
        celery_app.control.purge()  # Clear any pending tasks
        
        celery_app.start([
            'celery',
            'beat',
            '--loglevel=info',
        ])
        
    except Exception as e:
        console.print(f"❌ Scheduler startup failed: {e}")
        raise typer.Exit(1)


@app.command()
def health_check():
    """Perform a health check of the system."""
    console.print("🏥 Performing health check...")
    
    try:
        # This would perform actual health checks
        checks = {
            "Database": "✅ Connected",
            "Redis": "✅ Connected", 
            "ML Service": "✅ Running",
            "File Storage": "✅ Available",
        }
        
        table = Table(title="System Health")
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="green")
        
        for component, status in checks.items():
            table.add_row(component, status)
        
        console.print(table)
        console.print("✅ All systems operational!")
        
    except Exception as e:
        console.print(f"❌ Health check failed: {e}")
        raise typer.Exit(1)


@app.command()
def backup_database(
    output_file: str = typer.Option("backup.sql", "--output", "-o", help="Output file path"),
):
    """Backup the database."""
    console.print(f"💾 Backing up database to {output_file}...")
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Creating backup...", total=None)
            
            # This would perform actual database backup
            import time
            time.sleep(2)  # Simulate backup time
            
            progress.update(task, description="Backup completed!")
            
        console.print(f"✅ Database backed up to {output_file}")
        
    except Exception as e:
        console.print(f"❌ Backup failed: {e}")
        raise typer.Exit(1)


@app.command()
def clean_logs(
    days: int = typer.Option(30, "--days", "-d", help="Delete logs older than N days"),
    dry_run: bool = typer.Option(False, "--dry-run", help="Show what would be deleted"),
):
    """Clean old log files."""
    console.print(f"🧹 Cleaning logs older than {days} days...")
    
    try:
        if dry_run:
            console.print("🔍 Dry run mode - no files will be deleted")
            
        # This would clean actual log files
        console.print(f"✅ Log cleanup completed!")
        
    except Exception as e:
        console.print(f"❌ Log cleanup failed: {e}")
        raise typer.Exit(1)


@app.command()
def reset_database():
    """Reset the database (WARNING: This will delete all data!)."""
    console.print("⚠️  WARNING: This will delete ALL data in the database!")
    
    if not Confirm.ask("Are you sure you want to continue?"):
        console.print("❌ Database reset cancelled.")
        return
    
    if not Confirm.ask("Type 'yes' to confirm", default=False):
        console.print("❌ Database reset cancelled.")
        return
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Resetting database...", total=None)
            
            # This would reset the actual database
            import time
            time.sleep(2)  # Simulate reset time
            
            progress.update(task, description="Database reset completed!")
            
        console.print("✅ Database reset completed!")
        
    except Exception as e:
        console.print(f"❌ Database reset failed: {e}")
        raise typer.Exit(1)


def main():
    """Main CLI entry point."""
    # Setup logging
    setup_logging()
    
    # Run the CLI app
    app()


if __name__ == "__main__":
    main()
