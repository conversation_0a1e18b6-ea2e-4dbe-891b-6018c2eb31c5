"""
Core module for the Enterprise AI/ML Platform backend.

This module contains the core functionality including:
- Configuration management
- Database connections
- Authentication and security
- Logging setup
- Exception handling
"""

from .config import settings
from .database import get_db, init_db, close_db
from .security import get_current_user, verify_token, create_access_token
from .auth import authenticate_user, get_password_hash, verify_password

__all__ = [
    "settings",
    "get_db",
    "init_db", 
    "close_db",
    "get_current_user",
    "verify_token",
    "create_access_token",
    "authenticate_user",
    "get_password_hash",
    "verify_password",
]
