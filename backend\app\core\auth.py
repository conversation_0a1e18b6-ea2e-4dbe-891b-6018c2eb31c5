"""
Authentication and Authorization Module
======================================

Comprehensive authentication system with JWT tokens, OAuth2, and RBAC.
"""

from datetime import datetime, timedelta
from typing import Any, Union, Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON>2<PERSON><PERSON><PERSON><PERSON>earer
from sqlalchemy.orm import Session

from .config import settings
from .database import get_db
from ..models.user import User

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/token"
)

# JWT settings
SECRET_KEY = settings.SECRET_KEY
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30


def create_access_token(
    data: dict, 
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)


def verify_token(token: str) -> Optional[dict]:
    """Verify and decode JWT token."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None


async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = db.query(User).filter(User.email == email).first()
    if user is None:
        raise credentials_exception
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user."""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Get current admin user."""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


def authenticate_user(db: Session, email: str, password: str) -> Union[User, bool]:
    """Authenticate user with email and password."""
    user = db.query(User).filter(User.email == email).first()
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user


def create_user(db: Session, email: str, password: str, **kwargs) -> User:
    """Create a new user."""
    hashed_password = get_password_hash(password)
    
    user = User(
        email=email,
        hashed_password=hashed_password,
        **kwargs
    )
    
    db.add(user)
    db.commit()
    db.refresh(user)
    
    return user


class RoleChecker:
    """Role-based access control checker."""
    
    def __init__(self, allowed_roles: list):
        self.allowed_roles = allowed_roles
    
    def __call__(self, current_user: User = Depends(get_current_active_user)):
        if not any(role in current_user.roles for role in self.allowed_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Operation not permitted"
            )
        return current_user


class PermissionChecker:
    """Permission-based access control checker."""
    
    def __init__(self, required_permission: str):
        self.required_permission = required_permission
    
    def __call__(self, current_user: User = Depends(get_current_active_user)):
        if not self.has_permission(current_user, self.required_permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return current_user
    
    def has_permission(self, user: User, permission: str) -> bool:
        """Check if user has specific permission."""
        # Check direct permissions
        if permission in user.permissions:
            return True
        
        # Check role-based permissions
        for role in user.roles:
            if permission in role.permissions:
                return True
        
        return False


# Permission constants
class Permissions:
    """Permission constants."""
    
    # User management
    CREATE_USER = "create_user"
    READ_USER = "read_user"
    UPDATE_USER = "update_user"
    DELETE_USER = "delete_user"
    
    # Workflow management
    CREATE_WORKFLOW = "create_workflow"
    READ_WORKFLOW = "read_workflow"
    UPDATE_WORKFLOW = "update_workflow"
    DELETE_WORKFLOW = "delete_workflow"
    EXECUTE_WORKFLOW = "execute_workflow"
    
    # Model management
    CREATE_MODEL = "create_model"
    READ_MODEL = "read_model"
    UPDATE_MODEL = "update_model"
    DELETE_MODEL = "delete_model"
    DEPLOY_MODEL = "deploy_model"
    
    # Data management
    CREATE_DATASET = "create_dataset"
    READ_DATASET = "read_dataset"
    UPDATE_DATASET = "update_dataset"
    DELETE_DATASET = "delete_dataset"
    
    # System administration
    ADMIN_SYSTEM = "admin_system"
    VIEW_LOGS = "view_logs"
    MANAGE_AGENTS = "manage_agents"
    
    # Marketplace
    PUBLISH_ITEM = "publish_item"
    PURCHASE_ITEM = "purchase_item"
    REVIEW_ITEM = "review_item"


# Role constants
class Roles:
    """Role constants."""
    
    ADMIN = "admin"
    USER = "user"
    DEVELOPER = "developer"
    DATA_SCIENTIST = "data_scientist"
    ML_ENGINEER = "ml_engineer"
    VIEWER = "viewer"


# Default role permissions
DEFAULT_ROLE_PERMISSIONS = {
    Roles.ADMIN: [
        Permissions.CREATE_USER,
        Permissions.READ_USER,
        Permissions.UPDATE_USER,
        Permissions.DELETE_USER,
        Permissions.CREATE_WORKFLOW,
        Permissions.READ_WORKFLOW,
        Permissions.UPDATE_WORKFLOW,
        Permissions.DELETE_WORKFLOW,
        Permissions.EXECUTE_WORKFLOW,
        Permissions.CREATE_MODEL,
        Permissions.READ_MODEL,
        Permissions.UPDATE_MODEL,
        Permissions.DELETE_MODEL,
        Permissions.DEPLOY_MODEL,
        Permissions.CREATE_DATASET,
        Permissions.READ_DATASET,
        Permissions.UPDATE_DATASET,
        Permissions.DELETE_DATASET,
        Permissions.ADMIN_SYSTEM,
        Permissions.VIEW_LOGS,
        Permissions.MANAGE_AGENTS,
        Permissions.PUBLISH_ITEM,
        Permissions.PURCHASE_ITEM,
        Permissions.REVIEW_ITEM,
    ],
    Roles.DATA_SCIENTIST: [
        Permissions.CREATE_WORKFLOW,
        Permissions.READ_WORKFLOW,
        Permissions.UPDATE_WORKFLOW,
        Permissions.EXECUTE_WORKFLOW,
        Permissions.CREATE_MODEL,
        Permissions.READ_MODEL,
        Permissions.UPDATE_MODEL,
        Permissions.CREATE_DATASET,
        Permissions.READ_DATASET,
        Permissions.UPDATE_DATASET,
        Permissions.PUBLISH_ITEM,
        Permissions.PURCHASE_ITEM,
        Permissions.REVIEW_ITEM,
    ],
    Roles.ML_ENGINEER: [
        Permissions.READ_WORKFLOW,
        Permissions.EXECUTE_WORKFLOW,
        Permissions.CREATE_MODEL,
        Permissions.READ_MODEL,
        Permissions.UPDATE_MODEL,
        Permissions.DEPLOY_MODEL,
        Permissions.READ_DATASET,
        Permissions.PURCHASE_ITEM,
        Permissions.REVIEW_ITEM,
    ],
    Roles.DEVELOPER: [
        Permissions.CREATE_WORKFLOW,
        Permissions.READ_WORKFLOW,
        Permissions.UPDATE_WORKFLOW,
        Permissions.EXECUTE_WORKFLOW,
        Permissions.READ_MODEL,
        Permissions.READ_DATASET,
        Permissions.PURCHASE_ITEM,
        Permissions.REVIEW_ITEM,
    ],
    Roles.USER: [
        Permissions.READ_WORKFLOW,
        Permissions.EXECUTE_WORKFLOW,
        Permissions.READ_MODEL,
        Permissions.READ_DATASET,
        Permissions.PURCHASE_ITEM,
        Permissions.REVIEW_ITEM,
    ],
    Roles.VIEWER: [
        Permissions.READ_WORKFLOW,
        Permissions.READ_MODEL,
        Permissions.READ_DATASET,
    ],
}


# Convenience functions for common permission checks
require_admin = PermissionChecker(Permissions.ADMIN_SYSTEM)
require_workflow_create = PermissionChecker(Permissions.CREATE_WORKFLOW)
require_model_deploy = PermissionChecker(Permissions.DEPLOY_MODEL)
require_dataset_create = PermissionChecker(Permissions.CREATE_DATASET)
