"""
Enterprise Cache Manager
========================

Production-ready caching system for the Enterprise AI/ML Platform.

Features:
- Multi-tier Caching (Memory, Redis, Disk)
- LRU & TTL Eviction Policies
- Cache Warming & Preloading
- Distributed Cache Invalidation
- Compression & Serialization
- Cache Analytics & Monitoring
- Async/Await Support
- Cache Partitioning
"""

import asyncio
import json
import pickle
import gzip
import hashlib
import time
from datetime import datetime, timedelta
from typing import Any, Optional, Dict, List, Union, Callable
from dataclasses import dataclass, asdict
from collections import OrderedDict
import logging
from pathlib import Path

@dataclass
class CacheConfig:
    """Cache configuration."""
    max_size: int = 1000
    ttl: int = 3600  # seconds
    compression: bool = True
    serialization: str = 'pickle'  # 'pickle', 'json'
    eviction_policy: str = 'lru'  # 'lru', 'ttl', 'lfu'

@dataclass
class CacheStats:
    """Cache statistics."""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    evictions: int = 0
    size: int = 0
    memory_usage: int = 0

class CacheEntry:
    """Cache entry with metadata."""
    
    def __init__(self, value: Any, ttl: Optional[int] = None):
        self.value = value
        self.created_at = time.time()
        self.expires_at = time.time() + ttl if ttl else None
        self.access_count = 0
        self.last_accessed = time.time()
        self.size = self._calculate_size(value)
    
    def _calculate_size(self, value: Any) -> int:
        """Calculate approximate size of value."""
        try:
            return len(pickle.dumps(value))
        except:
            return len(str(value))
    
    def is_expired(self) -> bool:
        """Check if entry is expired."""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at
    
    def access(self):
        """Record access to entry."""
        self.access_count += 1
        self.last_accessed = time.time()

class MemoryCache:
    """In-memory cache with LRU eviction."""
    
    def __init__(self, config: CacheConfig):
        self.config = config
        self.cache = OrderedDict()
        self.stats = CacheStats()
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        async with self._lock:
            if key not in self.cache:
                self.stats.misses += 1
                return None
            
            entry = self.cache[key]
            
            # Check expiration
            if entry.is_expired():
                del self.cache[key]
                self.stats.misses += 1
                self.stats.evictions += 1
                return None
            
            # Update access info
            entry.access()
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            
            self.stats.hits += 1
            return entry.value
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        async with self._lock:
            # Use default TTL if not specified
            if ttl is None:
                ttl = self.config.ttl
            
            entry = CacheEntry(value, ttl)
            
            # Remove existing entry if present
            if key in self.cache:
                del self.cache[key]
            
            # Add new entry
            self.cache[key] = entry
            self.stats.sets += 1
            self.stats.size += 1
            self.stats.memory_usage += entry.size
            
            # Evict if necessary
            await self._evict_if_needed()
            
            return True
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        async with self._lock:
            if key in self.cache:
                entry = self.cache[key]
                del self.cache[key]
                self.stats.deletes += 1
                self.stats.size -= 1
                self.stats.memory_usage -= entry.size
                return True
            return False
    
    async def clear(self):
        """Clear all cache entries."""
        async with self._lock:
            self.cache.clear()
            self.stats = CacheStats()
    
    async def _evict_if_needed(self):
        """Evict entries if cache is full."""
        while len(self.cache) > self.config.max_size:
            if self.config.eviction_policy == 'lru':
                # Remove least recently used
                key, entry = self.cache.popitem(last=False)
            elif self.config.eviction_policy == 'lfu':
                # Remove least frequently used
                key = min(self.cache.keys(), key=lambda k: self.cache[k].access_count)
                entry = self.cache.pop(key)
            else:  # ttl
                # Remove oldest entry
                key, entry = self.cache.popitem(last=False)
            
            self.stats.evictions += 1
            self.stats.size -= 1
            self.stats.memory_usage -= entry.size
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        hit_rate = self.stats.hits / (self.stats.hits + self.stats.misses) if (self.stats.hits + self.stats.misses) > 0 else 0
        
        return {
            **asdict(self.stats),
            'hit_rate': hit_rate,
            'config': asdict(self.config)
        }

class RedisCache:
    """Redis-based distributed cache."""
    
    def __init__(self, redis_client, config: CacheConfig):
        self.redis = redis_client
        self.config = config
        self.stats = CacheStats()
        self.key_prefix = "ml_cache:"
    
    def _serialize(self, value: Any) -> bytes:
        """Serialize value for storage."""
        if self.config.serialization == 'json':
            data = json.dumps(value).encode()
        else:  # pickle
            data = pickle.dumps(value)
        
        if self.config.compression:
            data = gzip.compress(data)
        
        return data
    
    def _deserialize(self, data: bytes) -> Any:
        """Deserialize value from storage."""
        if self.config.compression:
            data = gzip.decompress(data)
        
        if self.config.serialization == 'json':
            return json.loads(data.decode())
        else:  # pickle
            return pickle.loads(data)
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache."""
        try:
            full_key = f"{self.key_prefix}{key}"
            data = await self.redis.get(full_key)
            
            if data is None:
                self.stats.misses += 1
                return None
            
            value = self._deserialize(data)
            self.stats.hits += 1
            return value
            
        except Exception as e:
            logging.error(f"Redis cache get error: {e}")
            self.stats.misses += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in Redis cache."""
        try:
            full_key = f"{self.key_prefix}{key}"
            data = self._serialize(value)
            
            if ttl is None:
                ttl = self.config.ttl
            
            await self.redis.setex(full_key, ttl, data)
            self.stats.sets += 1
            return True
            
        except Exception as e:
            logging.error(f"Redis cache set error: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from Redis cache."""
        try:
            full_key = f"{self.key_prefix}{key}"
            result = await self.redis.delete(full_key)
            
            if result > 0:
                self.stats.deletes += 1
                return True
            return False
            
        except Exception as e:
            logging.error(f"Redis cache delete error: {e}")
            return False
    
    async def clear(self):
        """Clear all cache entries."""
        try:
            pattern = f"{self.key_prefix}*"
            keys = await self.redis.keys(pattern)
            
            if keys:
                await self.redis.delete(*keys)
            
            self.stats = CacheStats()
            
        except Exception as e:
            logging.error(f"Redis cache clear error: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        hit_rate = self.stats.hits / (self.stats.hits + self.stats.misses) if (self.stats.hits + self.stats.misses) > 0 else 0
        
        return {
            **asdict(self.stats),
            'hit_rate': hit_rate,
            'config': asdict(self.config),
            'backend': 'redis'
        }

class MultiTierCache:
    """Multi-tier cache with L1 (memory) and L2 (Redis) layers."""
    
    def __init__(self, redis_client=None, l1_config: CacheConfig = None, l2_config: CacheConfig = None):
        self.l1_config = l1_config or CacheConfig(max_size=100, ttl=300)  # 5 min
        self.l2_config = l2_config or CacheConfig(max_size=10000, ttl=3600)  # 1 hour
        
        self.l1_cache = MemoryCache(self.l1_config)
        self.l2_cache = RedisCache(redis_client, self.l2_config) if redis_client else None
        
        self.stats = CacheStats()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from multi-tier cache."""
        # Try L1 cache first
        value = await self.l1_cache.get(key)
        if value is not None:
            self.stats.hits += 1
            return value
        
        # Try L2 cache
        if self.l2_cache:
            value = await self.l2_cache.get(key)
            if value is not None:
                # Promote to L1 cache
                await self.l1_cache.set(key, value, self.l1_config.ttl)
                self.stats.hits += 1
                return value
        
        self.stats.misses += 1
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in multi-tier cache."""
        success = True
        
        # Set in L1 cache
        l1_ttl = min(ttl or self.l1_config.ttl, self.l1_config.ttl)
        await self.l1_cache.set(key, value, l1_ttl)
        
        # Set in L2 cache
        if self.l2_cache:
            l2_ttl = ttl or self.l2_config.ttl
            success = await self.l2_cache.set(key, value, l2_ttl)
        
        if success:
            self.stats.sets += 1
        
        return success
    
    async def delete(self, key: str) -> bool:
        """Delete value from multi-tier cache."""
        l1_deleted = await self.l1_cache.delete(key)
        l2_deleted = await self.l2_cache.delete(key) if self.l2_cache else False
        
        if l1_deleted or l2_deleted:
            self.stats.deletes += 1
            return True
        
        return False
    
    async def clear(self):
        """Clear all cache tiers."""
        await self.l1_cache.clear()
        if self.l2_cache:
            await self.l2_cache.clear()
        
        self.stats = CacheStats()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        l1_stats = self.l1_cache.get_stats()
        l2_stats = self.l2_cache.get_stats() if self.l2_cache else {}
        
        total_hits = self.stats.hits
        total_misses = self.stats.misses
        hit_rate = total_hits / (total_hits + total_misses) if (total_hits + total_misses) > 0 else 0
        
        return {
            'overall': {
                **asdict(self.stats),
                'hit_rate': hit_rate
            },
            'l1_cache': l1_stats,
            'l2_cache': l2_stats,
            'tiers': 2 if self.l2_cache else 1
        }

class CacheManager:
    """Enterprise cache manager with advanced features."""
    
    def __init__(self, redis_client=None):
        self.redis = redis_client
        self.caches = {}
        self.default_cache = MultiTierCache(redis_client)
        
        # Cache warming functions
        self.warming_functions = {}
        
        logging.info("Cache manager initialized")
    
    def create_cache(self, name: str, config: CacheConfig, use_redis: bool = True) -> MultiTierCache:
        """Create a named cache with specific configuration."""
        redis_client = self.redis if use_redis else None
        cache = MultiTierCache(redis_client, config, config)
        self.caches[name] = cache
        return cache
    
    def get_cache(self, name: str = 'default') -> MultiTierCache:
        """Get cache by name."""
        if name == 'default':
            return self.default_cache
        return self.caches.get(name, self.default_cache)
    
    async def get(self, key: str, cache_name: str = 'default') -> Optional[Any]:
        """Get value from named cache."""
        cache = self.get_cache(cache_name)
        return await cache.get(key)
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, cache_name: str = 'default') -> bool:
        """Set value in named cache."""
        cache = self.get_cache(cache_name)
        return await cache.set(key, value, ttl)
    
    async def delete(self, key: str, cache_name: str = 'default') -> bool:
        """Delete value from named cache."""
        cache = self.get_cache(cache_name)
        return await cache.delete(key)
    
    async def clear(self, cache_name: str = 'default'):
        """Clear named cache."""
        cache = self.get_cache(cache_name)
        await cache.clear()
    
    def cache_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments."""
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items())
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def cached(self, ttl: int = 3600, cache_name: str = 'default', key_func: Optional[Callable] = None):
        """Decorator for caching function results."""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                # Generate cache key
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    cache_key = f"{func.__name__}:{self.cache_key(*args, **kwargs)}"
                
                # Try to get from cache
                cached_result = await self.get(cache_key, cache_name)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = await func(*args, **kwargs)
                await self.set(cache_key, result, ttl, cache_name)
                
                return result
            
            return wrapper
        return decorator
    
    def register_warming_function(self, cache_name: str, func: Callable):
        """Register cache warming function."""
        self.warming_functions[cache_name] = func
    
    async def warm_cache(self, cache_name: str = 'default'):
        """Warm cache using registered function."""
        if cache_name in self.warming_functions:
            warming_func = self.warming_functions[cache_name]
            await warming_func(self.get_cache(cache_name))
            logging.info(f"Cache {cache_name} warmed successfully")
    
    async def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all caches."""
        stats = {
            'default': self.default_cache.get_stats()
        }
        
        for name, cache in self.caches.items():
            stats[name] = cache.get_stats()
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform cache health check."""
        health = {
            'status': 'healthy',
            'caches': len(self.caches) + 1,  # +1 for default
            'redis_available': self.redis is not None
        }
        
        if self.redis:
            try:
                await self.redis.ping()
                health['redis_status'] = 'connected'
            except Exception as e:
                health['redis_status'] = f'error: {e}'
                health['status'] = 'degraded'
        
        return health

# Global cache manager instance
cache_manager = CacheManager()

# Convenience functions
async def get_cached(key: str, cache_name: str = 'default') -> Optional[Any]:
    """Get value from cache."""
    return await cache_manager.get(key, cache_name)

async def set_cached(key: str, value: Any, ttl: Optional[int] = None, cache_name: str = 'default') -> bool:
    """Set value in cache."""
    return await cache_manager.set(key, value, ttl, cache_name)

def cached(ttl: int = 3600, cache_name: str = 'default', key_func: Optional[Callable] = None):
    """Caching decorator."""
    return cache_manager.cached(ttl, cache_name, key_func)

# Export main components
__all__ = [
    'CacheConfig', 'CacheStats', 'CacheEntry', 'MemoryCache', 'RedisCache',
    'MultiTierCache', 'CacheManager', 'cache_manager', 'get_cached', 'set_cached', 'cached'
]
