"""
Database Management
===================

Multi-database architecture with PostgreSQL, MongoDB, and Redis
including connection pooling, migrations, and data versioning.
"""

import asyncio
from typing import AsyncGenerator, Optional
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import declarative_base
from sqlalchemy import MetaData
import motor.motor_asyncio
import redis.asyncio as redis
from alembic import command
from alembic.config import Config

from app.core.config import settings
from app.core.logging import logger


# SQLAlchemy Base
Base = declarative_base()
metadata = MetaData()

# Database engines and sessions
postgres_engine = None
postgres_session_factory = None
mongodb_client = None
mongodb_database = None
redis_client = None


class DatabaseManager:
    """Centralized database management."""
    
    def __init__(self):
        self.postgres_engine = None
        self.postgres_session_factory = None
        self.mongodb_client = None
        self.mongodb_database = None
        self.redis_client = None
    
    async def init_postgres(self):
        """Initialize PostgreSQL connection."""
        try:
            self.postgres_engine = create_async_engine(
                str(settings.DATABASE_URL),
                echo=settings.DEBUG,
                pool_size=20,
                max_overflow=30,
                pool_pre_ping=True,
                pool_recycle=3600,
            )
            
            self.postgres_session_factory = async_sessionmaker(
                self.postgres_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            logger.info("PostgreSQL connection initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL: {e}")
            raise
    
    async def init_mongodb(self):
        """Initialize MongoDB connection."""
        try:
            self.mongodb_client = motor.motor_asyncio.AsyncIOMotorClient(
                settings.MONGODB_URL,
                maxPoolSize=50,
                minPoolSize=10,
                maxIdleTimeMS=30000,
                serverSelectionTimeoutMS=5000,
            )
            
            self.mongodb_database = self.mongodb_client[settings.MONGODB_DB_NAME]
            
            # Test connection
            await self.mongodb_client.admin.command('ping')
            
            logger.info("MongoDB connection initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize MongoDB: {e}")
            raise
    
    async def init_redis(self):
        """Initialize Redis connection."""
        try:
            self.redis_client = redis.from_url(
                str(settings.REDIS_URL),
                encoding="utf-8",
                decode_responses=True,
                max_connections=20,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={},
            )
            
            # Test connection
            await self.redis_client.ping()
            
            logger.info("Redis connection initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
            raise
    
    async def close_connections(self):
        """Close all database connections."""
        try:
            if self.postgres_engine:
                await self.postgres_engine.dispose()
                logger.info("PostgreSQL connection closed")
            
            if self.mongodb_client:
                self.mongodb_client.close()
                logger.info("MongoDB connection closed")
            
            if self.redis_client:
                await self.redis_client.close()
                logger.info("Redis connection closed")
                
        except Exception as e:
            logger.error(f"Error closing database connections: {e}")


# Global database manager
db_manager = DatabaseManager()


async def init_db():
    """Initialize all database connections."""
    await db_manager.init_postgres()
    await db_manager.init_mongodb()
    await db_manager.init_redis()
    
    # Set global variables for backward compatibility
    global postgres_engine, postgres_session_factory, mongodb_client, mongodb_database, redis_client
    postgres_engine = db_manager.postgres_engine
    postgres_session_factory = db_manager.postgres_session_factory
    mongodb_client = db_manager.mongodb_client
    mongodb_database = db_manager.mongodb_database
    redis_client = db_manager.redis_client


async def close_db():
    """Close all database connections."""
    await db_manager.close_connections()


async def get_postgres_session() -> AsyncGenerator[AsyncSession, None]:
    """Get PostgreSQL session."""
    async with db_manager.postgres_session_factory() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


def get_mongodb() -> motor.motor_asyncio.AsyncIOMotorDatabase:
    """Get MongoDB database."""
    return db_manager.mongodb_database


def get_redis() -> redis.Redis:
    """Get Redis client."""
    return db_manager.redis_client


class DataVersioning:
    """Data versioning and lineage tracking."""
    
    def __init__(self):
        self.mongodb = get_mongodb()
    
    async def create_data_version(
        self,
        dataset_id: str,
        version: str,
        metadata: dict,
        user_id: str
    ) -> str:
        """Create a new data version."""
        version_doc = {
            "dataset_id": dataset_id,
            "version": version,
            "metadata": metadata,
            "created_by": user_id,
            "created_at": asyncio.get_event_loop().time(),
            "status": "active"
        }
        
        result = await self.mongodb.data_versions.insert_one(version_doc)
        return str(result.inserted_id)
    
    async def get_data_lineage(self, dataset_id: str) -> list:
        """Get data lineage for a dataset."""
        cursor = self.mongodb.data_versions.find(
            {"dataset_id": dataset_id}
        ).sort("created_at", -1)
        
        return await cursor.to_list(length=None)
    
    async def track_data_transformation(
        self,
        source_dataset_id: str,
        target_dataset_id: str,
        transformation_type: str,
        parameters: dict,
        user_id: str
    ):
        """Track data transformation."""
        transformation_doc = {
            "source_dataset_id": source_dataset_id,
            "target_dataset_id": target_dataset_id,
            "transformation_type": transformation_type,
            "parameters": parameters,
            "created_by": user_id,
            "created_at": asyncio.get_event_loop().time()
        }
        
        await self.mongodb.data_transformations.insert_one(transformation_doc)


class MigrationManager:
    """Database migration management."""
    
    def __init__(self):
        self.alembic_cfg = Config("alembic.ini")
    
    def run_migrations(self):
        """Run database migrations."""
        try:
            command.upgrade(self.alembic_cfg, "head")
            logger.info("Database migrations completed successfully")
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            raise
    
    def create_migration(self, message: str):
        """Create a new migration."""
        try:
            command.revision(self.alembic_cfg, message=message, autogenerate=True)
            logger.info(f"Migration created: {message}")
        except Exception as e:
            logger.error(f"Failed to create migration: {e}")
            raise


class BackupManager:
    """Database backup and disaster recovery."""
    
    def __init__(self):
        pass
    
    async def backup_postgres(self, backup_path: str):
        """Backup PostgreSQL database."""
        # Implementation would use pg_dump or similar
        logger.info(f"PostgreSQL backup created at {backup_path}")
    
    async def backup_mongodb(self, backup_path: str):
        """Backup MongoDB database."""
        # Implementation would use mongodump or similar
        logger.info(f"MongoDB backup created at {backup_path}")
    
    async def restore_postgres(self, backup_path: str):
        """Restore PostgreSQL database."""
        # Implementation would use pg_restore or similar
        logger.info(f"PostgreSQL restored from {backup_path}")
    
    async def restore_mongodb(self, backup_path: str):
        """Restore MongoDB database."""
        # Implementation would use mongorestore or similar
        logger.info(f"MongoDB restored from {backup_path}")


# Connection health check
async def check_database_health() -> dict:
    """Check health of all database connections."""
    health_status = {
        "postgres": False,
        "mongodb": False,
        "redis": False
    }
    
    try:
        # Check PostgreSQL
        async with get_postgres_session() as session:
            await session.execute("SELECT 1")
            health_status["postgres"] = True
    except Exception as e:
        logger.error(f"PostgreSQL health check failed: {e}")
    
    try:
        # Check MongoDB
        await mongodb_client.admin.command('ping')
        health_status["mongodb"] = True
    except Exception as e:
        logger.error(f"MongoDB health check failed: {e}")
    
    try:
        # Check Redis
        await redis_client.ping()
        health_status["redis"] = True
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
    
    return health_status
