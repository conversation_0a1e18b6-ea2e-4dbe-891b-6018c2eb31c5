"""
Enhanced Configuration
=====================

Comprehensive configuration for the Enterprise AI/ML Platform with
advanced settings for all domains and enterprise features.
"""

import os
from typing import List, Dict, Any, Optional
from pydantic import BaseSettings, Field, validator
from functools import lru_cache

class EnhancedSettings(BaseSettings):
    """Enhanced settings for the Enterprise AI/ML Platform."""
    
    # Application Settings
    APP_NAME: str = "Enterprise AI/ML Platform"
    APP_VERSION: str = "2.0.0"
    APP_DESCRIPTION: str = "Production-ready, enterprise-grade No Code AI/ML platform"
    DEBUG: bool = Field(default=False, env="DEBUG")
    ENVIRONMENT: str = Field(default="production", env="ENVIRONMENT")
    
    # Server Settings
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    WORKERS: int = Field(default=4, env="WORKERS")
    RELOAD: bool = Field(default=False, env="RELOAD")
    
    # Security Settings
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    ALGORITHM: str = Field(default="HS256", env="ALGORITHM")
    
    # CORS Settings
    ALLOWED_HOSTS: List[str] = Field(
        default=["*"], 
        env="ALLOWED_HOSTS"
    )
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "https://localhost:3000"],
        env="CORS_ORIGINS"
    )
    
    # Database Settings
    DATABASE_URL: str = Field(..., env="DATABASE_URL")
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    
    # Redis Settings
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    REDIS_POOL_SIZE: int = Field(default=20, env="REDIS_POOL_SIZE")
    
    # MongoDB Settings (for document storage)
    MONGODB_URL: str = Field(default="mongodb://localhost:27017", env="MONGODB_URL")
    MONGODB_DATABASE: str = Field(default="aiml_platform", env="MONGODB_DATABASE")
    
    # ML Model Storage
    ML_MODEL_STORAGE_PATH: str = Field(default="./models", env="ML_MODEL_STORAGE_PATH")
    ML_MODEL_CACHE_SIZE: int = Field(default=100, env="ML_MODEL_CACHE_SIZE")
    ML_MODEL_TIMEOUT: int = Field(default=300, env="ML_MODEL_TIMEOUT")
    
    # GPU Settings
    CUDA_VISIBLE_DEVICES: Optional[str] = Field(default=None, env="CUDA_VISIBLE_DEVICES")
    GPU_MEMORY_FRACTION: float = Field(default=0.8, env="GPU_MEMORY_FRACTION")
    ENABLE_MIXED_PRECISION: bool = Field(default=True, env="ENABLE_MIXED_PRECISION")
    
    # RAPIDS Settings
    ENABLE_RAPIDS: bool = Field(default=True, env="ENABLE_RAPIDS")
    RAPIDS_MEMORY_POOL_SIZE: str = Field(default="2GB", env="RAPIDS_MEMORY_POOL_SIZE")
    
    # AutoML Settings
    AUTOML_TIME_LIMIT: int = Field(default=3600, env="AUTOML_TIME_LIMIT")  # 1 hour
    AUTOML_QUALITY_PRESET: str = Field(default="best_quality", env="AUTOML_QUALITY_PRESET")
    AUTOML_MAX_MODELS: int = Field(default=50, env="AUTOML_MAX_MODELS")
    
    # Computer Vision Settings
    CV_MAX_IMAGE_SIZE: int = Field(default=2048, env="CV_MAX_IMAGE_SIZE")
    CV_SUPPORTED_FORMATS: List[str] = Field(
        default=["jpg", "jpeg", "png", "bmp", "tiff"],
        env="CV_SUPPORTED_FORMATS"
    )
    CV_BATCH_SIZE: int = Field(default=32, env="CV_BATCH_SIZE")
    
    # NLP Settings
    NLP_MAX_SEQUENCE_LENGTH: int = Field(default=512, env="NLP_MAX_SEQUENCE_LENGTH")
    NLP_BATCH_SIZE: int = Field(default=16, env="NLP_BATCH_SIZE")
    NLP_CACHE_SIZE: int = Field(default=1000, env="NLP_CACHE_SIZE")
    
    # Time Series Settings
    TS_MAX_FORECAST_HORIZON: int = Field(default=365, env="TS_MAX_FORECAST_HORIZON")
    TS_MIN_TRAINING_SAMPLES: int = Field(default=50, env="TS_MIN_TRAINING_SAMPLES")
    TS_SEASONALITY_DETECTION: bool = Field(default=True, env="TS_SEASONALITY_DETECTION")
    
    # Reinforcement Learning Settings
    RL_MAX_EPISODES: int = Field(default=10000, env="RL_MAX_EPISODES")
    RL_SAVE_FREQUENCY: int = Field(default=1000, env="RL_SAVE_FREQUENCY")
    RL_EVALUATION_EPISODES: int = Field(default=100, env="RL_EVALUATION_EPISODES")
    
    # Quantum ML Settings
    QUANTUM_BACKEND: str = Field(default="qasm_simulator", env="QUANTUM_BACKEND")
    QUANTUM_SHOTS: int = Field(default=1024, env="QUANTUM_SHOTS")
    QUANTUM_MAX_QUBITS: int = Field(default=20, env="QUANTUM_MAX_QUBITS")
    
    # Federated Learning Settings
    FL_MIN_CLIENTS: int = Field(default=2, env="FL_MIN_CLIENTS")
    FL_MAX_CLIENTS: int = Field(default=100, env="FL_MAX_CLIENTS")
    FL_ROUNDS: int = Field(default=10, env="FL_ROUNDS")
    FL_CLIENT_TIMEOUT: int = Field(default=300, env="FL_CLIENT_TIMEOUT")
    
    # Edge AI Settings
    EDGE_MODEL_FORMATS: List[str] = Field(
        default=["onnx", "tflite", "torchscript"],
        env="EDGE_MODEL_FORMATS"
    )
    EDGE_QUANTIZATION_ENABLED: bool = Field(default=True, env="EDGE_QUANTIZATION_ENABLED")
    EDGE_OPTIMIZATION_LEVEL: str = Field(default="O2", env="EDGE_OPTIMIZATION_LEVEL")
    
    # Audio Processing Settings
    AUDIO_SAMPLE_RATE: int = Field(default=16000, env="AUDIO_SAMPLE_RATE")
    AUDIO_MAX_DURATION: int = Field(default=300, env="AUDIO_MAX_DURATION")  # 5 minutes
    AUDIO_SUPPORTED_FORMATS: List[str] = Field(
        default=["wav", "mp3", "flac", "m4a"],
        env="AUDIO_SUPPORTED_FORMATS"
    )
    
    # Monitoring Settings
    ENABLE_PROMETHEUS: bool = Field(default=True, env="ENABLE_PROMETHEUS")
    PROMETHEUS_PORT: int = Field(default=9090, env="PROMETHEUS_PORT")
    ENABLE_GRAFANA: bool = Field(default=True, env="ENABLE_GRAFANA")
    GRAFANA_PORT: int = Field(default=3001, env="GRAFANA_PORT")
    
    # Logging Settings
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")
    LOG_FILE: Optional[str] = Field(default=None, env="LOG_FILE")
    ENABLE_STRUCTURED_LOGGING: bool = Field(default=True, env="ENABLE_STRUCTURED_LOGGING")
    
    # Rate Limiting Settings
    RATE_LIMIT_ENABLED: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")  # seconds
    
    # File Upload Settings
    MAX_FILE_SIZE: int = Field(default=100 * 1024 * 1024, env="MAX_FILE_SIZE")  # 100MB
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=["csv", "json", "parquet", "xlsx", "jpg", "png", "wav", "mp3"],
        env="ALLOWED_FILE_TYPES"
    )
    UPLOAD_DIRECTORY: str = Field(default="./uploads", env="UPLOAD_DIRECTORY")
    
    # Celery Settings (for background tasks)
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/1", env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/2", env="CELERY_RESULT_BACKEND")
    CELERY_TASK_SERIALIZER: str = Field(default="json", env="CELERY_TASK_SERIALIZER")
    CELERY_RESULT_SERIALIZER: str = Field(default="json", env="CELERY_RESULT_SERIALIZER")
    
    # Email Settings
    SMTP_HOST: Optional[str] = Field(default=None, env="SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USERNAME: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    SMTP_PASSWORD: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    SMTP_TLS: bool = Field(default=True, env="SMTP_TLS")
    
    # Cloud Provider Settings
    AWS_ACCESS_KEY_ID: Optional[str] = Field(default=None, env="AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY: Optional[str] = Field(default=None, env="AWS_SECRET_ACCESS_KEY")
    AWS_REGION: str = Field(default="us-east-1", env="AWS_REGION")
    AWS_S3_BUCKET: Optional[str] = Field(default=None, env="AWS_S3_BUCKET")
    
    GCP_PROJECT_ID: Optional[str] = Field(default=None, env="GCP_PROJECT_ID")
    GCP_CREDENTIALS_PATH: Optional[str] = Field(default=None, env="GCP_CREDENTIALS_PATH")
    
    AZURE_STORAGE_ACCOUNT: Optional[str] = Field(default=None, env="AZURE_STORAGE_ACCOUNT")
    AZURE_STORAGE_KEY: Optional[str] = Field(default=None, env="AZURE_STORAGE_KEY")
    
    # API Keys for External Services
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    HUGGINGFACE_API_KEY: Optional[str] = Field(default=None, env="HUGGINGFACE_API_KEY")
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    
    # Feature Flags
    ENABLE_EXPERIMENTAL_FEATURES: bool = Field(default=False, env="ENABLE_EXPERIMENTAL_FEATURES")
    ENABLE_QUANTUM_ML: bool = Field(default=False, env="ENABLE_QUANTUM_ML")
    ENABLE_FEDERATED_LEARNING: bool = Field(default=False, env="ENABLE_FEDERATED_LEARNING")
    ENABLE_EDGE_AI: bool = Field(default=True, env="ENABLE_EDGE_AI")
    
    # Performance Settings
    MAX_CONCURRENT_REQUESTS: int = Field(default=100, env="MAX_CONCURRENT_REQUESTS")
    REQUEST_TIMEOUT: int = Field(default=300, env="REQUEST_TIMEOUT")  # 5 minutes
    BACKGROUND_TASK_TIMEOUT: int = Field(default=3600, env="BACKGROUND_TASK_TIMEOUT")  # 1 hour
    
    # Security Settings
    ENABLE_HTTPS: bool = Field(default=True, env="ENABLE_HTTPS")
    SSL_CERT_PATH: Optional[str] = Field(default=None, env="SSL_CERT_PATH")
    SSL_KEY_PATH: Optional[str] = Field(default=None, env="SSL_KEY_PATH")
    ENABLE_CSRF_PROTECTION: bool = Field(default=True, env="ENABLE_CSRF_PROTECTION")
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("CORS_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("CV_SUPPORTED_FORMATS", pre=True)
    def parse_cv_formats(cls, v):
        if isinstance(v, str):
            return [fmt.strip() for fmt in v.split(",")]
        return v
    
    @validator("ALLOWED_FILE_TYPES", pre=True)
    def parse_file_types(cls, v):
        if isinstance(v, str):
            return [ftype.strip() for ftype in v.split(",")]
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

@lru_cache()
def get_enhanced_settings() -> EnhancedSettings:
    """Get cached enhanced settings instance."""
    return EnhancedSettings()

# Export settings instance
enhanced_settings = get_enhanced_settings()

# Module-specific configurations
ML_MODULE_CONFIGS = {
    "automl": {
        "enabled": True,
        "frameworks": ["autogluon", "h2o", "tpot"],
        "max_training_time": enhanced_settings.AUTOML_TIME_LIMIT,
        "quality_preset": enhanced_settings.AUTOML_QUALITY_PRESET
    },
    "computer_vision": {
        "enabled": True,
        "frameworks": ["pytorch", "tensorflow", "detectron2", "ultralytics"],
        "max_image_size": enhanced_settings.CV_MAX_IMAGE_SIZE,
        "batch_size": enhanced_settings.CV_BATCH_SIZE
    },
    "nlp": {
        "enabled": True,
        "frameworks": ["transformers", "spacy", "nltk", "gensim"],
        "max_sequence_length": enhanced_settings.NLP_MAX_SEQUENCE_LENGTH,
        "batch_size": enhanced_settings.NLP_BATCH_SIZE
    },
    "time_series": {
        "enabled": True,
        "frameworks": ["prophet", "darts", "statsmodels", "sktime"],
        "max_forecast_horizon": enhanced_settings.TS_MAX_FORECAST_HORIZON,
        "min_training_samples": enhanced_settings.TS_MIN_TRAINING_SAMPLES
    },
    "reinforcement_learning": {
        "enabled": True,
        "frameworks": ["stable_baselines3", "ray", "gym"],
        "max_episodes": enhanced_settings.RL_MAX_EPISODES,
        "save_frequency": enhanced_settings.RL_SAVE_FREQUENCY
    },
    "quantum_ml": {
        "enabled": enhanced_settings.ENABLE_QUANTUM_ML,
        "frameworks": ["qiskit", "cirq", "pennylane"],
        "backend": enhanced_settings.QUANTUM_BACKEND,
        "max_qubits": enhanced_settings.QUANTUM_MAX_QUBITS
    },
    "federated_learning": {
        "enabled": enhanced_settings.ENABLE_FEDERATED_LEARNING,
        "frameworks": ["flower", "fedml", "tensorflow_federated"],
        "min_clients": enhanced_settings.FL_MIN_CLIENTS,
        "max_clients": enhanced_settings.FL_MAX_CLIENTS
    },
    "edge_ai": {
        "enabled": enhanced_settings.ENABLE_EDGE_AI,
        "frameworks": ["onnx", "tensorrt", "openvino"],
        "supported_formats": enhanced_settings.EDGE_MODEL_FORMATS,
        "quantization_enabled": enhanced_settings.EDGE_QUANTIZATION_ENABLED
    }
}
