"""
Enhanced Error Handling System
=============================

Comprehensive error handling with circuit breakers, retry logic,
and advanced error tracking for the AI/ML platform.
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, Callable, Type
from functools import wraps
from dataclasses import dataclass
from enum import Enum
import traceback
from contextlib import asynccontextmanager

# Circuit breaker implementation
class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

@dataclass
class CircuitBreakerConfig:
    failure_threshold: int = 5
    recovery_timeout: int = 60
    expected_exception: Type[Exception] = Exception
    fallback_function: Optional[Callable] = None

class CircuitBreaker:
    """Circuit breaker pattern implementation for resilient API calls."""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
        
    def __call__(self, func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                else:
                    if self.config.fallback_function:
                        return await self.config.fallback_function(*args, **kwargs)
                    raise CircuitBreakerOpenError("Circuit breaker is open")
            
            try:
                result = await func(*args, **kwargs)
                self._on_success()
                return result
            except self.config.expected_exception as e:
                self._on_failure()
                raise e
        
        return wrapper
    
    def _should_attempt_reset(self) -> bool:
        return (
            self.last_failure_time and
            time.time() - self.last_failure_time >= self.config.recovery_timeout
        )
    
    def _on_success(self):
        self.failure_count = 0
        self.state = CircuitState.CLOSED
    
    def _on_failure(self):
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitState.OPEN

# Custom exceptions
class AIMLPlatformError(Exception):
    """Base exception for AI/ML platform."""
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.details = details or {}
        self.timestamp = time.time()

class ModelError(AIMLPlatformError):
    """Model-related errors."""
    pass

class DataError(AIMLPlatformError):
    """Data processing errors."""
    pass

class ValidationError(AIMLPlatformError):
    """Input validation errors."""
    pass

class ResourceError(AIMLPlatformError):
    """Resource allocation errors."""
    pass

class CircuitBreakerOpenError(AIMLPlatformError):
    """Circuit breaker is open."""
    pass

class RetryableError(AIMLPlatformError):
    """Errors that can be retried."""
    pass

# Retry decorator with exponential backoff
def retry_with_backoff(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """Retry decorator with exponential backoff."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        break
                    
                    # Calculate delay with exponential backoff
                    delay = min(
                        base_delay * (exponential_base ** attempt),
                        max_delay
                    )
                    
                    logging.warning(
                        f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                        f"Retrying in {delay:.2f} seconds..."
                    )
                    
                    await asyncio.sleep(delay)
            
            # If we get here, all retries failed
            raise last_exception
        
        return wrapper
    return decorator

# Error tracking and reporting
class ErrorTracker:
    """Track and report errors for monitoring and debugging."""
    
    def __init__(self):
        self.error_counts: Dict[str, int] = {}
        self.error_details: Dict[str, list] = {}
        
    def track_error(self, error: Exception, context: Dict[str, Any] = None):
        """Track an error occurrence."""
        error_type = type(error).__name__
        error_message = str(error)
        
        # Increment error count
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Store error details
        if error_type not in self.error_details:
            self.error_details[error_type] = []
        
        error_detail = {
            "message": error_message,
            "timestamp": time.time(),
            "context": context or {},
            "traceback": traceback.format_exc()
        }
        
        self.error_details[error_type].append(error_detail)
        
        # Keep only last 100 errors per type
        if len(self.error_details[error_type]) > 100:
            self.error_details[error_type] = self.error_details[error_type][-100:]
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of tracked errors."""
        return {
            "error_counts": self.error_counts.copy(),
            "total_errors": sum(self.error_counts.values()),
            "error_types": list(self.error_counts.keys())
        }
    
    def get_error_details(self, error_type: str) -> list:
        """Get detailed information about specific error type."""
        return self.error_details.get(error_type, [])

# Global error tracker instance
error_tracker = ErrorTracker()

# Error handling decorator
def handle_errors(
    fallback_value: Any = None,
    track_errors: bool = True,
    reraise: bool = True
):
    """Comprehensive error handling decorator."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # Track the error
                if track_errors:
                    context = {
                        "function": func.__name__,
                        "args": str(args)[:200],  # Limit length
                        "kwargs": str(kwargs)[:200]
                    }
                    error_tracker.track_error(e, context)
                
                # Log the error
                logging.error(
                    f"Error in {func.__name__}: {e}",
                    exc_info=True,
                    extra={
                        "function": func.__name__,
                        "error_type": type(e).__name__
                    }
                )
                
                # Return fallback value or reraise
                if reraise:
                    raise e
                else:
                    return fallback_value
        
        return wrapper
    return decorator

# Context manager for error handling
@asynccontextmanager
async def error_context(operation_name: str, context: Dict[str, Any] = None):
    """Context manager for handling errors in specific operations."""
    start_time = time.time()
    
    try:
        yield
        
        # Log successful operation
        duration = time.time() - start_time
        logging.info(
            f"Operation '{operation_name}' completed successfully in {duration:.2f}s",
            extra={"operation": operation_name, "duration": duration}
        )
        
    except Exception as e:
        # Track and log the error
        duration = time.time() - start_time
        error_context_data = {
            "operation": operation_name,
            "duration": duration,
            **(context or {})
        }
        
        error_tracker.track_error(e, error_context_data)
        
        logging.error(
            f"Operation '{operation_name}' failed after {duration:.2f}s: {e}",
            exc_info=True,
            extra=error_context_data
        )
        
        raise e

# Health check utilities
class HealthChecker:
    """Health check utilities for system components."""
    
    def __init__(self):
        self.checks: Dict[str, Callable] = {}
    
    def register_check(self, name: str, check_func: Callable):
        """Register a health check function."""
        self.checks[name] = check_func
    
    async def run_checks(self) -> Dict[str, Any]:
        """Run all registered health checks."""
        results = {}
        overall_healthy = True
        
        for name, check_func in self.checks.items():
            try:
                result = await check_func()
                results[name] = {
                    "status": "healthy",
                    "details": result
                }
            except Exception as e:
                overall_healthy = False
                results[name] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "details": None
                }
        
        return {
            "overall_status": "healthy" if overall_healthy else "unhealthy",
            "checks": results,
            "timestamp": time.time()
        }

# Global health checker instance
health_checker = HealthChecker()

# Utility functions
def create_circuit_breaker(
    failure_threshold: int = 5,
    recovery_timeout: int = 60,
    fallback_function: Optional[Callable] = None
) -> CircuitBreaker:
    """Create a circuit breaker with specified configuration."""
    config = CircuitBreakerConfig(
        failure_threshold=failure_threshold,
        recovery_timeout=recovery_timeout,
        fallback_function=fallback_function
    )
    return CircuitBreaker(config)

async def safe_execute(
    func: Callable,
    *args,
    fallback_value: Any = None,
    max_retries: int = 3,
    **kwargs
) -> Any:
    """Safely execute a function with error handling and retries."""
    @retry_with_backoff(max_retries=max_retries)
    @handle_errors(fallback_value=fallback_value, reraise=True)
    async def _execute():
        return await func(*args, **kwargs)
    
    try:
        return await _execute()
    except Exception:
        return fallback_value
