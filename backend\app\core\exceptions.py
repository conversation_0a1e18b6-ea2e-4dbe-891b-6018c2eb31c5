"""
Custom exceptions and exception handlers for the Enterprise AI/ML Platform.

This module defines custom exceptions and provides centralized
exception handling for the FastAPI application.
"""

from typing import Any, Dict, Optional
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import traceback
from datetime import datetime

from .logging import get_logger

logger = get_logger(__name__)


# Custom Exception Classes
class AIMLPlatformException(Exception):
    """Base exception for AI/ML Platform."""
    
    def __init__(
        self,
        message: str,
        error_code: str = "PLATFORM_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class AuthenticationError(AIMLPlatformException):
    """Authentication related errors."""
    
    def __init__(self, message: str = "Authentication failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="AUTH_ERROR",
            details=details
        )


class AuthorizationError(AIMLPlatformException):
    """Authorization related errors."""
    
    def __init__(self, message: str = "Access denied", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="AUTHZ_ERROR",
            details=details
        )


class ValidationError(AIMLPlatformException):
    """Data validation errors."""
    
    def __init__(self, message: str, field: str = None, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        if field:
            details["field"] = field
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=details
        )


class ModelError(AIMLPlatformException):
    """ML Model related errors."""
    
    def __init__(self, message: str, model_name: str = None, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        if model_name:
            details["model_name"] = model_name
        super().__init__(
            message=message,
            error_code="MODEL_ERROR",
            details=details
        )


class DataError(AIMLPlatformException):
    """Data processing related errors."""
    
    def __init__(self, message: str, data_source: str = None, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        if data_source:
            details["data_source"] = data_source
        super().__init__(
            message=message,
            error_code="DATA_ERROR",
            details=details
        )


class InfrastructureError(AIMLPlatformException):
    """Infrastructure related errors."""
    
    def __init__(self, message: str, component: str = None, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        if component:
            details["component"] = component
        super().__init__(
            message=message,
            error_code="INFRA_ERROR",
            details=details
        )


class RateLimitError(AIMLPlatformException):
    """Rate limiting errors."""
    
    def __init__(self, message: str = "Rate limit exceeded", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            details=details
        )


# Exception Handlers
async def aiml_platform_exception_handler(request: Request, exc: AIMLPlatformException) -> JSONResponse:
    """Handle custom AI/ML Platform exceptions."""
    logger.error(
        "Platform exception occurred",
        error_code=exc.error_code,
        message=exc.message,
        details=exc.details,
        path=request.url.path,
        method=request.method
    )
    
    # Map error codes to HTTP status codes
    status_code_map = {
        "AUTH_ERROR": status.HTTP_401_UNAUTHORIZED,
        "AUTHZ_ERROR": status.HTTP_403_FORBIDDEN,
        "VALIDATION_ERROR": status.HTTP_422_UNPROCESSABLE_ENTITY,
        "MODEL_ERROR": status.HTTP_400_BAD_REQUEST,
        "DATA_ERROR": status.HTTP_400_BAD_REQUEST,
        "INFRA_ERROR": status.HTTP_503_SERVICE_UNAVAILABLE,
        "RATE_LIMIT_ERROR": status.HTTP_429_TOO_MANY_REQUESTS,
        "PLATFORM_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
    }
    
    status_code = status_code_map.get(exc.error_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return JSONResponse(
        status_code=status_code,
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "details": exc.details,
                "timestamp": datetime.utcnow().isoformat(),
                "path": request.url.path
            }
        }
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions."""
    logger.warning(
        "HTTP exception occurred",
        status_code=exc.status_code,
        detail=exc.detail,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": "HTTP_ERROR",
                "message": exc.detail,
                "status_code": exc.status_code,
                "timestamp": datetime.utcnow().isoformat(),
                "path": request.url.path
            }
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle request validation errors."""
    logger.warning(
        "Validation error occurred",
        errors=exc.errors(),
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": {
                "code": "VALIDATION_ERROR",
                "message": "Request validation failed",
                "details": {
                    "validation_errors": exc.errors()
                },
                "timestamp": datetime.utcnow().isoformat(),
                "path": request.url.path
            }
        }
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle all other exceptions."""
    logger.error(
        "Unhandled exception occurred",
        exception_type=type(exc).__name__,
        exception_message=str(exc),
        traceback=traceback.format_exc(),
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": {
                "code": "INTERNAL_ERROR",
                "message": "An internal server error occurred",
                "timestamp": datetime.utcnow().isoformat(),
                "path": request.url.path
            }
        }
    )


def setup_exception_handlers(app: FastAPI) -> None:
    """Setup exception handlers for the FastAPI application."""
    
    # Custom exception handlers
    app.add_exception_handler(AIMLPlatformException, aiml_platform_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("Exception handlers configured successfully")
