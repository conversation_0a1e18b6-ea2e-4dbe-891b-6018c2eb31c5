"""
Logging configuration for the Enterprise AI/ML Platform.

This module provides structured logging with JSON formatting,
multiple log levels, and integration with monitoring systems.
"""

import logging
import logging.config
import sys
from typing import Any, Dict
import json
from datetime import datetime

import structlog
from pythonjsonlogger import jsonlogger

from .config import settings


class CustomJSONFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter with additional fields."""
    
    def add_fields(self, log_record: Dict[str, Any], record: logging.LogRecord, message_dict: Dict[str, Any]) -> None:
        super().add_fields(log_record, record, message_dict)
        
        # Add timestamp
        log_record['timestamp'] = datetime.utcnow().isoformat()
        
        # Add service information
        log_record['service'] = 'aiml-platform-backend'
        log_record['version'] = '2.0.0'
        
        # Add request ID if available
        if hasattr(record, 'request_id'):
            log_record['request_id'] = record.request_id
            
        # Add user ID if available
        if hasattr(record, 'user_id'):
            log_record['user_id'] = record.user_id


def setup_logging() -> None:
    """Setup logging configuration."""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Logging configuration
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": CustomJSONFormatter,
                "format": "%(asctime)s %(name)s %(levelname)s %(message)s"
            },
            "standard": {
                "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "json" if settings.LOG_FORMAT == "json" else "standard",
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "json" if settings.LOG_FORMAT == "json" else "standard",
                "filename": "logs/app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            }
        },
        "loggers": {
            "": {  # Root logger
                "handlers": ["console", "file"],
                "level": settings.LOG_LEVEL,
                "propagate": False
            },
            "uvicorn": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False
            },
            "uvicorn.error": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False
            },
            "uvicorn.access": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False
            },
            "sqlalchemy": {
                "handlers": ["console"],
                "level": "WARNING",
                "propagate": False
            },
            "celery": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False
            }
        }
    }
    
    # Create logs directory if it doesn't exist
    import os
    os.makedirs("logs", exist_ok=True)
    
    # Apply logging configuration
    logging.config.dictConfig(log_config)


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


# Create default logger
logger = get_logger(__name__)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> structlog.BoundLogger:
        """Get logger for this class."""
        return get_logger(self.__class__.__name__)


def log_request(request_id: str, user_id: str = None):
    """Decorator to log API requests."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger_instance = get_logger(func.__name__)
            
            # Add context
            logger_instance = logger_instance.bind(
                request_id=request_id,
                user_id=user_id,
                function=func.__name__
            )
            
            logger_instance.info("Request started")
            
            try:
                result = func(*args, **kwargs)
                logger_instance.info("Request completed successfully")
                return result
            except Exception as e:
                logger_instance.error("Request failed", error=str(e))
                raise
                
        return wrapper
    return decorator


def log_ml_operation(operation_type: str, model_name: str = None):
    """Decorator to log ML operations."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger_instance = get_logger(func.__name__)
            
            # Add context
            logger_instance = logger_instance.bind(
                operation_type=operation_type,
                model_name=model_name,
                function=func.__name__
            )
            
            logger_instance.info("ML operation started")
            
            try:
                result = func(*args, **kwargs)
                logger_instance.info("ML operation completed successfully")
                return result
            except Exception as e:
                logger_instance.error("ML operation failed", error=str(e))
                raise
                
        return wrapper
    return decorator


# Audit logging
audit_logger = get_logger("audit")

def audit_log(action: str, user_id: str, resource: str, details: Dict[str, Any] = None):
    """Log audit events."""
    audit_logger.info(
        "Audit event",
        action=action,
        user_id=user_id,
        resource=resource,
        details=details or {},
        timestamp=datetime.utcnow().isoformat()
    )
