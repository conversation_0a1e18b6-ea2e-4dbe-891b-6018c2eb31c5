"""
Performance Optimization Module
==============================

Advanced performance optimizations for the AI/ML platform including:
- Model caching and warm-up strategies
- Batch processing optimizations
- Memory management
- GPU resource pooling
- Async processing pipelines
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from functools import wraps, lru_cache
from dataclasses import dataclass
import psutil
try:
    import GPUtil
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False
from concurrent.futures import ThreadPoolExecutor
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
import numpy as np

@dataclass
class PerformanceMetrics:
    """Performance metrics tracking."""
    cpu_usage: float
    memory_usage: float
    gpu_usage: Optional[float]
    inference_time: float
    throughput: float
    cache_hit_rate: float

class ModelCache:
    """Advanced model caching with LRU and warm-up strategies."""
    
    def __init__(self, max_size: int = 10, warm_up_models: List[str] = None):
        self.max_size = max_size
        self.cache = {}
        self.access_times = {}
        self.warm_up_models = warm_up_models or []
        
    async def get_model(self, model_id: str):
        """Get model with caching."""
        if model_id in self.cache:
            self.access_times[model_id] = time.time()
            return self.cache[model_id]
        
        # Load model if not in cache
        model = await self._load_model(model_id)
        await self._add_to_cache(model_id, model)
        return model
    
    async def _load_model(self, model_id: str):
        """Load model implementation."""
        # Placeholder - implement actual model loading
        await asyncio.sleep(0.1)  # Simulate loading time
        return f"model_{model_id}"
    
    async def _add_to_cache(self, model_id: str, model):
        """Add model to cache with LRU eviction."""
        if len(self.cache) >= self.max_size:
            # Evict least recently used model
            lru_model = min(self.access_times.items(), key=lambda x: x[1])[0]
            del self.cache[lru_model]
            del self.access_times[lru_model]
        
        self.cache[model_id] = model
        self.access_times[model_id] = time.time()

class BatchProcessor:
    """Optimized batch processing for ML inference."""
    
    def __init__(self, max_batch_size: int = 32, timeout: float = 0.1):
        self.max_batch_size = max_batch_size
        self.timeout = timeout
        self.pending_requests = []
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    async def process_batch(self, requests: List[Dict[str, Any]]) -> List[Any]:
        """Process requests in optimized batches."""
        results = []
        
        for i in range(0, len(requests), self.max_batch_size):
            batch = requests[i:i + self.max_batch_size]
            batch_results = await self._process_single_batch(batch)
            results.extend(batch_results)
        
        return results
    
    async def _process_single_batch(self, batch: List[Dict[str, Any]]) -> List[Any]:
        """Process a single batch with GPU optimization."""
        # Implement batch processing logic
        await asyncio.sleep(0.01 * len(batch))  # Simulate processing
        return [{"result": f"processed_{i}"} for i in range(len(batch))]

def performance_monitor(func):
    """Decorator for performance monitoring."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.virtual_memory().percent
        
        try:
            result = await func(*args, **kwargs)
            
            # Calculate metrics
            end_time = time.time()
            end_memory = psutil.virtual_memory().percent
            
            gpu_usage = None
            if GPU_AVAILABLE:
                gpus = GPUtil.getGPUs()
                gpu_usage = gpus[0].load if gpus else None
            
            metrics = PerformanceMetrics(
                cpu_usage=psutil.cpu_percent(),
                memory_usage=end_memory,
                gpu_usage=gpu_usage,
                inference_time=end_time - start_time,
                throughput=1.0 / (end_time - start_time) if end_time > start_time else 0,
                cache_hit_rate=0.0  # Calculate based on cache stats
            )
            
            # Log metrics (in production, use proper logging)
            print(f"Performance: {func.__name__} - {metrics}")
            
            return result
            
        except Exception as e:
            print(f"Performance monitoring error in {func.__name__}: {e}")
            raise
    
    return wrapper

class GPUResourceManager:
    """GPU resource management and pooling."""
    
    def __init__(self):
        self.gpu_pool = []
        self.device_map = {}
        
    def allocate_gpu(self, model_id: str):
        """Allocate GPU for model."""
        if TORCH_AVAILABLE and torch.cuda.is_available():
            gpu_id = self._get_least_used_gpu()
            device = torch.device(f"cuda:{gpu_id}")
            self.device_map[model_id] = device
            return device
        return None if not TORCH_AVAILABLE else torch.device("cpu")
    
    def _get_least_used_gpu(self) -> int:
        """Get GPU with lowest utilization."""
        if not GPU_AVAILABLE:
            return 0
            
        gpus = GPUtil.getGPUs()
        if not gpus:
            return 0
        
        return min(range(len(gpus)), key=lambda i: gpus[i].load)

class MemoryManager:
    """Memory management and optimization."""
    
    @staticmethod
    def clear_cache():
        """Clear various caches to free memory."""
        if TORCH_AVAILABLE and torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    @staticmethod
    def get_memory_usage() -> Dict[str, float]:
        """Get current memory usage statistics."""
        memory = psutil.virtual_memory()
        result = {
            "total": memory.total / (1024**3),  # GB
            "available": memory.available / (1024**3),  # GB
            "percent": memory.percent,
            "used": memory.used / (1024**3)  # GB
        }
        
        if TORCH_AVAILABLE and torch.cuda.is_available():
            gpu_memory = torch.cuda.get_memory_stats()
            result["gpu_allocated"] = gpu_memory.get("allocated_bytes.all.current", 0) / (1024**3)
            result["gpu_reserved"] = gpu_memory.get("reserved_bytes.all.current", 0) / (1024**3)
        
        return result

# Global instances
model_cache = ModelCache()
batch_processor = BatchProcessor()
gpu_manager = GPUResourceManager()
memory_manager = MemoryManager()

# Async context manager for resource management
class ResourceManager:
    """Context manager for resource allocation and cleanup."""
    
    def __init__(self, model_id: str):
        self.model_id = model_id
        self.device = None
    
    async def __aenter__(self):
        self.device = gpu_manager.allocate_gpu(self.model_id)
        return self.device
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Cleanup resources
        memory_manager.clear_cache()
        if self.model_id in gpu_manager.device_map:
            del gpu_manager.device_map[self.model_id]
