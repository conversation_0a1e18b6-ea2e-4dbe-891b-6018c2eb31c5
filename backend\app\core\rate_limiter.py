"""
Enterprise Rate Limiter
=======================

Production-ready rate limiting for the Enterprise AI/ML Platform.

Features:
- Token Bucket Algorithm
- Sliding Window Rate Limiting
- User-based & IP-based Limiting
- Redis Backend Support
- Memory-based Fallback
- Configurable Limits per Endpoint
- Burst Handling
- Rate Limit Headers
"""

import asyncio
import time
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import logging

@dataclass
class RateLimit:
    """Rate limit configuration."""
    requests: int  # Number of requests
    window: int    # Time window in seconds
    burst: int     # Burst allowance

@dataclass
class RateLimitStatus:
    """Rate limit status."""
    allowed: bool
    remaining: int
    reset_time: int
    retry_after: Optional[int] = None

class TokenBucket:
    """Token bucket implementation for rate limiting."""
    
    def __init__(self, capacity: int, refill_rate: float):
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate
        self.last_refill = time.time()
    
    def consume(self, tokens: int = 1) -> bool:
        """Consume tokens from bucket."""
        now = time.time()
        
        # Refill tokens
        time_passed = now - self.last_refill
        self.tokens = min(
            self.capacity,
            self.tokens + time_passed * self.refill_rate
        )
        self.last_refill = now
        
        # Check if we have enough tokens
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        
        return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get current bucket status."""
        return {
            'tokens': self.tokens,
            'capacity': self.capacity,
            'refill_rate': self.refill_rate
        }

class SlidingWindow:
    """Sliding window rate limiter."""
    
    def __init__(self, limit: int, window: int):
        self.limit = limit
        self.window = window
        self.requests = deque()
    
    def is_allowed(self) -> Tuple[bool, int]:
        """Check if request is allowed."""
        now = time.time()
        
        # Remove old requests outside window
        while self.requests and self.requests[0] <= now - self.window:
            self.requests.popleft()
        
        # Check if under limit
        if len(self.requests) < self.limit:
            self.requests.append(now)
            return True, self.limit - len(self.requests)
        
        return False, 0
    
    def get_reset_time(self) -> int:
        """Get time when window resets."""
        if not self.requests:
            return int(time.time())
        return int(self.requests[0] + self.window)

class RateLimiter:
    """Enterprise rate limiter with multiple algorithms."""
    
    def __init__(self, redis_client=None):
        self.redis = redis_client
        self.memory_store = defaultdict(dict)
        self.token_buckets = {}
        self.sliding_windows = {}
        
        # Default rate limits
        self.default_limits = {
            'user': RateLimit(requests=1000, window=3600, burst=100),  # 1000/hour
            'ip': RateLimit(requests=100, window=3600, burst=20),      # 100/hour
            'api_key': RateLimit(requests=10000, window=3600, burst=1000)  # 10000/hour
        }
        
        # Endpoint-specific limits
        self.endpoint_limits = {
            '/api/v1/ml/computer-vision/classify': RateLimit(requests=100, window=3600, burst=10),
            '/api/v1/ml/nlp/analyze': RateLimit(requests=500, window=3600, burst=50),
            '/api/v1/ml/time-series/forecast': RateLimit(requests=50, window=3600, burst=5),
            '/api/v1/ml/automl/select-model': RateLimit(requests=10, window=3600, burst=2),
            '/api/v1/ml/deep-learning/train': RateLimit(requests=5, window=3600, burst=1)
        }
        
        logging.info("Rate limiter initialized")
    
    async def check_limit(
        self,
        identifier: str,
        limit_type: str = 'user',
        endpoint: Optional[str] = None
    ) -> RateLimitStatus:
        """Check if request is within rate limit."""
        try:
            # Get applicable rate limit
            rate_limit = self._get_rate_limit(limit_type, endpoint)
            
            # Use Redis if available, otherwise memory
            if self.redis:
                return await self._check_redis_limit(identifier, rate_limit, endpoint)
            else:
                return await self._check_memory_limit(identifier, rate_limit, endpoint)
                
        except Exception as e:
            logging.error(f"Rate limit check failed: {e}")
            # Fail open - allow request if rate limiter fails
            return RateLimitStatus(
                allowed=True,
                remaining=100,
                reset_time=int(time.time() + 3600)
            )
    
    def _get_rate_limit(self, limit_type: str, endpoint: Optional[str]) -> RateLimit:
        """Get rate limit configuration."""
        # Endpoint-specific limits take precedence
        if endpoint and endpoint in self.endpoint_limits:
            return self.endpoint_limits[endpoint]
        
        # Use default limits
        return self.default_limits.get(limit_type, self.default_limits['user'])
    
    async def _check_redis_limit(
        self,
        identifier: str,
        rate_limit: RateLimit,
        endpoint: Optional[str]
    ) -> RateLimitStatus:
        """Check rate limit using Redis backend."""
        key = f"rate_limit:{identifier}:{endpoint or 'default'}"
        now = int(time.time())
        window_start = now - rate_limit.window
        
        try:
            # Use Redis pipeline for atomic operations
            pipe = self.redis.pipeline()
            
            # Remove old entries
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current requests
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(now): now})
            
            # Set expiration
            pipe.expire(key, rate_limit.window)
            
            results = await pipe.execute()
            current_count = results[1]
            
            # Check if under limit
            if current_count < rate_limit.requests:
                remaining = rate_limit.requests - current_count - 1
                return RateLimitStatus(
                    allowed=True,
                    remaining=remaining,
                    reset_time=now + rate_limit.window
                )
            else:
                # Get oldest request time for retry_after
                oldest = await self.redis.zrange(key, 0, 0, withscores=True)
                retry_after = None
                if oldest:
                    retry_after = int(oldest[0][1] + rate_limit.window - now)
                
                return RateLimitStatus(
                    allowed=False,
                    remaining=0,
                    reset_time=now + rate_limit.window,
                    retry_after=retry_after
                )
                
        except Exception as e:
            logging.error(f"Redis rate limit check failed: {e}")
            # Fallback to memory-based limiting
            return await self._check_memory_limit(identifier, rate_limit, endpoint)
    
    async def _check_memory_limit(
        self,
        identifier: str,
        rate_limit: RateLimit,
        endpoint: Optional[str]
    ) -> RateLimitStatus:
        """Check rate limit using memory backend."""
        key = f"{identifier}:{endpoint or 'default'}"
        
        # Initialize sliding window if not exists
        if key not in self.sliding_windows:
            self.sliding_windows[key] = SlidingWindow(
                limit=rate_limit.requests,
                window=rate_limit.window
            )
        
        window = self.sliding_windows[key]
        allowed, remaining = window.is_allowed()
        reset_time = window.get_reset_time()
        
        retry_after = None
        if not allowed:
            retry_after = reset_time - int(time.time())
        
        return RateLimitStatus(
            allowed=allowed,
            remaining=remaining,
            reset_time=reset_time,
            retry_after=retry_after
        )
    
    async def get_limit_info(
        self,
        identifier: str,
        limit_type: str = 'user',
        endpoint: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get current rate limit information."""
        rate_limit = self._get_rate_limit(limit_type, endpoint)
        status = await self.check_limit(identifier, limit_type, endpoint)
        
        return {
            'limit': rate_limit.requests,
            'window': rate_limit.window,
            'remaining': status.remaining,
            'reset_time': status.reset_time,
            'burst_allowance': rate_limit.burst
        }
    
    def configure_endpoint_limit(self, endpoint: str, rate_limit: RateLimit):
        """Configure rate limit for specific endpoint."""
        self.endpoint_limits[endpoint] = rate_limit
        logging.info(f"Configured rate limit for {endpoint}: {asdict(rate_limit)}")
    
    def configure_default_limit(self, limit_type: str, rate_limit: RateLimit):
        """Configure default rate limit for limit type."""
        self.default_limits[limit_type] = rate_limit
        logging.info(f"Configured default {limit_type} rate limit: {asdict(rate_limit)}")
    
    async def reset_limit(self, identifier: str, endpoint: Optional[str] = None):
        """Reset rate limit for identifier."""
        if self.redis:
            key = f"rate_limit:{identifier}:{endpoint or 'default'}"
            await self.redis.delete(key)
        else:
            key = f"{identifier}:{endpoint or 'default'}"
            if key in self.sliding_windows:
                del self.sliding_windows[key]
        
        logging.info(f"Reset rate limit for {identifier}:{endpoint}")
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get rate limiter statistics."""
        stats = {
            'backend': 'redis' if self.redis else 'memory',
            'default_limits': {k: asdict(v) for k, v in self.default_limits.items()},
            'endpoint_limits': {k: asdict(v) for k, v in self.endpoint_limits.items()},
            'active_windows': len(self.sliding_windows) if not self.redis else 'N/A'
        }
        
        if self.redis:
            try:
                # Get Redis info
                info = await self.redis.info()
                stats['redis_info'] = {
                    'connected_clients': info.get('connected_clients', 0),
                    'used_memory': info.get('used_memory_human', 'N/A'),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0)
                }
            except Exception as e:
                stats['redis_error'] = str(e)
        
        return stats

class AdaptiveRateLimiter(RateLimiter):
    """Adaptive rate limiter that adjusts limits based on system load."""
    
    def __init__(self, redis_client=None):
        super().__init__(redis_client)
        self.system_load = 0.0
        self.load_history = deque(maxlen=60)  # Keep 60 seconds of history
        self.last_load_check = time.time()
    
    async def check_limit(
        self,
        identifier: str,
        limit_type: str = 'user',
        endpoint: Optional[str] = None
    ) -> RateLimitStatus:
        """Check rate limit with adaptive adjustment."""
        # Update system load
        await self._update_system_load()
        
        # Get base rate limit
        base_limit = self._get_rate_limit(limit_type, endpoint)
        
        # Adjust limit based on system load
        adjusted_limit = self._adjust_limit_for_load(base_limit)
        
        # Use adjusted limit for checking
        if self.redis:
            return await self._check_redis_limit(identifier, adjusted_limit, endpoint)
        else:
            return await self._check_memory_limit(identifier, adjusted_limit, endpoint)
    
    async def _update_system_load(self):
        """Update system load metrics."""
        now = time.time()
        
        # Update load every 5 seconds
        if now - self.last_load_check > 5:
            try:
                # In production, this would check actual system metrics
                # For now, simulate based on request patterns
                import psutil
                cpu_percent = psutil.cpu_percent()
                memory_percent = psutil.virtual_memory().percent
                
                # Combine CPU and memory load
                self.system_load = (cpu_percent + memory_percent) / 200.0
                self.load_history.append(self.system_load)
                self.last_load_check = now
                
            except ImportError:
                # Fallback if psutil not available
                self.system_load = 0.5  # Assume moderate load
    
    def _adjust_limit_for_load(self, base_limit: RateLimit) -> RateLimit:
        """Adjust rate limit based on system load."""
        if not self.load_history:
            return base_limit
        
        avg_load = sum(self.load_history) / len(self.load_history)
        
        # Reduce limits when system is under high load
        if avg_load > 0.8:  # High load
            multiplier = 0.5
        elif avg_load > 0.6:  # Medium load
            multiplier = 0.7
        elif avg_load > 0.4:  # Low-medium load
            multiplier = 0.9
        else:  # Low load
            multiplier = 1.0
        
        return RateLimit(
            requests=int(base_limit.requests * multiplier),
            window=base_limit.window,
            burst=int(base_limit.burst * multiplier)
        )

# Global rate limiter instance
rate_limiter = RateLimiter()

# Convenience functions
async def check_rate_limit(
    identifier: str,
    limit_type: str = 'user',
    endpoint: Optional[str] = None
) -> RateLimitStatus:
    """Check rate limit for identifier."""
    return await rate_limiter.check_limit(identifier, limit_type, endpoint)

async def get_rate_limit_headers(status: RateLimitStatus) -> Dict[str, str]:
    """Get rate limit headers for HTTP response."""
    headers = {
        'X-RateLimit-Remaining': str(status.remaining),
        'X-RateLimit-Reset': str(status.reset_time)
    }
    
    if status.retry_after:
        headers['Retry-After'] = str(status.retry_after)
    
    return headers

# Export main components
__all__ = [
    'RateLimit', 'RateLimitStatus', 'RateLimiter', 'AdaptiveRateLimiter',
    'TokenBucket', 'SlidingWindow', 'rate_limiter', 'check_rate_limit',
    'get_rate_limit_headers'
]
