"""
Security Module
===============

Comprehensive security implementation with JWT authentication,
OAuth2, RBAC, and encryption utilities.
"""

import secrets
from datetime import datetime, timedelta
from typing import Any, Union, Optional, List
from passlib.context import CryptContext
from jose import JW<PERSON><PERSON>r, jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from cryptography.fernet import <PERSON><PERSON>t
import bcrypt

from app.core.config import settings
from app.models.user import User, UserRole
from app.services.user_service import UserService


# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Security
security = HTTPBearer()

# Encryption
if settings.ENCRYPTION_KEY:
    cipher_suite = Fernet(settings.ENCRYPTION_KEY.encode())
else:
    cipher_suite = None


class SecurityManager:
    """Centralized security management."""
    
    def __init__(self):
        self.user_service = UserService()
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Generate password hash."""
        return pwd_context.hash(password)
    
    def create_access_token(
        self, 
        subject: Union[str, Any], 
        expires_delta: timedelta = None,
        scopes: List[str] = None
    ) -> str:
        """Create JWT access token."""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )
        
        to_encode = {
            "exp": expire,
            "sub": str(subject),
            "type": "access",
            "scopes": scopes or []
        }
        
        encoded_jwt = jwt.encode(
            to_encode, 
            settings.SECRET_KEY, 
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt
    
    def create_refresh_token(self, subject: Union[str, Any]) -> str:
        """Create JWT refresh token."""
        expire = datetime.utcnow() + timedelta(
            days=settings.REFRESH_TOKEN_EXPIRE_DAYS
        )
        
        to_encode = {
            "exp": expire,
            "sub": str(subject),
            "type": "refresh"
        }
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[dict]:
        """Verify and decode JWT token."""
        try:
            payload = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM]
            )
            return payload
        except JWTError:
            return None
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data."""
        if not cipher_suite:
            raise ValueError("Encryption key not configured")
        return cipher_suite.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data."""
        if not cipher_suite:
            raise ValueError("Encryption key not configured")
        return cipher_suite.decrypt(encrypted_data.encode()).decode()
    
    def generate_api_key(self) -> str:
        """Generate secure API key."""
        return secrets.token_urlsafe(32)
    
    def check_permissions(self, user: User, required_permissions: List[str]) -> bool:
        """Check if user has required permissions."""
        user_permissions = self.get_user_permissions(user)
        return all(perm in user_permissions for perm in required_permissions)
    
    def get_user_permissions(self, user: User) -> List[str]:
        """Get user permissions based on role."""
        role_permissions = {
            UserRole.ADMIN: [
                "read:all", "write:all", "delete:all", "manage:users",
                "manage:models", "manage:system", "audit:logs"
            ],
            UserRole.DATA_SCIENTIST: [
                "read:data", "write:data", "read:models", "write:models",
                "train:models", "deploy:models"
            ],
            UserRole.ANALYST: [
                "read:data", "read:models", "create:reports", "view:dashboards"
            ],
            UserRole.VIEWER: [
                "read:data", "view:dashboards"
            ]
        }
        return role_permissions.get(user.role, [])


# Global security manager
security_manager = SecurityManager()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """Get current authenticated user."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = security_manager.verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception
        
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        
        token_type: str = payload.get("type")
        if token_type != "access":
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception
    
    user = await security_manager.user_service.get_user_by_id(user_id)
    if user is None:
        raise credentials_exception
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user."""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


def require_permissions(permissions: List[str]):
    """Decorator to require specific permissions."""
    def permission_checker(current_user: User = Depends(get_current_active_user)):
        if not security_manager.check_permissions(current_user, permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return current_user
    return permission_checker


def require_role(required_role: UserRole):
    """Decorator to require specific role."""
    def role_checker(current_user: User = Depends(get_current_active_user)):
        if current_user.role != required_role and current_user.role != UserRole.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient role privileges"
            )
        return current_user
    return role_checker


async def verify_token(token: str) -> Optional[dict]:
    """Verify JWT token (for external use)."""
    return security_manager.verify_token(token)


class RateLimiter:
    """Rate limiting implementation."""
    
    def __init__(self, redis_client):
        self.redis = redis_client
    
    async def is_allowed(
        self, 
        key: str, 
        limit: int, 
        window: int
    ) -> bool:
        """Check if request is within rate limit."""
        current = await self.redis.get(key)
        
        if current is None:
            await self.redis.setex(key, window, 1)
            return True
        
        if int(current) >= limit:
            return False
        
        await self.redis.incr(key)
        return True


class AuditLogger:
    """Security audit logging."""
    
    def __init__(self):
        pass
    
    async def log_authentication(
        self, 
        user_id: str, 
        action: str, 
        success: bool,
        ip_address: str = None,
        user_agent: str = None
    ):
        """Log authentication events."""
        # Implementation would log to database or external service
        pass
    
    async def log_authorization(
        self,
        user_id: str,
        resource: str,
        action: str,
        granted: bool,
        ip_address: str = None
    ):
        """Log authorization events."""
        # Implementation would log to database or external service
        pass
    
    async def log_data_access(
        self,
        user_id: str,
        resource_type: str,
        resource_id: str,
        action: str,
        ip_address: str = None
    ):
        """Log data access events."""
        # Implementation would log to database or external service
        pass
