"""
Enterprise AI/ML Platform - FastAPI Backend
==========================================

Production-ready FastAPI backend with async support, authentication,
and comprehensive API endpoints for the AI/ML platform.

Author: AI Platform Team
Version: 2.0.0
License: MIT
"""

import os
import asyncio
from contextlib import asynccontextmanager
from typing import List, Optional

import uvicorn
from fastapi import FastAPI, Depends, HTTPException, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from fastapi.responses import J<PERSON>NResponse
from prometheus_fastapi_instrumentator import Instrumentator

from app.core.config import settings
from app.core.enhanced_config import enhanced_settings, validate_configuration
from app.core.security import verify_token, get_current_user
from app.core.database import init_db, close_db
from app.core.logging import setup_logging, logger
from app.core.exceptions import setup_exception_handlers
from app.core.performance import model_cache, batch_processor, gpu_manager, memory_manager
from app.core.error_handling import error_tracker, health_checker
from app.middleware.security import SecurityMiddleware
from app.api.v1.api import api_router
from app.api.v1.ml_endpoints import router as ml_router
from app.api.v2 import api_v2_router
from app.models.user import User
from app.services.enterprise_ml_service import ml_service
from app.services.monitoring_service import MonitoringService
from app.core.rate_limiter import rate_limiter
from app.core.cache import cache_manager

# Health check functions
async def check_database_health():
    """Check database connectivity."""
    try:
        # Simple database query
        from app.core.database import get_db
        async with get_db() as db:
            await db.execute("SELECT 1")
        return {"status": "healthy", "latency": "< 10ms"}
    except Exception as e:
        raise Exception(f"Database unhealthy: {str(e)}")

async def check_ml_service_health():
    """Check ML service health."""
    try:
        return await ml_service.health_check()
    except Exception as e:
        raise Exception(f"ML service unhealthy: {str(e)}")

async def check_cache_health():
    """Check cache service health."""
    try:
        await cache_manager.ping()
        return {"status": "healthy", "cache_size": len(model_cache.cache)}
    except Exception as e:
        raise Exception(f"Cache unhealthy: {str(e)}")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting AI/ML Platform Backend...")

    # Validate configuration
    if not validate_configuration():
        logger.error("Configuration validation failed")
        raise RuntimeError("Invalid configuration")

    # Initialize database connections
    await init_db()

    # Initialize performance components
    await model_cache.get_model("warmup")  # Warm up cache
    logger.info("Performance components initialized")

    # Initialize enterprise ML service
    await ml_service.initialize()

    # Initialize monitoring
    monitoring_service = MonitoringService()
    await monitoring_service.start()

    # Setup health checks
    health_checker.register_check("database", check_database_health)
    health_checker.register_check("ml_service", check_ml_service_health)
    health_checker.register_check("cache", check_cache_health)

    # Setup Prometheus metrics
    instrumentator = Instrumentator()
    instrumentator.instrument(app).expose(app)

    logger.info("Backend startup complete")

    yield

    # Shutdown
    logger.info("Shutting down AI/ML Platform Backend...")

    # Close database connections
    await close_db()

    # Cleanup ML services
    await ml_service.cleanup()

    # Cleanup performance components
    memory_manager.clear_cache()

    # Stop monitoring
    await monitoring_service.stop()

    logger.info("Backend shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="Enterprise AI/ML Platform API",
    description="Production-ready API for comprehensive AI/ML operations",
    version="2.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    lifespan=lifespan
)

# Setup logging
setup_logging()

# Setup exception handlers
setup_exception_handlers(app)

# Security middleware
security = HTTPBearer()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Trusted host middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

# Compression middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)

# Security middleware
app.add_middleware(SecurityMiddleware)


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "timestamp": asyncio.get_event_loop().time()
    }


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint."""
    # Metrics are exposed by instrumentator
    pass


# Include API routes
app.include_router(api_router, prefix="/api/v1")
app.include_router(ml_router, prefix="")
app.include_router(api_v2_router, prefix="/api/v2")


# WebSocket endpoint for real-time updates
@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket, client_id: str):
    """WebSocket endpoint for real-time communication."""
    from app.services.websocket_service import WebSocketManager
    
    manager = WebSocketManager()
    await manager.connect(websocket, client_id)
    
    try:
        while True:
            data = await websocket.receive_text()
            await manager.send_personal_message(f"Echo: {data}", client_id)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        manager.disconnect(client_id)


# Background task example
@app.post("/api/v1/ml/train-async")
async def train_model_async(
    background_tasks: BackgroundTasks,
    model_config: dict,
    current_user: User = Depends(get_current_user)
):
    """Start asynchronous model training."""
    from app.tasks.ml_tasks import train_model_task
    
    task_id = f"train_{current_user.id}_{asyncio.get_event_loop().time()}"
    
    background_tasks.add_task(
        train_model_task,
        task_id=task_id,
        user_id=current_user.id,
        config=model_config
    )
    
    return {
        "task_id": task_id,
        "status": "started",
        "message": "Model training started in background"
    }


# Rate limiting example
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)


@app.get("/api/v1/limited")
@limiter.limit("5/minute")
async def limited_endpoint(request):
    """Rate limited endpoint example."""
    return {"message": "This endpoint is rate limited"}


# Error handling
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler."""
    logger.error(f"HTTP {exc.status_code}: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code,
            "timestamp": asyncio.get_event_loop().time()
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "status_code": 500,
            "timestamp": asyncio.get_event_loop().time()
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        workers=settings.WORKERS if not settings.DEBUG else 1,
        log_config=None  # Use our custom logging
    )
