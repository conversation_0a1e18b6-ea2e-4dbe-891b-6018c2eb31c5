"""
Comprehensive Security Middleware
================================

Enterprise-grade security middleware with:
- Security headers
- Rate limiting
- Request validation
- Attack prevention
- Audit logging
"""

import time
import hashlib
import json
from typing import Dict, Any, Optional, List
from fastapi import Request, Response, HTTPException
from fastapi.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import redis
from datetime import datetime, timedelta
import ipaddress
import re
from urllib.parse import urlparse

from ..core.enhanced_config import enhanced_settings
from ..core.error_handling import error_tracker

class SecurityHeaders:
    """Security headers configuration."""
    
    @staticmethod
    def get_headers() -> Dict[str, str]:
        """Get comprehensive security headers."""
        return {
            # Content Security Policy
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; "
                "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
                "font-src 'self' https://fonts.gstatic.com; "
                "img-src 'self' data: https:; "
                "connect-src 'self' https: wss:; "
                "frame-ancestors 'none'; "
                "base-uri 'self'; "
                "form-action 'self'"
            ),
            
            # XSS Protection
            "X-XSS-Protection": "1; mode=block",
            
            # Content Type Options
            "X-Content-Type-Options": "nosniff",
            
            # Frame Options
            "X-Frame-Options": "DENY",
            
            # Referrer Policy
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # Permissions Policy
            "Permissions-Policy": (
                "geolocation=(), "
                "microphone=(), "
                "camera=(), "
                "payment=(), "
                "usb=(), "
                "magnetometer=(), "
                "gyroscope=(), "
                "speaker=()"
            ),
            
            # HSTS (HTTP Strict Transport Security)
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
            
            # Cross-Origin Policies
            "Cross-Origin-Embedder-Policy": "require-corp",
            "Cross-Origin-Opener-Policy": "same-origin",
            "Cross-Origin-Resource-Policy": "same-origin",
            
            # Server Information
            "Server": "Enterprise-AI-ML-Platform",
            "X-Powered-By": "",  # Remove server information
            
            # Cache Control for sensitive endpoints
            "Cache-Control": "no-store, no-cache, must-revalidate, private",
            "Pragma": "no-cache",
            "Expires": "0"
        }

class RateLimiter:
    """Advanced rate limiting with multiple strategies."""
    
    def __init__(self, redis_client: redis.Redis = None):
        self.redis = redis_client or redis.Redis.from_url(enhanced_settings.redis.url)
        
        # Rate limit configurations
        self.limits = {
            "default": {"requests": 1000, "window": 3600},  # 1000 requests per hour
            "auth": {"requests": 10, "window": 300},         # 10 auth attempts per 5 minutes
            "api": {"requests": 100, "window": 60},          # 100 API calls per minute
            "upload": {"requests": 10, "window": 3600},      # 10 uploads per hour
            "ml_inference": {"requests": 50, "window": 60},  # 50 ML inferences per minute
            "premium": {"requests": 10000, "window": 3600},  # Premium users
        }
    
    def get_client_id(self, request: Request) -> str:
        """Get unique client identifier."""
        # Try to get user ID from JWT token
        user_id = getattr(request.state, 'user_id', None)
        if user_id:
            return f"user:{user_id}"
        
        # Fall back to IP address
        client_ip = self.get_client_ip(request)
        return f"ip:{client_ip}"
    
    def get_client_ip(self, request: Request) -> str:
        """Get client IP address considering proxies."""
        # Check for forwarded headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host
    
    def get_rate_limit_key(self, client_id: str, endpoint: str, limit_type: str) -> str:
        """Generate rate limit key."""
        return f"rate_limit:{limit_type}:{client_id}:{endpoint}"
    
    async def is_rate_limited(
        self,
        request: Request,
        limit_type: str = "default",
        custom_limit: Dict[str, int] = None
    ) -> tuple[bool, Dict[str, Any]]:
        """Check if request is rate limited."""
        client_id = self.get_client_id(request)
        endpoint = request.url.path
        
        # Get limit configuration
        limit_config = custom_limit or self.limits.get(limit_type, self.limits["default"])
        max_requests = limit_config["requests"]
        window_seconds = limit_config["window"]
        
        # Generate key
        key = self.get_rate_limit_key(client_id, endpoint, limit_type)
        
        try:
            # Use sliding window log algorithm
            now = time.time()
            window_start = now - window_seconds
            
            # Remove old entries
            self.redis.zremrangebyscore(key, 0, window_start)
            
            # Count current requests
            current_requests = self.redis.zcard(key)
            
            # Check if limit exceeded
            if current_requests >= max_requests:
                # Get reset time
                oldest_request = self.redis.zrange(key, 0, 0, withscores=True)
                reset_time = oldest_request[0][1] + window_seconds if oldest_request else now + window_seconds
                
                return True, {
                    "limit": max_requests,
                    "remaining": 0,
                    "reset": int(reset_time),
                    "retry_after": int(reset_time - now)
                }
            
            # Add current request
            self.redis.zadd(key, {str(now): now})
            self.redis.expire(key, window_seconds)
            
            return False, {
                "limit": max_requests,
                "remaining": max_requests - current_requests - 1,
                "reset": int(now + window_seconds),
                "retry_after": 0
            }
            
        except Exception as e:
            # If Redis fails, allow request but log error
            error_tracker.track_error(e, {"component": "rate_limiter", "client_id": client_id})
            return False, {"limit": max_requests, "remaining": max_requests, "reset": 0, "retry_after": 0}

class SecurityValidator:
    """Request security validation."""
    
    def __init__(self):
        # Malicious patterns
        self.sql_injection_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
            r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
            r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
            r"(--|#|/\*|\*/)",
            r"(\bxp_cmdshell\b|\bsp_executesql\b)"
        ]
        
        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>.*?</iframe>",
            r"<object[^>]*>.*?</object>",
            r"<embed[^>]*>.*?</embed>"
        ]
        
        self.path_traversal_patterns = [
            r"\.\./",
            r"\.\.\\",
            r"%2e%2e%2f",
            r"%2e%2e%5c"
        ]
        
        # Compile patterns
        self.sql_regex = [re.compile(pattern, re.IGNORECASE) for pattern in self.sql_injection_patterns]
        self.xss_regex = [re.compile(pattern, re.IGNORECASE) for pattern in self.xss_patterns]
        self.path_regex = [re.compile(pattern, re.IGNORECASE) for pattern in self.path_traversal_patterns]
    
    def validate_request(self, request: Request, body: str = None) -> Dict[str, Any]:
        """Validate request for security threats."""
        threats = []
        
        # Check URL path
        path = request.url.path
        if self._check_path_traversal(path):
            threats.append("path_traversal")
        
        # Check query parameters
        for key, value in request.query_params.items():
            if self._check_sql_injection(value) or self._check_sql_injection(key):
                threats.append("sql_injection")
            if self._check_xss(value) or self._check_xss(key):
                threats.append("xss")
        
        # Check request body
        if body:
            if self._check_sql_injection(body):
                threats.append("sql_injection")
            if self._check_xss(body):
                threats.append("xss")
        
        # Check headers for suspicious content
        for header_name, header_value in request.headers.items():
            if self._check_xss(header_value):
                threats.append("xss")
        
        return {
            "is_safe": len(threats) == 0,
            "threats": list(set(threats)),
            "risk_level": self._calculate_risk_level(threats)
        }
    
    def _check_sql_injection(self, text: str) -> bool:
        """Check for SQL injection patterns."""
        return any(pattern.search(text) for pattern in self.sql_regex)
    
    def _check_xss(self, text: str) -> bool:
        """Check for XSS patterns."""
        return any(pattern.search(text) for pattern in self.xss_regex)
    
    def _check_path_traversal(self, text: str) -> bool:
        """Check for path traversal patterns."""
        return any(pattern.search(text) for pattern in self.path_regex)
    
    def _calculate_risk_level(self, threats: List[str]) -> str:
        """Calculate risk level based on threats."""
        if not threats:
            return "low"
        elif len(threats) == 1:
            return "medium"
        else:
            return "high"

class SecurityAuditLogger:
    """Security event audit logging."""
    
    def __init__(self):
        self.redis = redis.Redis.from_url(enhanced_settings.redis.url)
    
    def log_security_event(
        self,
        event_type: str,
        request: Request,
        details: Dict[str, Any] = None,
        severity: str = "info"
    ):
        """Log security event."""
        event = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type,
            "severity": severity,
            "client_ip": self._get_client_ip(request),
            "user_agent": request.headers.get("User-Agent", ""),
            "method": request.method,
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "headers": dict(request.headers),
            "details": details or {}
        }
        
        # Store in Redis for real-time monitoring
        key = f"security_events:{datetime.utcnow().strftime('%Y-%m-%d')}"
        self.redis.lpush(key, json.dumps(event))
        self.redis.expire(key, 86400 * 30)  # Keep for 30 days
        
        # Also log to application logger
        import logging
        logger = logging.getLogger("security")
        
        if severity == "critical":
            logger.critical(f"Security Event: {event_type} - {details}")
        elif severity == "high":
            logger.error(f"Security Event: {event_type} - {details}")
        elif severity == "medium":
            logger.warning(f"Security Event: {event_type} - {details}")
        else:
            logger.info(f"Security Event: {event_type} - {details}")
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        return request.client.host

class SecurityMiddleware(BaseHTTPMiddleware):
    """Comprehensive security middleware."""
    
    def __init__(self, app, config: Dict[str, Any] = None):
        super().__init__(app)
        self.config = config or {}
        self.rate_limiter = RateLimiter()
        self.validator = SecurityValidator()
        self.audit_logger = SecurityAuditLogger()
        self.security_headers = SecurityHeaders()
        
        # Whitelist for security bypasses (internal services)
        self.whitelist_ips = self.config.get("whitelist_ips", [])
        self.whitelist_paths = self.config.get("whitelist_paths", ["/health", "/metrics"])
    
    async def dispatch(self, request: Request, call_next):
        """Process request through security middleware."""
        start_time = time.time()
        
        try:
            # Skip security checks for whitelisted IPs/paths
            if self._is_whitelisted(request):
                response = await call_next(request)
                return self._add_security_headers(response)
            
            # Read request body for validation
            body = None
            if request.method in ["POST", "PUT", "PATCH"]:
                body = await self._read_body(request)
            
            # 1. Rate limiting check
            is_limited, rate_info = await self._check_rate_limit(request)
            if is_limited:
                return self._create_rate_limit_response(rate_info)
            
            # 2. Security validation
            validation_result = self.validator.validate_request(request, body)
            if not validation_result["is_safe"]:
                return self._create_security_response(request, validation_result)
            
            # 3. Process request
            response = await call_next(request)
            
            # 4. Add security headers
            response = self._add_security_headers(response)
            
            # 5. Add rate limit headers
            self._add_rate_limit_headers(response, rate_info)
            
            # 6. Log successful request
            processing_time = time.time() - start_time
            if processing_time > 5.0:  # Log slow requests
                self.audit_logger.log_security_event(
                    "slow_request",
                    request,
                    {"processing_time": processing_time},
                    "medium"
                )
            
            return response
            
        except Exception as e:
            # Log security middleware errors
            self.audit_logger.log_security_event(
                "middleware_error",
                request,
                {"error": str(e)},
                "high"
            )
            
            # Return error response
            return JSONResponse(
                status_code=500,
                content={"error": "Internal security error"}
            )
    
    def _is_whitelisted(self, request: Request) -> bool:
        """Check if request is whitelisted."""
        # Check path whitelist
        if request.url.path in self.whitelist_paths:
            return True
        
        # Check IP whitelist
        client_ip = self.rate_limiter.get_client_ip(request)
        try:
            client_addr = ipaddress.ip_address(client_ip)
            for whitelist_ip in self.whitelist_ips:
                if client_addr in ipaddress.ip_network(whitelist_ip):
                    return True
        except ValueError:
            pass
        
        return False
    
    async def _read_body(self, request: Request) -> str:
        """Read request body safely."""
        try:
            body = await request.body()
            return body.decode('utf-8')
        except Exception:
            return ""
    
    async def _check_rate_limit(self, request: Request) -> tuple[bool, Dict[str, Any]]:
        """Check rate limiting."""
        # Determine rate limit type based on endpoint
        path = request.url.path
        
        if "/auth/" in path:
            limit_type = "auth"
        elif "/api/v" in path and "/ml/" in path:
            limit_type = "ml_inference"
        elif "/upload" in path:
            limit_type = "upload"
        elif "/api/" in path:
            limit_type = "api"
        else:
            limit_type = "default"
        
        return await self.rate_limiter.is_rate_limited(request, limit_type)
    
    def _create_rate_limit_response(self, rate_info: Dict[str, Any]) -> JSONResponse:
        """Create rate limit exceeded response."""
        return JSONResponse(
            status_code=429,
            content={
                "error": "Rate limit exceeded",
                "message": f"Too many requests. Try again in {rate_info['retry_after']} seconds.",
                "retry_after": rate_info["retry_after"]
            },
            headers={
                "Retry-After": str(rate_info["retry_after"]),
                "X-RateLimit-Limit": str(rate_info["limit"]),
                "X-RateLimit-Remaining": str(rate_info["remaining"]),
                "X-RateLimit-Reset": str(rate_info["reset"])
            }
        )
    
    def _create_security_response(self, request: Request, validation_result: Dict[str, Any]) -> JSONResponse:
        """Create security threat response."""
        # Log security threat
        self.audit_logger.log_security_event(
            "security_threat",
            request,
            validation_result,
            validation_result["risk_level"]
        )
        
        return JSONResponse(
            status_code=400,
            content={
                "error": "Security validation failed",
                "message": "Request contains potentially malicious content"
            }
        )
    
    def _add_security_headers(self, response: Response) -> Response:
        """Add security headers to response."""
        headers = self.security_headers.get_headers()
        for header_name, header_value in headers.items():
            response.headers[header_name] = header_value
        return response
    
    def _add_rate_limit_headers(self, response: Response, rate_info: Dict[str, Any]):
        """Add rate limit headers to response."""
        response.headers["X-RateLimit-Limit"] = str(rate_info["limit"])
        response.headers["X-RateLimit-Remaining"] = str(rate_info["remaining"])
        response.headers["X-RateLimit-Reset"] = str(rate_info["reset"])
