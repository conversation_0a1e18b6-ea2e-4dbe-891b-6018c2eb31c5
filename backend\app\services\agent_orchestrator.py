"""
Agent Orchestrator for NeuroFlowAI
==================================

Central coordination system for all autonomous agents.
Manages agent lifecycle, task distribution, and inter-agent communication.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional
from datetime import datetime
from enum import Enum

from ..agents import (
    BaseAgent, ArchitectAgent, EngineerAgent, TrainerAgent, DevOpsAgent,
    AgentMessage, AgentTask, MessageType
)
import logging

logger = logging.getLogger(__name__)


class WorkflowStatus(str, Enum):
    """Workflow execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"
    CANCELLED = "cancelled"


class AgentOrchestrator:
    """
    Central orchestrator for managing all NeuroFlowAI agents.
    
    Responsibilities:
    - Agent lifecycle management
    - Task distribution and coordination
    - Inter-agent message routing
    - Workflow execution management
    - Resource allocation coordination
    - Performance monitoring and optimization
    """
    
    def __init__(self):
        # Agent registry
        self.agents: Dict[str, BaseAgent] = {}
        self.agent_types = {
            "architect": <PERSON><PERSON><PERSON>,
            "engineer": <PERSON><PERSON><PERSON>,
            "trainer": <PERSON>er<PERSON><PERSON>,
            "devops": <PERSON>OpsAgent
        }
        
        # Workflow management
        self.active_workflows: Dict[str, Dict[str, Any]] = {}
        self.workflow_history: List[Dict[str, Any]] = []
        
        # Message routing
        self.message_queue: List[AgentMessage] = []
        self.message_handlers = {}
        
        # Performance tracking
        self.orchestrator_metrics = {
            "total_workflows": 0,
            "successful_workflows": 0,
            "failed_workflows": 0,
            "average_workflow_time": 0.0,
            "agent_utilization": {},
            "message_throughput": 0.0
        }
        
        # Configuration
        self.is_running = False
        self.max_concurrent_workflows = 10
        
    async def start(self) -> None:
        """Start the agent orchestrator."""
        self.is_running = True
        logger.info("Agent Orchestrator starting...")
        
        # Initialize default agents
        await self._initialize_default_agents()
        
        # Start message processing loop
        asyncio.create_task(self._message_processing_loop())
        
        # Start workflow monitoring loop
        asyncio.create_task(self._workflow_monitoring_loop())
        
        logger.info("Agent Orchestrator started successfully")
    
    async def stop(self) -> None:
        """Stop the agent orchestrator."""
        self.is_running = False
        
        # Stop all agents
        for agent in self.agents.values():
            await agent.stop()
        
        logger.info("Agent Orchestrator stopped")
    
    async def _initialize_default_agents(self) -> None:
        """Initialize the default set of agents."""
        default_agents = [
            ("architect_001", "architect"),
            ("engineer_001", "engineer"),
            ("trainer_001", "trainer"),
            ("devops_001", "devops")
        ]
        
        for agent_id, agent_type in default_agents:
            await self.create_agent(agent_id, agent_type)
    
    async def create_agent(self, agent_id: str, agent_type: str, **kwargs) -> str:
        """Create and register a new agent."""
        if agent_id in self.agents:
            raise ValueError(f"Agent {agent_id} already exists")
        
        if agent_type not in self.agent_types:
            raise ValueError(f"Unknown agent type: {agent_type}")
        
        # Create agent instance
        agent_class = self.agent_types[agent_type]
        agent = agent_class(agent_id=agent_id, **kwargs)
        
        # Register agent
        self.agents[agent_id] = agent
        
        # Start agent
        await agent.start()
        
        # Setup message routing
        agent.send_message = self._create_message_sender(agent_id)
        
        logger.info(f"Created and started agent {agent_id} of type {agent_type}")
        
        return agent_id
    
    def _create_message_sender(self, sender_id: str):
        """Create a message sender function for an agent."""
        async def send_message(message: AgentMessage) -> None:
            message.sender_id = sender_id
            await self.route_message(message)
        
        return send_message
    
    async def route_message(self, message: AgentMessage) -> None:
        """Route a message between agents."""
        # Add to message queue for processing
        self.message_queue.append(message)
        
        logger.debug(f"Routed message {message.id} from {message.sender_id} to {message.receiver_id}")
    
    async def _message_processing_loop(self) -> None:
        """Main message processing loop."""
        while self.is_running:
            try:
                # Process pending messages
                while self.message_queue:
                    message = self.message_queue.pop(0)
                    await self._process_message(message)
                
                # Brief pause to prevent CPU spinning
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in message processing loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _process_message(self, message: AgentMessage) -> None:
        """Process a single message."""
        try:
            receiver_id = message.receiver_id
            
            if receiver_id in self.agents:
                # Route to specific agent
                agent = self.agents[receiver_id]
                await agent.receive_message(message)
            elif receiver_id == "orchestrator":
                # Handle orchestrator messages
                await self._handle_orchestrator_message(message)
            else:
                logger.warning(f"Unknown message receiver: {receiver_id}")
                
        except Exception as e:
            logger.error(f"Error processing message {message.id}: {e}")
    
    async def _handle_orchestrator_message(self, message: AgentMessage) -> None:
        """Handle messages directed to the orchestrator."""
        if message.message_type == MessageType.COORDINATION:
            # Handle coordination requests
            await self._handle_coordination_request(message)
        elif message.message_type == MessageType.STATUS_UPDATE:
            # Handle status updates
            await self._handle_status_update(message)
    
    async def execute_workflow(
        self, 
        workflow_definition: Dict[str, Any],
        user_id: str = "system"
    ) -> str:
        """Execute a complete AI/ML workflow."""
        
        workflow_id = f"workflow_{int(datetime.now().timestamp())}"
        
        # Create workflow record
        workflow = {
            "workflow_id": workflow_id,
            "user_id": user_id,
            "definition": workflow_definition,
            "status": WorkflowStatus.PENDING,
            "start_time": datetime.now(),
            "end_time": None,
            "progress": 0.0,
            "current_stage": "initialization",
            "agent_tasks": {},
            "results": {},
            "logs": []
        }
        
        self.active_workflows[workflow_id] = workflow
        
        # Start workflow execution
        asyncio.create_task(self._execute_workflow_async(workflow))
        
        logger.info(f"Started workflow {workflow_id} for user {user_id}")
        
        return workflow_id
    
    async def _execute_workflow_async(self, workflow: Dict[str, Any]) -> None:
        """Execute workflow asynchronously."""
        workflow_id = workflow["workflow_id"]
        
        try:
            workflow["status"] = WorkflowStatus.RUNNING
            
            # Execute workflow stages
            stages = [
                "data_analysis",
                "architecture_design", 
                "feature_engineering",
                "model_training",
                "model_evaluation",
                "deployment"
            ]
            
            for i, stage in enumerate(stages):
                workflow["current_stage"] = stage
                
                # Execute stage
                stage_result = await self._execute_workflow_stage(workflow, stage)
                workflow["results"][stage] = stage_result
                
                # Update progress
                workflow["progress"] = (i + 1) / len(stages)
                
                # Log stage completion
                workflow["logs"].append({
                    "timestamp": datetime.now().isoformat(),
                    "stage": stage,
                    "status": "completed",
                    "result": stage_result
                })
                
                logger.info(f"Workflow {workflow_id} completed stage: {stage}")
            
            # Mark workflow as completed
            workflow["status"] = WorkflowStatus.COMPLETED
            workflow["end_time"] = datetime.now()
            
            # Update metrics
            await self._update_workflow_metrics(workflow)
            
            logger.info(f"Workflow {workflow_id} completed successfully")
            
        except Exception as e:
            workflow["status"] = WorkflowStatus.FAILED
            workflow["error"] = str(e)
            workflow["end_time"] = datetime.now()
            
            logger.error(f"Workflow {workflow_id} failed: {e}")
        
        finally:
            # Move to workflow history
            self.workflow_history.append(workflow)
            if workflow_id in self.active_workflows:
                del self.active_workflows[workflow_id]
    
    async def _execute_workflow_stage(
        self, 
        workflow: Dict[str, Any], 
        stage: str
    ) -> Dict[str, Any]:
        """Execute a single workflow stage."""
        
        # Map stages to responsible agents
        stage_agent_mapping = {
            "data_analysis": "architect_001",
            "architecture_design": "architect_001", 
            "feature_engineering": "engineer_001",
            "model_training": "trainer_001",
            "model_evaluation": "trainer_001",
            "deployment": "devops_001"
        }
        
        agent_id = stage_agent_mapping.get(stage)
        if not agent_id or agent_id not in self.agents:
            raise ValueError(f"No agent available for stage: {stage}")
        
        # Create task for the stage
        task = AgentTask(
            name=f"workflow_{stage}",
            description=f"Execute {stage} stage of workflow {workflow['workflow_id']}",
            parameters={
                "workflow_id": workflow["workflow_id"],
                "stage": stage,
                "workflow_definition": workflow["definition"],
                "previous_results": workflow["results"]
            },
            priority=3  # High priority for workflow tasks
        )
        
        # Assign task to agent
        agent = self.agents[agent_id]
        task_id = await agent.assign_task(task)
        
        # Track task in workflow
        workflow["agent_tasks"][stage] = {
            "agent_id": agent_id,
            "task_id": task_id,
            "status": "assigned"
        }
        
        # Wait for task completion
        return await self._wait_for_task_completion(agent, task_id)
    
    async def _wait_for_task_completion(
        self, 
        agent: BaseAgent, 
        task_id: str,
        timeout: int = 3600  # 1 hour timeout
    ) -> Dict[str, Any]:
        """Wait for a task to complete."""
        
        start_time = datetime.now()
        
        while True:
            # Check if task exists and get its status
            if task_id in agent.current_tasks:
                task = agent.current_tasks[task_id]
                
                if task.status == "completed":
                    return task.result or {}
                elif task.status == "failed":
                    raise Exception(f"Task {task_id} failed: {task.error}")
            
            # Check timeout
            elapsed = (datetime.now() - start_time).total_seconds()
            if elapsed > timeout:
                raise TimeoutError(f"Task {task_id} timed out after {timeout} seconds")
            
            # Wait before checking again
            await asyncio.sleep(1.0)
    
    async def _workflow_monitoring_loop(self) -> None:
        """Monitor active workflows and handle issues."""
        while self.is_running:
            try:
                # Check for stuck workflows
                current_time = datetime.now()
                
                for workflow_id, workflow in list(self.active_workflows.items()):
                    # Check if workflow has been running too long
                    elapsed = (current_time - workflow["start_time"]).total_seconds()
                    
                    if elapsed > 7200:  # 2 hours
                        logger.warning(f"Workflow {workflow_id} has been running for {elapsed/3600:.1f} hours")
                        
                        # Consider cancelling stuck workflows
                        if elapsed > 14400:  # 4 hours
                            workflow["status"] = WorkflowStatus.CANCELLED
                            workflow["error"] = "Workflow cancelled due to timeout"
                            workflow["end_time"] = current_time
                            
                            # Move to history
                            self.workflow_history.append(workflow)
                            del self.active_workflows[workflow_id]
                            
                            logger.warning(f"Cancelled stuck workflow {workflow_id}")
                
                # Sleep before next check
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in workflow monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get the status of a workflow."""
        
        # Check active workflows
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows[workflow_id]
            return self._format_workflow_status(workflow)
        
        # Check workflow history
        for workflow in self.workflow_history:
            if workflow["workflow_id"] == workflow_id:
                return self._format_workflow_status(workflow)
        
        raise ValueError(f"Workflow {workflow_id} not found")
    
    def _format_workflow_status(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """Format workflow status for API response."""
        
        return {
            "workflow_id": workflow["workflow_id"],
            "status": workflow["status"].value if hasattr(workflow["status"], "value") else workflow["status"],
            "progress": workflow["progress"],
            "current_stage": workflow["current_stage"],
            "start_time": workflow["start_time"].isoformat(),
            "end_time": workflow["end_time"].isoformat() if workflow.get("end_time") else None,
            "agent_tasks": workflow.get("agent_tasks", {}),
            "error": workflow.get("error"),
            "logs": workflow.get("logs", [])[-10:]  # Last 10 log entries
        }
    
    async def get_agent_status(self, agent_id: str = None) -> Dict[str, Any]:
        """Get status of agents."""
        
        if agent_id:
            if agent_id not in self.agents:
                raise ValueError(f"Agent {agent_id} not found")
            
            agent = self.agents[agent_id]
            return agent.get_status()
        else:
            # Return status of all agents
            return {
                agent_id: agent.get_status()
                for agent_id, agent in self.agents.items()
            }
    
    async def _update_workflow_metrics(self, workflow: Dict[str, Any]) -> None:
        """Update orchestrator metrics based on completed workflow."""
        
        self.orchestrator_metrics["total_workflows"] += 1
        
        if workflow["status"] == WorkflowStatus.COMPLETED:
            self.orchestrator_metrics["successful_workflows"] += 1
        else:
            self.orchestrator_metrics["failed_workflows"] += 1
        
        # Update average workflow time
        if workflow.get("start_time") and workflow.get("end_time"):
            duration = (workflow["end_time"] - workflow["start_time"]).total_seconds()
            total_workflows = self.orchestrator_metrics["total_workflows"]
            current_avg = self.orchestrator_metrics["average_workflow_time"]
            
            self.orchestrator_metrics["average_workflow_time"] = (
                (current_avg * (total_workflows - 1) + duration) / total_workflows
            )
    
    async def _handle_coordination_request(self, message: AgentMessage) -> None:
        """Handle coordination requests between agents."""
        request_type = message.content.get("request_type")
        
        if request_type == "resource_allocation":
            await self._handle_resource_allocation_request(message)
        elif request_type == "task_dependency":
            await self._handle_task_dependency_request(message)
    
    async def _handle_status_update(self, message: AgentMessage) -> None:
        """Handle status updates from agents."""
        agent_id = message.sender_id
        status_info = message.content
        
        # Update agent utilization metrics
        if agent_id not in self.orchestrator_metrics["agent_utilization"]:
            self.orchestrator_metrics["agent_utilization"][agent_id] = []
        
        self.orchestrator_metrics["agent_utilization"][agent_id].append({
            "timestamp": datetime.now().isoformat(),
            "status": status_info
        })
        
        # Keep only last 100 status updates per agent
        if len(self.orchestrator_metrics["agent_utilization"][agent_id]) > 100:
            self.orchestrator_metrics["agent_utilization"][agent_id] = \
                self.orchestrator_metrics["agent_utilization"][agent_id][-100:]
    
    async def _handle_resource_allocation_request(self, message: AgentMessage) -> None:
        """Handle resource allocation requests."""
        # Placeholder for resource allocation logic
        logger.info(f"Handling resource allocation request from {message.sender_id}")
    
    async def _handle_task_dependency_request(self, message: AgentMessage) -> None:
        """Handle task dependency coordination."""
        # Placeholder for task dependency logic
        logger.info(f"Handling task dependency request from {message.sender_id}")
    
    def get_orchestrator_metrics(self) -> Dict[str, Any]:
        """Get orchestrator performance metrics."""
        return {
            **self.orchestrator_metrics,
            "active_workflows": len(self.active_workflows),
            "active_agents": len([a for a in self.agents.values() if a.is_running]),
            "message_queue_size": len(self.message_queue)
        }
