"""
API Gateway Service for NeuroFlowAI
===================================

Dynamic API generation and management for deployed models.
Handles routing, authentication, rate limiting, and monitoring.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from dataclasses import dataclass, field
from fastapi import FastAP<PERSON>, HTTPException, Depends, Request, Response
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import time

import logging

logger = logging.getLogger(__name__)


class APITier(str, Enum):
    """API service tiers."""
    FREE = "free"
    BASIC = "basic"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class RateLimitType(str, Enum):
    """Rate limiting types."""
    REQUESTS_PER_MINUTE = "requests_per_minute"
    REQUESTS_PER_HOUR = "requests_per_hour"
    REQUESTS_PER_DAY = "requests_per_day"
    TOKENS_PER_MINUTE = "tokens_per_minute"


@dataclass
class APIEndpoint:
    """API endpoint configuration."""
    endpoint_id: str
    model_id: str
    path: str
    method: str = "POST"
    
    # API configuration
    name: str = ""
    description: str = ""
    version: str = "1.0.0"
    
    # Authentication
    requires_auth: bool = True
    api_key_required: bool = True
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])
    
    # Rate limiting
    rate_limits: Dict[APITier, Dict[RateLimitType, int]] = field(default_factory=dict)
    
    # Request/Response configuration
    request_schema: Dict[str, Any] = field(default_factory=dict)
    response_schema: Dict[str, Any] = field(default_factory=dict)
    
    # Monitoring
    enable_logging: bool = True
    enable_metrics: bool = True
    
    # Status
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.now)
    last_request_at: Optional[datetime] = None
    
    # Usage statistics
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_tokens_processed: int = 0


@dataclass
class APIKey:
    """API key configuration."""
    key_id: str
    key_hash: str
    user_id: str
    
    # Key configuration
    name: str = ""
    tier: APITier = APITier.FREE
    
    # Permissions
    allowed_endpoints: List[str] = field(default_factory=list)
    allowed_methods: List[str] = field(default_factory=lambda: ["GET", "POST"])
    
    # Rate limiting
    rate_limits: Dict[RateLimitType, int] = field(default_factory=dict)
    
    # Status
    is_active: bool = True
    expires_at: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    last_used_at: Optional[datetime] = None
    
    # Usage tracking
    total_requests: int = 0
    requests_today: int = 0
    tokens_used_today: int = 0


@dataclass
class RequestLog:
    """API request log entry."""
    request_id: str
    endpoint_id: str
    user_id: Optional[str]
    api_key_id: Optional[str]
    
    # Request details
    method: str
    path: str
    headers: Dict[str, str]
    query_params: Dict[str, str]
    request_size_bytes: int
    
    # Response details
    status_code: int
    response_size_bytes: int
    response_time_ms: float
    
    # Metadata
    timestamp: datetime = field(default_factory=datetime.now)
    ip_address: str = ""
    user_agent: str = ""
    
    # Error information
    error_message: Optional[str] = None
    error_type: Optional[str] = None


class APIGateway:
    """
    Dynamic API Gateway for NeuroFlowAI.
    
    Features:
    - Dynamic API endpoint generation
    - Authentication and authorization
    - Rate limiting and quotas
    - Request/response transformation
    - Comprehensive monitoring and logging
    """
    
    def __init__(self, inference_engine):
        self.inference_engine = inference_engine
        
        # API management
        self.endpoints: Dict[str, APIEndpoint] = {}
        self.api_keys: Dict[str, APIKey] = {}
        self.request_logs: List[RequestLog] = []
        
        # Rate limiting
        self.rate_limit_buckets: Dict[str, Dict[str, List[float]]] = {}
        
        # FastAPI app
        self.app = FastAPI(
            title="NeuroFlowAI API Gateway",
            description="Dynamic API gateway for AI model inference",
            version="1.0.0",
        )
        
        # Security
        self.security = HTTPBearer()
        
        # Metrics
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time_ms": 0.0,
            "requests_per_second": 0.0,
            "active_api_keys": 0,
            "rate_limited_requests": 0,
        }
        
        # Configuration
        self.default_rate_limits = {
            APITier.FREE: {
                RateLimitType.REQUESTS_PER_MINUTE: 10,
                RateLimitType.REQUESTS_PER_HOUR: 100,
                RateLimitType.REQUESTS_PER_DAY: 1000,
            },
            APITier.BASIC: {
                RateLimitType.REQUESTS_PER_MINUTE: 100,
                RateLimitType.REQUESTS_PER_HOUR: 1000,
                RateLimitType.REQUESTS_PER_DAY: 10000,
            },
            APITier.PRO: {
                RateLimitType.REQUESTS_PER_MINUTE: 1000,
                RateLimitType.REQUESTS_PER_HOUR: 10000,
                RateLimitType.REQUESTS_PER_DAY: 100000,
            },
            APITier.ENTERPRISE: {
                RateLimitType.REQUESTS_PER_MINUTE: 10000,
                RateLimitType.REQUESTS_PER_HOUR: 100000,
                RateLimitType.REQUESTS_PER_DAY: 1000000,
            },
        }
        
        self._setup_middleware()
        self._setup_routes()
    
    def _setup_middleware(self) -> None:
        """Setup FastAPI middleware."""
        
        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Compression middleware
        self.app.add_middleware(GZipMiddleware, minimum_size=1000)
        
        # Custom middleware for logging and metrics
        @self.app.middleware("http")
        async def logging_middleware(request: Request, call_next):
            start_time = time.time()
            
            # Process request
            response = await call_next(request)
            
            # Calculate response time
            response_time = (time.time() - start_time) * 1000
            
            # Log request
            await self._log_request(request, response, response_time)
            
            # Update metrics
            self._update_metrics(response.status_code, response_time)
            
            return response
    
    def _setup_routes(self) -> None:
        """Setup FastAPI routes."""
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint."""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0",
            }
        
        @self.app.get("/metrics")
        async def get_metrics():
            """Get API gateway metrics."""
            return self.get_metrics()
        
        @self.app.post("/admin/endpoints")
        async def create_endpoint(endpoint_config: dict):
            """Create new API endpoint."""
            return await self.create_endpoint(endpoint_config)
        
        @self.app.get("/admin/endpoints")
        async def list_endpoints():
            """List all API endpoints."""
            return self.list_endpoints()
        
        @self.app.post("/admin/api-keys")
        async def create_api_key(key_config: dict):
            """Create new API key."""
            return await self.create_api_key(key_config)
    
    async def create_endpoint(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new API endpoint."""
        
        endpoint = APIEndpoint(
            endpoint_id=config["endpoint_id"],
            model_id=config["model_id"],
            path=config["path"],
            method=config.get("method", "POST"),
            name=config.get("name", ""),
            description=config.get("description", ""),
            version=config.get("version", "1.0.0"),
            requires_auth=config.get("requires_auth", True),
            api_key_required=config.get("api_key_required", True),
            request_schema=config.get("request_schema", {}),
            response_schema=config.get("response_schema", {}),
        )
        
        # Set default rate limits
        endpoint.rate_limits = self.default_rate_limits.copy()
        
        # Store endpoint
        self.endpoints[endpoint.endpoint_id] = endpoint
        
        # Register dynamic route
        await self._register_dynamic_route(endpoint)
        
        logger.info(f"Created API endpoint {endpoint.endpoint_id} at {endpoint.path}")
        
        return {
            "endpoint_id": endpoint.endpoint_id,
            "path": endpoint.path,
            "method": endpoint.method,
            "status": "created",
        }
    
    async def _register_dynamic_route(self, endpoint: APIEndpoint) -> None:
        """Register a dynamic route for the endpoint."""
        
        async def endpoint_handler(request: Request):
            """Dynamic endpoint handler."""
            
            # Authenticate request
            api_key = await self._authenticate_request(request, endpoint)
            
            # Check rate limits
            await self._check_rate_limits(api_key, endpoint)
            
            # Parse request
            request_data = await self._parse_request(request)
            
            # Validate request
            await self._validate_request(request_data, endpoint)
            
            # Make inference
            result = await self.inference_engine.predict(
                endpoint_id=endpoint.model_id,  # Use model_id as inference endpoint
                inputs=request_data.get("inputs", {}),
                parameters=request_data.get("parameters", {}),
                user_id=api_key.user_id if api_key else None,
            )
            
            # Transform response
            response_data = await self._transform_response(result, endpoint)
            
            # Update usage statistics
            await self._update_usage_stats(api_key, endpoint, request_data, response_data)
            
            return response_data
        
        # Register route with FastAPI
        if endpoint.method.upper() == "POST":
            self.app.post(endpoint.path)(endpoint_handler)
        elif endpoint.method.upper() == "GET":
            self.app.get(endpoint.path)(endpoint_handler)
        else:
            raise ValueError(f"Unsupported HTTP method: {endpoint.method}")
    
    async def _authenticate_request(self, request: Request, endpoint: APIEndpoint) -> Optional[APIKey]:
        """Authenticate API request."""
        
        if not endpoint.requires_auth:
            return None
        
        # Extract API key from header
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Missing or invalid authorization header")
        
        api_key_value = auth_header.replace("Bearer ", "")
        
        # Find API key
        api_key = None
        for key in self.api_keys.values():
            if key.key_hash == api_key_value and key.is_active:
                api_key = key
                break
        
        if not api_key:
            raise HTTPException(status_code=401, detail="Invalid API key")
        
        # Check expiration
        if api_key.expires_at and datetime.now() > api_key.expires_at:
            raise HTTPException(status_code=401, detail="API key expired")
        
        # Check endpoint permissions
        if (api_key.allowed_endpoints and 
            endpoint.endpoint_id not in api_key.allowed_endpoints):
            raise HTTPException(status_code=403, detail="Access denied to this endpoint")
        
        return api_key
    
    async def _check_rate_limits(self, api_key: Optional[APIKey], endpoint: APIEndpoint) -> None:
        """Check rate limits for the request."""
        
        if not api_key:
            return  # No rate limiting for unauthenticated requests
        
        current_time = time.time()
        key_id = api_key.key_id
        
        # Initialize rate limit buckets if needed
        if key_id not in self.rate_limit_buckets:
            self.rate_limit_buckets[key_id] = {}
        
        # Check each rate limit type
        rate_limits = api_key.rate_limits or self.default_rate_limits.get(api_key.tier, {})
        
        for limit_type, limit_value in rate_limits.items():
            bucket_key = f"{key_id}_{limit_type.value}"
            
            if bucket_key not in self.rate_limit_buckets[key_id]:
                self.rate_limit_buckets[key_id][bucket_key] = []
            
            bucket = self.rate_limit_buckets[key_id][bucket_key]
            
            # Determine time window
            if limit_type == RateLimitType.REQUESTS_PER_MINUTE:
                window_seconds = 60
            elif limit_type == RateLimitType.REQUESTS_PER_HOUR:
                window_seconds = 3600
            elif limit_type == RateLimitType.REQUESTS_PER_DAY:
                window_seconds = 86400
            else:
                continue
            
            # Clean old entries
            cutoff_time = current_time - window_seconds
            bucket[:] = [timestamp for timestamp in bucket if timestamp > cutoff_time]
            
            # Check limit
            if len(bucket) >= limit_value:
                self.metrics["rate_limited_requests"] += 1
                raise HTTPException(
                    status_code=429, 
                    detail=f"Rate limit exceeded: {limit_value} {limit_type.value}"
                )
            
            # Add current request
            bucket.append(current_time)
    
    async def _parse_request(self, request: Request) -> Dict[str, Any]:
        """Parse incoming request."""
        
        if request.method == "POST":
            try:
                return await request.json()
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"Invalid JSON: {e}")
        elif request.method == "GET":
            return dict(request.query_params)
        else:
            raise HTTPException(status_code=405, detail="Method not allowed")
    
    async def _validate_request(self, request_data: Dict[str, Any], endpoint: APIEndpoint) -> None:
        """Validate request against endpoint schema."""
        
        if not endpoint.request_schema:
            return  # No validation if no schema defined
        
        # Basic validation (in production, use jsonschema or pydantic)
        required_fields = endpoint.request_schema.get("required", [])
        
        for field in required_fields:
            if field not in request_data:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Missing required field: {field}"
                )
    
    async def _transform_response(self, result: Dict[str, Any], endpoint: APIEndpoint) -> Dict[str, Any]:
        """Transform inference result according to endpoint configuration."""
        
        # Basic response transformation
        response = {
            "success": result.get("status") == "completed",
            "data": result.get("outputs", {}),
            "metadata": {
                "endpoint_id": endpoint.endpoint_id,
                "model_id": endpoint.model_id,
                "version": endpoint.version,
                "timestamp": datetime.now().isoformat(),
            },
        }
        
        # Add timing information if available
        if "timing" in result:
            response["metadata"]["timing"] = result["timing"]
        
        # Add error information if failed
        if result.get("status") == "failed":
            response["error"] = {
                "message": result.get("error", "Unknown error"),
                "type": "inference_error",
            }
        
        return response
    
    async def _update_usage_stats(
        self, 
        api_key: Optional[APIKey], 
        endpoint: APIEndpoint, 
        request_data: Dict[str, Any],
        response_data: Dict[str, Any]
    ) -> None:
        """Update usage statistics."""
        
        # Update endpoint stats
        endpoint.total_requests += 1
        endpoint.last_request_at = datetime.now()
        
        if response_data.get("success", False):
            endpoint.successful_requests += 1
        else:
            endpoint.failed_requests += 1
        
        # Update API key stats
        if api_key:
            api_key.total_requests += 1
            api_key.last_used_at = datetime.now()
            
            # Update daily counters (simplified)
            api_key.requests_today += 1
            
            # Estimate tokens processed (simplified)
            estimated_tokens = len(str(request_data)) + len(str(response_data))
            api_key.tokens_used_today += estimated_tokens
            endpoint.total_tokens_processed += estimated_tokens
    
    async def _log_request(self, request: Request, response: Response, response_time_ms: float) -> None:
        """Log API request."""
        
        # Extract request information
        request_log = RequestLog(
            request_id=f"req_{int(time.time() * 1000)}",
            endpoint_id="",  # Will be filled if endpoint is identified
            user_id=None,
            api_key_id=None,
            method=request.method,
            path=str(request.url.path),
            headers=dict(request.headers),
            query_params=dict(request.query_params),
            request_size_bytes=0,  # Simplified
            status_code=response.status_code,
            response_size_bytes=0,  # Simplified
            response_time_ms=response_time_ms,
            ip_address=request.client.host if request.client else "",
            user_agent=request.headers.get("user-agent", ""),
        )
        
        # Store log entry
        self.request_logs.append(request_log)
        
        # Keep only last 10000 log entries
        if len(self.request_logs) > 10000:
            self.request_logs = self.request_logs[-10000:]
    
    def _update_metrics(self, status_code: int, response_time_ms: float) -> None:
        """Update API gateway metrics."""
        
        self.metrics["total_requests"] += 1
        
        if 200 <= status_code < 300:
            self.metrics["successful_requests"] += 1
        else:
            self.metrics["failed_requests"] += 1
        
        # Update average response time (exponential moving average)
        current_avg = self.metrics["average_response_time_ms"]
        self.metrics["average_response_time_ms"] = (
            current_avg * 0.9 + response_time_ms * 0.1
        )
    
    async def create_api_key(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new API key."""
        
        import secrets
        import hashlib
        
        # Generate API key
        key_value = f"nf_{secrets.token_urlsafe(32)}"
        key_hash = hashlib.sha256(key_value.encode()).hexdigest()
        
        api_key = APIKey(
            key_id=f"key_{int(time.time())}",
            key_hash=key_hash,
            user_id=config["user_id"],
            name=config.get("name", ""),
            tier=APITier(config.get("tier", APITier.FREE)),
            allowed_endpoints=config.get("allowed_endpoints", []),
            expires_at=datetime.fromisoformat(config["expires_at"]) if config.get("expires_at") else None,
        )
        
        # Set rate limits based on tier
        api_key.rate_limits = self.default_rate_limits.get(api_key.tier, {}).copy()
        
        # Store API key
        self.api_keys[api_key.key_id] = api_key
        
        logger.info(f"Created API key {api_key.key_id} for user {api_key.user_id}")
        
        return {
            "key_id": api_key.key_id,
            "api_key": key_value,  # Only returned once
            "tier": api_key.tier.value,
            "rate_limits": api_key.rate_limits,
            "expires_at": api_key.expires_at.isoformat() if api_key.expires_at else None,
        }
    
    def list_endpoints(self) -> List[Dict[str, Any]]:
        """List all API endpoints."""
        
        return [
            {
                "endpoint_id": endpoint.endpoint_id,
                "path": endpoint.path,
                "method": endpoint.method,
                "name": endpoint.name,
                "description": endpoint.description,
                "version": endpoint.version,
                "is_active": endpoint.is_active,
                "total_requests": endpoint.total_requests,
                "success_rate": (
                    endpoint.successful_requests / max(1, endpoint.total_requests)
                ),
                "last_request_at": endpoint.last_request_at.isoformat() if endpoint.last_request_at else None,
            }
            for endpoint in self.endpoints.values()
        ]
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get API gateway metrics."""
        
        # Calculate requests per second
        current_time = time.time()
        recent_logs = [
            log for log in self.request_logs[-1000:] 
            if (current_time - log.timestamp.timestamp()) < 60
        ]
        
        self.metrics["requests_per_second"] = len(recent_logs) / 60.0
        self.metrics["active_api_keys"] = len([key for key in self.api_keys.values() if key.is_active])
        
        return {
            **self.metrics,
            "active_endpoints": len([ep for ep in self.endpoints.values() if ep.is_active]),
            "total_api_keys": len(self.api_keys),
            "request_logs_count": len(self.request_logs),
        }
    
    def get_usage_analytics(self, days: int = 7) -> Dict[str, Any]:
        """Get usage analytics for the specified period."""
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # Filter recent logs
        recent_logs = [
            log for log in self.request_logs 
            if log.timestamp >= cutoff_date
        ]
        
        # Calculate analytics
        total_requests = len(recent_logs)
        successful_requests = len([log for log in recent_logs if 200 <= log.status_code < 300])
        
        # Group by endpoint
        endpoint_stats = {}
        for log in recent_logs:
            endpoint_id = log.endpoint_id or "unknown"
            if endpoint_id not in endpoint_stats:
                endpoint_stats[endpoint_id] = {"requests": 0, "errors": 0}
            
            endpoint_stats[endpoint_id]["requests"] += 1
            if log.status_code >= 400:
                endpoint_stats[endpoint_id]["errors"] += 1
        
        # Group by day
        daily_stats = {}
        for log in recent_logs:
            day = log.timestamp.date().isoformat()
            if day not in daily_stats:
                daily_stats[day] = {"requests": 0, "errors": 0}
            
            daily_stats[day]["requests"] += 1
            if log.status_code >= 400:
                daily_stats[day]["errors"] += 1
        
        return {
            "period_days": days,
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "error_rate": (total_requests - successful_requests) / max(1, total_requests),
            "average_response_time_ms": (
                sum(log.response_time_ms for log in recent_logs) / max(1, len(recent_logs))
            ),
            "endpoint_stats": endpoint_stats,
            "daily_stats": daily_stats,
        }
