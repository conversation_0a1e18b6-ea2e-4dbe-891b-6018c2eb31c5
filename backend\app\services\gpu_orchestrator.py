"""
GPU Orchestrator Service for NeuroFlowAI
========================================

Advanced GPU resource management and orchestration system.
Handles multi-cloud GPU allocation, spot instances, and cost optimization.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field

import logging

logger = logging.getLogger(__name__)


class GPUProvider(str, Enum):
    """GPU cloud providers."""
    AWS = "aws"
    GCP = "gcp"
    AZURE = "azure"
    LAMBDA_LABS = "lambda_labs"
    VAST_AI = "vast_ai"
    ON_PREMISE = "on_premise"


class GPUInstanceType(str, Enum):
    """GPU instance types across providers."""
    # NVIDIA Tesla/Data Center
    T4 = "nvidia-tesla-t4"
    V100 = "nvidia-tesla-v100"
    A100_40GB = "nvidia-a100-40gb"
    A100_80GB = "nvidia-a100-80gb"
    H100 = "nvidia-h100"
    
    # NVIDIA RTX/Gaming
    RTX3090 = "nvidia-rtx-3090"
    RTX4090 = "nvidia-rtx-4090"
    RTX6000 = "nvidia-rtx-6000"
    
    # AMD
    MI100 = "amd-mi100"
    MI250 = "amd-mi250"


class InstanceStatus(str, Enum):
    """GPU instance status."""
    PENDING = "pending"
    LAUNCHING = "launching"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    TERMINATED = "terminated"
    FAILED = "failed"


@dataclass
class GPUInstance:
    """GPU instance representation."""
    instance_id: str
    provider: GPUProvider
    instance_type: GPUInstanceType
    region: str
    zone: Optional[str] = None
    status: InstanceStatus = InstanceStatus.PENDING
    
    # Hardware specs
    gpu_count: int = 1
    gpu_memory_gb: int = 16
    cpu_cores: int = 8
    ram_gb: int = 32
    storage_gb: int = 100
    
    # Pricing
    cost_per_hour: float = 0.0
    is_spot: bool = False
    spot_price: Optional[float] = None
    
    # Networking
    public_ip: Optional[str] = None
    private_ip: Optional[str] = None
    ssh_key: Optional[str] = None
    
    # Metadata
    created_at: datetime = field(default_factory=datetime.now)
    launched_at: Optional[datetime] = None
    terminated_at: Optional[datetime] = None
    tags: Dict[str, str] = field(default_factory=dict)
    
    # Training job assignment
    assigned_job_id: Optional[str] = None
    utilization: float = 0.0


@dataclass
class GPURequest:
    """GPU resource request."""
    request_id: str
    job_id: str
    
    # Requirements
    gpu_type: GPUInstanceType
    gpu_count: int = 1
    min_gpu_memory_gb: int = 16
    max_cost_per_hour: Optional[float] = None
    
    # Preferences
    preferred_providers: List[GPUProvider] = field(default_factory=list)
    preferred_regions: List[str] = field(default_factory=list)
    allow_spot: bool = True
    max_spot_interruption_rate: float = 0.1
    
    # Timing
    requested_at: datetime = field(default_factory=datetime.now)
    deadline: Optional[datetime] = None
    estimated_duration_hours: float = 1.0
    
    # Status
    status: str = "pending"
    allocated_instances: List[str] = field(default_factory=list)


class GPUOrchestrator:
    """
    Advanced GPU orchestration system for NeuroFlowAI.
    
    Features:
    - Multi-cloud GPU management
    - Intelligent spot instance handling
    - Cost optimization algorithms
    - Auto-scaling and load balancing
    - Fault tolerance and recovery
    """
    
    def __init__(self):
        # Instance tracking
        self.active_instances: Dict[str, GPUInstance] = {}
        self.pending_requests: Dict[str, GPURequest] = {}
        self.instance_history: List[GPUInstance] = []
        
        # Provider configurations
        self.provider_configs = {
            GPUProvider.AWS: {
                "regions": ["us-east-1", "us-west-2", "eu-west-1"],
                "instance_types": {
                    GPUInstanceType.T4: {"instance": "g4dn.xlarge", "cost": 0.526},
                    GPUInstanceType.V100: {"instance": "p3.2xlarge", "cost": 3.06},
                    GPUInstanceType.A100_40GB: {"instance": "p4d.xlarge", "cost": 3.912},
                    GPUInstanceType.A100_80GB: {"instance": "p4de.xlarge", "cost": 5.424},
                },
                "spot_availability": 0.85,
                "spot_discount": 0.7,
            },
            GPUProvider.GCP: {
                "regions": ["us-central1", "us-west1", "europe-west1"],
                "instance_types": {
                    GPUInstanceType.T4: {"instance": "n1-standard-4", "cost": 0.35},
                    GPUInstanceType.V100: {"instance": "n1-standard-8", "cost": 2.48},
                    GPUInstanceType.A100_40GB: {"instance": "a2-highgpu-1g", "cost": 3.673},
                },
                "spot_availability": 0.80,
                "spot_discount": 0.6,
            },
            GPUProvider.LAMBDA_LABS: {
                "regions": ["us-east-1", "us-west-2"],
                "instance_types": {
                    GPUInstanceType.RTX3090: {"instance": "gpu_1x_rtx3090", "cost": 0.50},
                    GPUInstanceType.RTX4090: {"instance": "gpu_1x_rtx4090", "cost": 0.75},
                    GPUInstanceType.A100_80GB: {"instance": "gpu_1x_a100", "cost": 1.10},
                },
                "spot_availability": 0.90,
                "spot_discount": 0.3,
            }
        }
        
        # Performance tracking
        self.metrics = {
            "total_instances_launched": 0,
            "total_instances_terminated": 0,
            "total_gpu_hours": 0.0,
            "total_cost": 0.0,
            "average_utilization": 0.0,
            "spot_interruption_rate": 0.0,
            "cost_savings_from_spot": 0.0,
        }
        
        # Configuration
        self.is_running = False
        self.max_instances_per_provider = 50
        self.cost_optimization_enabled = True
        self.auto_scaling_enabled = True
    
    async def start(self) -> None:
        """Start the GPU orchestrator."""
        self.is_running = True
        logger.info("GPU Orchestrator starting...")
        
        # Start background tasks
        asyncio.create_task(self._instance_monitoring_loop())
        asyncio.create_task(self._cost_optimization_loop())
        asyncio.create_task(self._spot_instance_management_loop())
        asyncio.create_task(self._metrics_collection_loop())
        
        logger.info("GPU Orchestrator started successfully")
    
    async def stop(self) -> None:
        """Stop the GPU orchestrator."""
        self.is_running = False
        
        # Terminate all instances
        await self._terminate_all_instances()
        
        logger.info("GPU Orchestrator stopped")
    
    async def request_gpu(self, request: GPURequest) -> str:
        """Request GPU resources for a training job."""
        
        logger.info(f"Processing GPU request {request.request_id} for job {request.job_id}")
        
        # Store request
        self.pending_requests[request.request_id] = request
        
        try:
            # Find optimal allocation
            allocation_plan = await self._find_optimal_allocation(request)
            
            if not allocation_plan:
                raise Exception("No suitable GPU resources available")
            
            # Launch instances
            instances = await self._launch_instances(allocation_plan, request)
            
            # Update request status
            request.status = "allocated"
            request.allocated_instances = [inst.instance_id for inst in instances]
            
            logger.info(f"Successfully allocated {len(instances)} instances for request {request.request_id}")
            
            return request.request_id
            
        except Exception as e:
            request.status = "failed"
            logger.error(f"Failed to allocate GPU for request {request.request_id}: {e}")
            raise
    
    async def _find_optimal_allocation(self, request: GPURequest) -> Optional[List[Dict[str, Any]]]:
        """Find optimal GPU allocation based on cost, availability, and requirements."""
        
        allocation_options = []
        
        # Check each provider
        for provider in GPUProvider:
            if request.preferred_providers and provider not in request.preferred_providers:
                continue
            
            provider_config = self.provider_configs.get(provider)
            if not provider_config:
                continue
            
            # Check if provider supports requested GPU type
            if request.gpu_type not in provider_config["instance_types"]:
                continue
            
            instance_config = provider_config["instance_types"][request.gpu_type]
            base_cost = instance_config["cost"]
            
            # Calculate costs for on-demand and spot
            on_demand_cost = base_cost * request.gpu_count * request.estimated_duration_hours
            
            spot_cost = None
            if request.allow_spot and provider_config["spot_availability"] > 0.7:
                spot_discount = provider_config["spot_discount"]
                spot_cost = on_demand_cost * (1 - spot_discount)
            
            # Check cost constraints
            if request.max_cost_per_hour:
                if base_cost * request.gpu_count > request.max_cost_per_hour:
                    continue
            
            # Add allocation options
            allocation_options.append({
                "provider": provider,
                "instance_type": request.gpu_type,
                "gpu_count": request.gpu_count,
                "cost_per_hour": base_cost * request.gpu_count,
                "total_cost": on_demand_cost,
                "is_spot": False,
                "availability_score": self._calculate_availability_score(provider, request.gpu_type),
            })
            
            if spot_cost:
                allocation_options.append({
                    "provider": provider,
                    "instance_type": request.gpu_type,
                    "gpu_count": request.gpu_count,
                    "cost_per_hour": base_cost * request.gpu_count * (1 - provider_config["spot_discount"]),
                    "total_cost": spot_cost,
                    "is_spot": True,
                    "availability_score": self._calculate_availability_score(provider, request.gpu_type) * 0.8,
                })
        
        if not allocation_options:
            return None
        
        # Sort by cost-effectiveness score
        allocation_options.sort(key=lambda x: self._calculate_allocation_score(x, request))
        
        return [allocation_options[0]]  # Return best option
    
    def _calculate_availability_score(self, provider: GPUProvider, gpu_type: GPUInstanceType) -> float:
        """Calculate availability score for provider/GPU combination."""
        
        # Base availability from provider config
        base_score = self.provider_configs[provider].get("spot_availability", 0.8)
        
        # Adjust based on current usage
        current_instances = sum(1 for inst in self.active_instances.values() 
                              if inst.provider == provider and inst.instance_type == gpu_type)
        
        max_instances = self.max_instances_per_provider
        usage_factor = 1.0 - (current_instances / max_instances)
        
        return base_score * usage_factor
    
    def _calculate_allocation_score(self, allocation: Dict[str, Any], request: GPURequest) -> float:
        """Calculate allocation score (lower is better)."""
        
        # Cost factor (primary)
        cost_score = allocation["total_cost"]
        
        # Availability factor
        availability_score = 1.0 / max(0.1, allocation["availability_score"])
        
        # Spot instance risk factor
        spot_penalty = 0.2 if allocation["is_spot"] else 0.0
        
        # Provider preference bonus
        provider_bonus = 0.0
        if request.preferred_providers and allocation["provider"] in request.preferred_providers:
            provider_bonus = -0.1 * cost_score
        
        return cost_score + availability_score + spot_penalty + provider_bonus
    
    async def _launch_instances(
        self, 
        allocation_plan: List[Dict[str, Any]], 
        request: GPURequest
    ) -> List[GPUInstance]:
        """Launch GPU instances based on allocation plan."""
        
        launched_instances = []
        
        for allocation in allocation_plan:
            provider = allocation["provider"]
            instance_type = allocation["instance_type"]
            gpu_count = allocation["gpu_count"]
            is_spot = allocation["is_spot"]
            
            # Create instance configuration
            instance_config = self._create_instance_config(
                provider, instance_type, gpu_count, is_spot, request
            )
            
            # Launch instance(s)
            for i in range(gpu_count):
                instance = await self._launch_single_instance(instance_config, request)
                launched_instances.append(instance)
                self.active_instances[instance.instance_id] = instance
        
        return launched_instances
    
    def _create_instance_config(
        self,
        provider: GPUProvider,
        instance_type: GPUInstanceType,
        gpu_count: int,
        is_spot: bool,
        request: GPURequest
    ) -> Dict[str, Any]:
        """Create instance configuration for launching."""
        
        provider_config = self.provider_configs[provider]
        instance_config = provider_config["instance_types"][instance_type]
        
        return {
            "provider": provider,
            "instance_type": instance_type,
            "gpu_count": gpu_count,
            "is_spot": is_spot,
            "cost_per_hour": instance_config["cost"],
            "regions": request.preferred_regions or provider_config["regions"],
            "tags": {
                "job_id": request.job_id,
                "request_id": request.request_id,
                "created_by": "neuroflow-orchestrator",
                "purpose": "ml-training",
            },
        }
    
    async def _launch_single_instance(
        self, 
        config: Dict[str, Any], 
        request: GPURequest
    ) -> GPUInstance:
        """Launch a single GPU instance."""
        
        instance_id = f"{config['provider']}_{config['instance_type']}_{int(datetime.now().timestamp())}"
        
        # Create instance object
        instance = GPUInstance(
            instance_id=instance_id,
            provider=config["provider"],
            instance_type=config["instance_type"],
            region=config["regions"][0],  # Use first available region
            gpu_count=config["gpu_count"],
            cost_per_hour=config["cost_per_hour"],
            is_spot=config["is_spot"],
            tags=config["tags"],
            assigned_job_id=request.job_id,
        )
        
        # Set hardware specs based on instance type
        instance = self._set_hardware_specs(instance)
        
        # Simulate instance launch (in real implementation, call cloud provider APIs)
        await self._simulate_instance_launch(instance)
        
        # Update metrics
        self.metrics["total_instances_launched"] += 1
        
        logger.info(f"Launched instance {instance_id} for job {request.job_id}")
        
        return instance
    
    def _set_hardware_specs(self, instance: GPUInstance) -> GPUInstance:
        """Set hardware specifications based on instance type."""
        
        specs_map = {
            GPUInstanceType.T4: {"gpu_memory_gb": 16, "cpu_cores": 4, "ram_gb": 16},
            GPUInstanceType.V100: {"gpu_memory_gb": 32, "cpu_cores": 8, "ram_gb": 32},
            GPUInstanceType.A100_40GB: {"gpu_memory_gb": 40, "cpu_cores": 16, "ram_gb": 64},
            GPUInstanceType.A100_80GB: {"gpu_memory_gb": 80, "cpu_cores": 32, "ram_gb": 128},
            GPUInstanceType.H100: {"gpu_memory_gb": 80, "cpu_cores": 32, "ram_gb": 128},
            GPUInstanceType.RTX3090: {"gpu_memory_gb": 24, "cpu_cores": 8, "ram_gb": 32},
            GPUInstanceType.RTX4090: {"gpu_memory_gb": 24, "cpu_cores": 12, "ram_gb": 48},
        }
        
        specs = specs_map.get(instance.instance_type, {"gpu_memory_gb": 16, "cpu_cores": 4, "ram_gb": 16})
        
        instance.gpu_memory_gb = specs["gpu_memory_gb"]
        instance.cpu_cores = specs["cpu_cores"]
        instance.ram_gb = specs["ram_gb"]
        
        return instance
    
    async def _simulate_instance_launch(self, instance: GPUInstance) -> None:
        """Simulate instance launch process."""
        
        # Simulate launch delay
        await asyncio.sleep(0.1)
        
        instance.status = InstanceStatus.LAUNCHING
        instance.launched_at = datetime.now()
        
        # Simulate getting IP addresses
        instance.public_ip = f"203.0.113.{hash(instance.instance_id) % 255}"
        instance.private_ip = f"10.0.0.{hash(instance.instance_id) % 255}"
        
        # Simulate startup time
        await asyncio.sleep(0.1)
        
        instance.status = InstanceStatus.RUNNING
    
    async def release_gpu(self, request_id: str) -> bool:
        """Release GPU resources for a completed job."""
        
        if request_id not in self.pending_requests:
            logger.warning(f"Request {request_id} not found")
            return False
        
        request = self.pending_requests[request_id]
        
        # Terminate allocated instances
        for instance_id in request.allocated_instances:
            if instance_id in self.active_instances:
                await self._terminate_instance(instance_id)
        
        # Clean up request
        del self.pending_requests[request_id]
        
        logger.info(f"Released GPU resources for request {request_id}")
        
        return True
    
    async def _terminate_instance(self, instance_id: str) -> None:
        """Terminate a GPU instance."""
        
        if instance_id not in self.active_instances:
            return
        
        instance = self.active_instances[instance_id]
        
        # Update status
        instance.status = InstanceStatus.STOPPING
        
        # Simulate termination
        await asyncio.sleep(0.1)
        
        instance.status = InstanceStatus.TERMINATED
        instance.terminated_at = datetime.now()
        
        # Move to history
        self.instance_history.append(instance)
        del self.active_instances[instance_id]
        
        # Update metrics
        self.metrics["total_instances_terminated"] += 1
        
        if instance.launched_at and instance.terminated_at:
            duration = (instance.terminated_at - instance.launched_at).total_seconds() / 3600
            self.metrics["total_gpu_hours"] += duration
            self.metrics["total_cost"] += duration * instance.cost_per_hour
        
        logger.info(f"Terminated instance {instance_id}")
    
    async def _terminate_all_instances(self) -> None:
        """Terminate all active instances."""
        
        instance_ids = list(self.active_instances.keys())
        
        for instance_id in instance_ids:
            await self._terminate_instance(instance_id)
        
        logger.info(f"Terminated {len(instance_ids)} instances")
    
    async def _instance_monitoring_loop(self) -> None:
        """Monitor instance health and utilization."""
        
        while self.is_running:
            try:
                for instance in self.active_instances.values():
                    # Simulate utilization monitoring
                    if instance.assigned_job_id:
                        instance.utilization = 0.75 + 0.25 * (hash(instance.instance_id) % 100) / 100
                    
                    # Check for spot instance interruptions
                    if instance.is_spot and self._should_simulate_spot_interruption():
                        logger.warning(f"Spot instance {instance.instance_id} interrupted")
                        await self._handle_spot_interruption(instance)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in instance monitoring loop: {e}")
                await asyncio.sleep(30)
    
    def _should_simulate_spot_interruption(self) -> bool:
        """Simulate spot instance interruption (very low probability)."""
        import random
        return random.random() < 0.001  # 0.1% chance per check
    
    async def _handle_spot_interruption(self, instance: GPUInstance) -> None:
        """Handle spot instance interruption."""
        
        # Update metrics
        self.metrics["spot_interruption_rate"] += 1
        
        # Terminate interrupted instance
        await self._terminate_instance(instance.instance_id)
        
        # Try to launch replacement if job is still active
        if instance.assigned_job_id:
            # Find the original request
            for request in self.pending_requests.values():
                if request.job_id == instance.assigned_job_id:
                    logger.info(f"Launching replacement instance for job {instance.assigned_job_id}")
                    # Launch replacement (simplified)
                    break
    
    async def _cost_optimization_loop(self) -> None:
        """Optimize costs by managing spot instances and rightsizing."""
        
        while self.is_running:
            try:
                if self.cost_optimization_enabled:
                    await self._optimize_instance_costs()
                
                await asyncio.sleep(300)  # Optimize every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in cost optimization loop: {e}")
                await asyncio.sleep(300)
    
    async def _optimize_instance_costs(self) -> None:
        """Optimize instance costs through various strategies."""
        
        # Check for underutilized instances
        for instance in list(self.active_instances.values()):
            if instance.utilization < 0.3 and instance.status == InstanceStatus.RUNNING:
                # Consider terminating underutilized instances
                logger.info(f"Instance {instance.instance_id} underutilized ({instance.utilization:.2f})")
        
        # Check for spot instance opportunities
        for instance in list(self.active_instances.values()):
            if not instance.is_spot and instance.status == InstanceStatus.RUNNING:
                # Consider migrating to spot if available
                pass
    
    async def _spot_instance_management_loop(self) -> None:
        """Manage spot instance lifecycle and replacements."""
        
        while self.is_running:
            try:
                # Monitor spot instance health
                spot_instances = [inst for inst in self.active_instances.values() if inst.is_spot]
                
                for instance in spot_instances:
                    # Check spot instance health and pricing
                    pass
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in spot instance management loop: {e}")
                await asyncio.sleep(60)
    
    async def _metrics_collection_loop(self) -> None:
        """Collect and update performance metrics."""
        
        while self.is_running:
            try:
                # Update utilization metrics
                if self.active_instances:
                    total_utilization = sum(inst.utilization for inst in self.active_instances.values())
                    self.metrics["average_utilization"] = total_utilization / len(self.active_instances)
                
                # Calculate cost savings from spot instances
                spot_savings = 0.0
                for instance in self.active_instances.values():
                    if instance.is_spot:
                        provider_config = self.provider_configs[instance.provider]
                        discount = provider_config.get("spot_discount", 0.0)
                        savings = instance.cost_per_hour * discount
                        spot_savings += savings
                
                self.metrics["cost_savings_from_spot"] = spot_savings
                
                await asyncio.sleep(60)  # Update every minute
                
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                await asyncio.sleep(60)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get orchestrator performance metrics."""
        
        return {
            **self.metrics,
            "active_instances": len(self.active_instances),
            "pending_requests": len(self.pending_requests),
            "instance_types_in_use": list(set(inst.instance_type for inst in self.active_instances.values())),
            "providers_in_use": list(set(inst.provider for inst in self.active_instances.values())),
            "total_gpu_memory_gb": sum(inst.gpu_memory_gb * inst.gpu_count for inst in self.active_instances.values()),
        }
    
    def get_instance_status(self, instance_id: str = None) -> Dict[str, Any]:
        """Get status of GPU instances."""
        
        if instance_id:
            if instance_id in self.active_instances:
                instance = self.active_instances[instance_id]
                return {
                    "instance_id": instance.instance_id,
                    "status": instance.status.value,
                    "provider": instance.provider.value,
                    "instance_type": instance.instance_type.value,
                    "utilization": instance.utilization,
                    "cost_per_hour": instance.cost_per_hour,
                    "uptime_hours": (datetime.now() - instance.launched_at).total_seconds() / 3600 if instance.launched_at else 0,
                }
            else:
                raise ValueError(f"Instance {instance_id} not found")
        else:
            return {
                instance_id: {
                    "status": instance.status.value,
                    "provider": instance.provider.value,
                    "instance_type": instance.instance_type.value,
                    "utilization": instance.utilization,
                    "cost_per_hour": instance.cost_per_hour,
                }
                for instance_id, instance in self.active_instances.items()
            }
