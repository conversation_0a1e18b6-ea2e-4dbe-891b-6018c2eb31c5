"""
Inference Engine for NeuroFlowAI
================================

High-performance model serving and inference orchestration.
Supports multiple model formats, auto-scaling, and real-time optimization.
"""

import asyncio
import json
import time
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
import numpy as np

import logging

logger = logging.getLogger(__name__)


class ModelFormat(str, Enum):
    """Supported model formats."""
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"
    ONNX = "onnx"
    HUGGINGFACE = "huggingface"
    SKLEARN = "sklearn"
    XGBOOST = "xgboost"
    LIGHTGBM = "lightgbm"


class InferenceStatus(str, Enum):
    """Inference request status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"


@dataclass
class ModelEndpoint:
    """Model endpoint configuration."""
    endpoint_id: str
    model_id: str
    model_path: str
    model_format: ModelFormat
    
    # Endpoint configuration
    name: str
    description: str = ""
    version: str = "1.0.0"
    
    # Performance settings
    batch_size: int = 1
    max_batch_delay_ms: int = 100
    timeout_seconds: int = 30
    
    # Scaling configuration
    min_replicas: int = 1
    max_replicas: int = 10
    target_utilization: float = 0.7
    
    # Model metadata
    input_schema: Dict[str, Any] = field(default_factory=dict)
    output_schema: Dict[str, Any] = field(default_factory=dict)
    preprocessing_config: Dict[str, Any] = field(default_factory=dict)
    postprocessing_config: Dict[str, Any] = field(default_factory=dict)
    
    # Runtime state
    status: str = "initializing"
    current_replicas: int = 0
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_latency_ms: float = 0.0
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.now)
    last_request_at: Optional[datetime] = None


@dataclass
class InferenceRequest:
    """Inference request representation."""
    request_id: str
    endpoint_id: str
    
    # Request data
    inputs: Dict[str, Any]
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    # Request metadata
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    priority: int = 1
    
    # Timing
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Results
    status: InferenceStatus = InferenceStatus.PENDING
    outputs: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    
    # Performance metrics
    preprocessing_time_ms: float = 0.0
    inference_time_ms: float = 0.0
    postprocessing_time_ms: float = 0.0
    total_time_ms: float = 0.0


class InferenceEngine:
    """
    High-performance inference engine for NeuroFlowAI.
    
    Features:
    - Multi-format model support
    - Dynamic batching and optimization
    - Auto-scaling based on load
    - Real-time performance monitoring
    - Caching and optimization
    """
    
    def __init__(self):
        # Model endpoints
        self.endpoints: Dict[str, ModelEndpoint] = {}
        self.loaded_models: Dict[str, Any] = {}
        
        # Request management
        self.pending_requests: Dict[str, InferenceRequest] = {}
        self.request_history: List[InferenceRequest] = []
        self.request_batches: Dict[str, List[InferenceRequest]] = {}
        
        # Performance tracking
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_latency_ms": 0.0,
            "requests_per_second": 0.0,
            "cache_hit_rate": 0.0,
            "model_utilization": {},
        }
        
        # Caching
        self.response_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl_seconds = 300  # 5 minutes
        
        # Configuration
        self.is_running = False
        self.max_concurrent_requests = 1000
        self.enable_batching = True
        self.enable_caching = True
    
    async def start(self) -> None:
        """Start the inference engine."""
        self.is_running = True
        logger.info("Inference Engine starting...")
        
        # Start background tasks
        asyncio.create_task(self._request_processing_loop())
        asyncio.create_task(self._batch_processing_loop())
        asyncio.create_task(self._auto_scaling_loop())
        asyncio.create_task(self._metrics_collection_loop())
        asyncio.create_task(self._cache_cleanup_loop())
        
        logger.info("Inference Engine started successfully")
    
    async def stop(self) -> None:
        """Stop the inference engine."""
        self.is_running = False
        
        # Unload all models
        for model_id in list(self.loaded_models.keys()):
            await self._unload_model(model_id)
        
        logger.info("Inference Engine stopped")
    
    async def register_endpoint(self, endpoint: ModelEndpoint) -> str:
        """Register a new model endpoint."""
        
        # Validate endpoint configuration
        if not endpoint.model_path:
            raise ValueError("Model path is required")
        
        # Store endpoint
        self.endpoints[endpoint.endpoint_id] = endpoint
        
        # Load model
        await self._load_model(endpoint)
        
        # Initialize scaling
        await self._initialize_endpoint_scaling(endpoint)
        
        logger.info(f"Registered endpoint {endpoint.endpoint_id} for model {endpoint.model_id}")
        
        return endpoint.endpoint_id
    
    async def _load_model(self, endpoint: ModelEndpoint) -> None:
        """Load model into memory."""
        
        try:
            endpoint.status = "loading"
            
            # Simulate model loading based on format
            if endpoint.model_format == ModelFormat.PYTORCH:
                model = await self._load_pytorch_model(endpoint.model_path)
            elif endpoint.model_format == ModelFormat.TENSORFLOW:
                model = await self._load_tensorflow_model(endpoint.model_path)
            elif endpoint.model_format == ModelFormat.ONNX:
                model = await self._load_onnx_model(endpoint.model_path)
            elif endpoint.model_format == ModelFormat.HUGGINGFACE:
                model = await self._load_huggingface_model(endpoint.model_path)
            elif endpoint.model_format == ModelFormat.SKLEARN:
                model = await self._load_sklearn_model(endpoint.model_path)
            else:
                raise ValueError(f"Unsupported model format: {endpoint.model_format}")
            
            # Store loaded model
            self.loaded_models[endpoint.model_id] = model
            
            endpoint.status = "ready"
            endpoint.current_replicas = 1
            
            logger.info(f"Successfully loaded model {endpoint.model_id}")
            
        except Exception as e:
            endpoint.status = "failed"
            logger.error(f"Failed to load model {endpoint.model_id}: {e}")
            raise
    
    async def _load_pytorch_model(self, model_path: str) -> Any:
        """Load PyTorch model."""
        # Simulate PyTorch model loading
        await asyncio.sleep(0.1)
        return {"type": "pytorch", "path": model_path, "loaded_at": datetime.now()}
    
    async def _load_tensorflow_model(self, model_path: str) -> Any:
        """Load TensorFlow model."""
        # Simulate TensorFlow model loading
        await asyncio.sleep(0.1)
        return {"type": "tensorflow", "path": model_path, "loaded_at": datetime.now()}
    
    async def _load_onnx_model(self, model_path: str) -> Any:
        """Load ONNX model."""
        # Simulate ONNX model loading
        await asyncio.sleep(0.1)
        return {"type": "onnx", "path": model_path, "loaded_at": datetime.now()}
    
    async def _load_huggingface_model(self, model_path: str) -> Any:
        """Load HuggingFace model."""
        # Simulate HuggingFace model loading
        await asyncio.sleep(0.1)
        return {"type": "huggingface", "path": model_path, "loaded_at": datetime.now()}
    
    async def _load_sklearn_model(self, model_path: str) -> Any:
        """Load scikit-learn model."""
        # Simulate sklearn model loading
        await asyncio.sleep(0.1)
        return {"type": "sklearn", "path": model_path, "loaded_at": datetime.now()}
    
    async def predict(
        self, 
        endpoint_id: str, 
        inputs: Dict[str, Any],
        parameters: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Make prediction using specified endpoint."""
        
        # Validate endpoint
        if endpoint_id not in self.endpoints:
            raise ValueError(f"Endpoint {endpoint_id} not found")
        
        endpoint = self.endpoints[endpoint_id]
        
        if endpoint.status != "ready":
            raise ValueError(f"Endpoint {endpoint_id} is not ready (status: {endpoint.status})")
        
        # Create inference request
        request = InferenceRequest(
            request_id=f"req_{int(time.time() * 1000)}_{hash(str(inputs)) % 10000}",
            endpoint_id=endpoint_id,
            inputs=inputs,
            parameters=parameters or {},
            user_id=user_id,
        )
        
        # Check cache first
        if self.enable_caching:
            cached_result = await self._check_cache(request)
            if cached_result:
                return cached_result
        
        # Add to pending requests
        self.pending_requests[request.request_id] = request
        
        # Process request
        result = await self._process_inference_request(request)
        
        # Cache result
        if self.enable_caching and request.status == InferenceStatus.COMPLETED:
            await self._cache_result(request, result)
        
        return result
    
    async def _check_cache(self, request: InferenceRequest) -> Optional[Dict[str, Any]]:
        """Check if request result is cached."""
        
        cache_key = self._generate_cache_key(request)
        
        if cache_key in self.response_cache:
            cached_entry = self.response_cache[cache_key]
            
            # Check if cache entry is still valid
            if datetime.now() - cached_entry["timestamp"] < timedelta(seconds=self.cache_ttl_seconds):
                self.metrics["cache_hit_rate"] = (
                    self.metrics["cache_hit_rate"] * 0.9 + 0.1
                )  # Exponential moving average
                
                logger.debug(f"Cache hit for request {request.request_id}")
                return cached_entry["result"]
        
        return None
    
    def _generate_cache_key(self, request: InferenceRequest) -> str:
        """Generate cache key for request."""
        
        # Create deterministic key from inputs and parameters
        key_data = {
            "endpoint_id": request.endpoint_id,
            "inputs": request.inputs,
            "parameters": request.parameters,
        }
        
        return f"cache_{hash(json.dumps(key_data, sort_keys=True))}"
    
    async def _cache_result(self, request: InferenceRequest, result: Dict[str, Any]) -> None:
        """Cache inference result."""
        
        cache_key = self._generate_cache_key(request)
        
        self.response_cache[cache_key] = {
            "result": result,
            "timestamp": datetime.now(),
            "request_id": request.request_id,
        }
        
        logger.debug(f"Cached result for request {request.request_id}")
    
    async def _process_inference_request(self, request: InferenceRequest) -> Dict[str, Any]:
        """Process a single inference request."""
        
        start_time = time.time()
        request.started_at = datetime.now()
        request.status = InferenceStatus.PROCESSING
        
        try:
            endpoint = self.endpoints[request.endpoint_id]
            model = self.loaded_models[endpoint.model_id]
            
            # Preprocessing
            preprocessing_start = time.time()
            processed_inputs = await self._preprocess_inputs(request.inputs, endpoint)
            request.preprocessing_time_ms = (time.time() - preprocessing_start) * 1000
            
            # Inference
            inference_start = time.time()
            raw_outputs = await self._run_inference(processed_inputs, model, endpoint)
            request.inference_time_ms = (time.time() - inference_start) * 1000
            
            # Postprocessing
            postprocessing_start = time.time()
            final_outputs = await self._postprocess_outputs(raw_outputs, endpoint)
            request.postprocessing_time_ms = (time.time() - postprocessing_start) * 1000
            
            # Complete request
            request.status = InferenceStatus.COMPLETED
            request.outputs = final_outputs
            request.completed_at = datetime.now()
            request.total_time_ms = (time.time() - start_time) * 1000
            
            # Update endpoint metrics
            endpoint.total_requests += 1
            endpoint.successful_requests += 1
            endpoint.last_request_at = datetime.now()
            
            # Update average latency
            endpoint.average_latency_ms = (
                endpoint.average_latency_ms * 0.9 + 
                request.total_time_ms * 0.1
            )
            
            # Update global metrics
            self.metrics["total_requests"] += 1
            self.metrics["successful_requests"] += 1
            
            logger.debug(f"Completed inference request {request.request_id} in {request.total_time_ms:.2f}ms")
            
            return {
                "request_id": request.request_id,
                "status": "completed",
                "outputs": final_outputs,
                "timing": {
                    "preprocessing_ms": request.preprocessing_time_ms,
                    "inference_ms": request.inference_time_ms,
                    "postprocessing_ms": request.postprocessing_time_ms,
                    "total_ms": request.total_time_ms,
                },
            }
            
        except Exception as e:
            request.status = InferenceStatus.FAILED
            request.error = str(e)
            request.completed_at = datetime.now()
            request.total_time_ms = (time.time() - start_time) * 1000
            
            # Update metrics
            endpoint.total_requests += 1
            endpoint.failed_requests += 1
            self.metrics["total_requests"] += 1
            self.metrics["failed_requests"] += 1
            
            logger.error(f"Inference request {request.request_id} failed: {e}")
            
            return {
                "request_id": request.request_id,
                "status": "failed",
                "error": str(e),
                "timing": {
                    "total_ms": request.total_time_ms,
                },
            }
        
        finally:
            # Move to history and clean up
            self.request_history.append(request)
            if request.request_id in self.pending_requests:
                del self.pending_requests[request.request_id]
            
            # Keep only last 1000 requests in history
            if len(self.request_history) > 1000:
                self.request_history = self.request_history[-1000:]
    
    async def _preprocess_inputs(self, inputs: Dict[str, Any], endpoint: ModelEndpoint) -> Dict[str, Any]:
        """Preprocess inputs according to endpoint configuration."""
        
        # Apply preprocessing configuration
        preprocessing_config = endpoint.preprocessing_config
        
        processed_inputs = inputs.copy()
        
        # Example preprocessing steps
        if preprocessing_config.get("normalize", False):
            # Simulate normalization
            for key, value in processed_inputs.items():
                if isinstance(value, (int, float)):
                    processed_inputs[key] = (value - 0.5) * 2  # Simple normalization
        
        if preprocessing_config.get("tokenize", False):
            # Simulate tokenization for text inputs
            for key, value in processed_inputs.items():
                if isinstance(value, str):
                    processed_inputs[key] = value.split()  # Simple tokenization
        
        return processed_inputs
    
    async def _run_inference(
        self, 
        inputs: Dict[str, Any], 
        model: Any, 
        endpoint: ModelEndpoint
    ) -> Dict[str, Any]:
        """Run model inference."""
        
        # Simulate inference based on model type
        model_type = model.get("type", "unknown")
        
        if model_type == "pytorch":
            return await self._run_pytorch_inference(inputs, model, endpoint)
        elif model_type == "tensorflow":
            return await self._run_tensorflow_inference(inputs, model, endpoint)
        elif model_type == "onnx":
            return await self._run_onnx_inference(inputs, model, endpoint)
        elif model_type == "huggingface":
            return await self._run_huggingface_inference(inputs, model, endpoint)
        elif model_type == "sklearn":
            return await self._run_sklearn_inference(inputs, model, endpoint)
        else:
            raise ValueError(f"Unsupported model type: {model_type}")
    
    async def _run_pytorch_inference(
        self, 
        inputs: Dict[str, Any], 
        model: Any, 
        endpoint: ModelEndpoint
    ) -> Dict[str, Any]:
        """Run PyTorch model inference."""
        
        # Simulate PyTorch inference
        await asyncio.sleep(0.05)  # Simulate computation time
        
        # Generate mock outputs
        outputs = {
            "predictions": [0.8, 0.2],  # Mock classification probabilities
            "confidence": 0.85,
            "model_version": endpoint.version,
        }
        
        return outputs
    
    async def _run_tensorflow_inference(
        self, 
        inputs: Dict[str, Any], 
        model: Any, 
        endpoint: ModelEndpoint
    ) -> Dict[str, Any]:
        """Run TensorFlow model inference."""
        
        # Simulate TensorFlow inference
        await asyncio.sleep(0.05)
        
        outputs = {
            "predictions": [0.7, 0.3],
            "confidence": 0.82,
            "model_version": endpoint.version,
        }
        
        return outputs
    
    async def _run_onnx_inference(
        self, 
        inputs: Dict[str, Any], 
        model: Any, 
        endpoint: ModelEndpoint
    ) -> Dict[str, Any]:
        """Run ONNX model inference."""
        
        # Simulate ONNX inference
        await asyncio.sleep(0.03)  # ONNX is typically faster
        
        outputs = {
            "predictions": [0.75, 0.25],
            "confidence": 0.88,
            "model_version": endpoint.version,
        }
        
        return outputs
    
    async def _run_huggingface_inference(
        self, 
        inputs: Dict[str, Any], 
        model: Any, 
        endpoint: ModelEndpoint
    ) -> Dict[str, Any]:
        """Run HuggingFace model inference."""
        
        # Simulate HuggingFace inference
        await asyncio.sleep(0.08)  # Transformers can be slower
        
        outputs = {
            "predictions": "Generated text response",
            "confidence": 0.90,
            "tokens_generated": 25,
            "model_version": endpoint.version,
        }
        
        return outputs
    
    async def _run_sklearn_inference(
        self, 
        inputs: Dict[str, Any], 
        model: Any, 
        endpoint: ModelEndpoint
    ) -> Dict[str, Any]:
        """Run scikit-learn model inference."""
        
        # Simulate sklearn inference
        await asyncio.sleep(0.01)  # sklearn is typically very fast
        
        outputs = {
            "predictions": [1],  # Mock classification result
            "confidence": 0.92,
            "model_version": endpoint.version,
        }
        
        return outputs
    
    async def _postprocess_outputs(self, outputs: Dict[str, Any], endpoint: ModelEndpoint) -> Dict[str, Any]:
        """Postprocess outputs according to endpoint configuration."""
        
        postprocessing_config = endpoint.postprocessing_config
        
        processed_outputs = outputs.copy()
        
        # Apply postprocessing steps
        if postprocessing_config.get("round_predictions", False):
            if "predictions" in processed_outputs and isinstance(processed_outputs["predictions"], list):
                processed_outputs["predictions"] = [
                    round(p, 4) if isinstance(p, float) else p 
                    for p in processed_outputs["predictions"]
                ]
        
        if postprocessing_config.get("add_metadata", True):
            processed_outputs["metadata"] = {
                "endpoint_id": endpoint.endpoint_id,
                "model_id": endpoint.model_id,
                "timestamp": datetime.now().isoformat(),
                "processing_time_ms": 0,  # Will be filled by caller
            }
        
        return processed_outputs
    
    async def _request_processing_loop(self) -> None:
        """Main request processing loop."""
        
        while self.is_running:
            try:
                # Process pending requests
                if len(self.pending_requests) > self.max_concurrent_requests:
                    logger.warning(f"Request queue full ({len(self.pending_requests)} requests)")
                
                await asyncio.sleep(0.01)  # Brief pause
                
            except Exception as e:
                logger.error(f"Error in request processing loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _batch_processing_loop(self) -> None:
        """Process requests in batches for efficiency."""
        
        while self.is_running:
            try:
                if self.enable_batching:
                    # Group requests by endpoint for batch processing
                    for endpoint_id, endpoint in self.endpoints.items():
                        if endpoint.batch_size > 1:
                            await self._process_batched_requests(endpoint_id)
                
                await asyncio.sleep(0.1)  # Check every 100ms
                
            except Exception as e:
                logger.error(f"Error in batch processing loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _process_batched_requests(self, endpoint_id: str) -> None:
        """Process requests in batches for an endpoint."""
        
        # Find requests for this endpoint
        endpoint_requests = [
            req for req in self.pending_requests.values() 
            if req.endpoint_id == endpoint_id and req.status == InferenceStatus.PENDING
        ]
        
        endpoint = self.endpoints[endpoint_id]
        
        if len(endpoint_requests) >= endpoint.batch_size:
            # Process batch
            batch = endpoint_requests[:endpoint.batch_size]
            logger.debug(f"Processing batch of {len(batch)} requests for endpoint {endpoint_id}")
            
            # Process batch (simplified - in reality would batch the actual inference)
            for request in batch:
                await self._process_inference_request(request)
    
    async def _auto_scaling_loop(self) -> None:
        """Auto-scale endpoints based on load."""
        
        while self.is_running:
            try:
                for endpoint in self.endpoints.values():
                    await self._check_endpoint_scaling(endpoint)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in auto-scaling loop: {e}")
                await asyncio.sleep(30)
    
    async def _check_endpoint_scaling(self, endpoint: ModelEndpoint) -> None:
        """Check if endpoint needs scaling."""
        
        # Calculate current utilization
        current_requests = len([
            req for req in self.pending_requests.values() 
            if req.endpoint_id == endpoint.endpoint_id
        ])
        
        utilization = current_requests / max(1, endpoint.current_replicas)
        
        # Scale up if utilization is high
        if utilization > endpoint.target_utilization and endpoint.current_replicas < endpoint.max_replicas:
            endpoint.current_replicas += 1
            logger.info(f"Scaled up endpoint {endpoint.endpoint_id} to {endpoint.current_replicas} replicas")
        
        # Scale down if utilization is low
        elif utilization < endpoint.target_utilization * 0.5 and endpoint.current_replicas > endpoint.min_replicas:
            endpoint.current_replicas -= 1
            logger.info(f"Scaled down endpoint {endpoint.endpoint_id} to {endpoint.current_replicas} replicas")
    
    async def _initialize_endpoint_scaling(self, endpoint: ModelEndpoint) -> None:
        """Initialize scaling for an endpoint."""
        endpoint.current_replicas = endpoint.min_replicas
    
    async def _metrics_collection_loop(self) -> None:
        """Collect and update performance metrics."""
        
        while self.is_running:
            try:
                # Update requests per second
                current_time = time.time()
                recent_requests = [
                    req for req in self.request_history[-100:] 
                    if req.completed_at and (current_time - req.completed_at.timestamp()) < 60
                ]
                
                self.metrics["requests_per_second"] = len(recent_requests) / 60.0
                
                # Update average latency
                if recent_requests:
                    avg_latency = sum(req.total_time_ms for req in recent_requests) / len(recent_requests)
                    self.metrics["average_latency_ms"] = avg_latency
                
                # Update model utilization
                for endpoint_id, endpoint in self.endpoints.items():
                    active_requests = len([
                        req for req in self.pending_requests.values() 
                        if req.endpoint_id == endpoint_id
                    ])
                    
                    self.metrics["model_utilization"][endpoint_id] = {
                        "active_requests": active_requests,
                        "current_replicas": endpoint.current_replicas,
                        "utilization": active_requests / max(1, endpoint.current_replicas),
                    }
                
                await asyncio.sleep(10)  # Update every 10 seconds
                
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                await asyncio.sleep(10)
    
    async def _cache_cleanup_loop(self) -> None:
        """Clean up expired cache entries."""
        
        while self.is_running:
            try:
                current_time = datetime.now()
                expired_keys = []
                
                for cache_key, cache_entry in self.response_cache.items():
                    if current_time - cache_entry["timestamp"] > timedelta(seconds=self.cache_ttl_seconds):
                        expired_keys.append(cache_key)
                
                for key in expired_keys:
                    del self.response_cache[key]
                
                if expired_keys:
                    logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
                
                await asyncio.sleep(60)  # Clean up every minute
                
            except Exception as e:
                logger.error(f"Error in cache cleanup loop: {e}")
                await asyncio.sleep(60)
    
    async def _unload_model(self, model_id: str) -> None:
        """Unload model from memory."""
        
        if model_id in self.loaded_models:
            del self.loaded_models[model_id]
            logger.info(f"Unloaded model {model_id}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get inference engine metrics."""
        
        return {
            **self.metrics,
            "active_endpoints": len(self.endpoints),
            "loaded_models": len(self.loaded_models),
            "pending_requests": len(self.pending_requests),
            "cache_size": len(self.response_cache),
            "total_replicas": sum(ep.current_replicas for ep in self.endpoints.values()),
        }
    
    def get_endpoint_status(self, endpoint_id: str = None) -> Dict[str, Any]:
        """Get status of endpoints."""
        
        if endpoint_id:
            if endpoint_id not in self.endpoints:
                raise ValueError(f"Endpoint {endpoint_id} not found")
            
            endpoint = self.endpoints[endpoint_id]
            return {
                "endpoint_id": endpoint.endpoint_id,
                "status": endpoint.status,
                "current_replicas": endpoint.current_replicas,
                "total_requests": endpoint.total_requests,
                "successful_requests": endpoint.successful_requests,
                "failed_requests": endpoint.failed_requests,
                "average_latency_ms": endpoint.average_latency_ms,
                "last_request_at": endpoint.last_request_at.isoformat() if endpoint.last_request_at else None,
            }
        else:
            return {
                endpoint_id: {
                    "status": endpoint.status,
                    "current_replicas": endpoint.current_replicas,
                    "total_requests": endpoint.total_requests,
                    "average_latency_ms": endpoint.average_latency_ms,
                }
                for endpoint_id, endpoint in self.endpoints.items()
            }
