"""
Kubernetes GPU Orchestrator for NeuroFlowAI
===========================================

Production-ready GPU orchestration using Kubernetes with Ray integration.
Handles multi-cloud deployments, auto-scaling, and fault tolerance.
"""

import asyncio
import json
import yaml
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field

from kubernetes import client, config, watch
from kubernetes.client.rest import ApiException
import ray
from ray import serve

import logging

logger = logging.getLogger(__name__)


class NodeType(str, Enum):
    """Kubernetes node types."""
    CPU_ONLY = "cpu-only"
    GPU_T4 = "gpu-t4"
    GPU_V100 = "gpu-v100"
    GPU_A100 = "gpu-a100"
    GPU_H100 = "gpu-h100"


class WorkloadType(str, Enum):
    """Training workload types."""
    SINGLE_GPU = "single-gpu"
    MULTI_GPU = "multi-gpu"
    DISTRIBUTED = "distributed"
    INFERENCE = "inference"
    BATCH_PROCESSING = "batch-processing"


@dataclass
class GPUCluster:
    """GPU cluster configuration."""
    cluster_id: str
    name: str
    provider: str  # aws, gcp, azure
    region: str
    
    # Node configuration
    node_pools: Dict[NodeType, Dict[str, Any]] = field(default_factory=dict)
    
    # Cluster state
    status: str = "initializing"
    total_nodes: int = 0
    available_gpus: int = 0
    allocated_gpus: int = 0
    
    # Ray cluster integration
    ray_cluster_address: Optional[str] = None
    ray_dashboard_url: Optional[str] = None
    
    # Monitoring
    created_at: datetime = field(default_factory=datetime.now)
    last_health_check: Optional[datetime] = None


@dataclass
class TrainingJob:
    """Training job specification."""
    job_id: str
    name: str
    user_id: str
    
    # Resource requirements
    workload_type: WorkloadType
    gpu_count: int
    gpu_type: NodeType
    cpu_cores: int
    memory_gb: int
    storage_gb: int
    
    # Training configuration
    framework: str  # pytorch, tensorflow, etc.
    model_config: Dict[str, Any]
    training_script: str
    environment_vars: Dict[str, str] = field(default_factory=dict)
    
    # Scheduling
    priority: int = 1
    max_runtime_hours: int = 24
    preemptible: bool = False
    
    # Status
    status: str = "pending"
    cluster_id: Optional[str] = None
    ray_job_id: Optional[str] = None
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class K8sGPUOrchestrator:
    """
    Production-ready Kubernetes GPU orchestrator.
    
    Features:
    - Multi-cloud Kubernetes cluster management
    - Ray integration for distributed training
    - Auto-scaling based on workload demand
    - Fault tolerance and job recovery
    - Cost optimization with spot instances
    """
    
    def __init__(self, kubeconfig_path: Optional[str] = None):
        # Initialize Kubernetes client
        try:
            if kubeconfig_path:
                config.load_kube_config(config_file=kubeconfig_path)
            else:
                config.load_incluster_config()  # For in-cluster deployment
        except Exception:
            config.load_kube_config()  # Fallback to default kubeconfig
        
        self.k8s_client = client.ApiClient()
        self.apps_v1 = client.AppsV1Api()
        self.core_v1 = client.CoreV1Api()
        self.batch_v1 = client.BatchV1Api()
        self.custom_objects_api = client.CustomObjectsApi()
        
        # Cluster management
        self.clusters: Dict[str, GPUCluster] = {}
        self.training_jobs: Dict[str, TrainingJob] = {}
        self.job_queue: List[str] = []
        
        # Ray integration
        self.ray_clusters: Dict[str, Any] = {}
        
        # Configuration
        self.namespace = "neuroflow-gpu"
        self.is_running = False
        
        # Cluster templates
        self.cluster_templates = {
            "aws-gpu-cluster": self._get_aws_cluster_template(),
            "gcp-gpu-cluster": self._get_gcp_cluster_template(),
            "azure-gpu-cluster": self._get_azure_cluster_template(),
        }
    
    async def start(self) -> None:
        """Start the GPU orchestrator."""
        self.is_running = True
        logger.info("K8s GPU Orchestrator starting...")
        
        # Ensure namespace exists
        await self._ensure_namespace()
        
        # Start background tasks
        asyncio.create_task(self._cluster_monitoring_loop())
        asyncio.create_task(self._job_scheduling_loop())
        asyncio.create_task(self._auto_scaling_loop())
        asyncio.create_task(self._health_check_loop())
        
        logger.info("K8s GPU Orchestrator started successfully")
    
    async def stop(self) -> None:
        """Stop the GPU orchestrator."""
        self.is_running = False
        
        # Clean up running jobs
        for job_id in list(self.training_jobs.keys()):
            await self.cancel_training_job(job_id)
        
        logger.info("K8s GPU Orchestrator stopped")
    
    async def create_gpu_cluster(
        self, 
        cluster_config: Dict[str, Any]
    ) -> str:
        """Create a new GPU cluster."""
        
        cluster_id = f"gpu-cluster-{int(datetime.now().timestamp())}"
        
        cluster = GPUCluster(
            cluster_id=cluster_id,
            name=cluster_config["name"],
            provider=cluster_config["provider"],
            region=cluster_config["region"],
            node_pools=cluster_config.get("node_pools", {}),
        )
        
        try:
            # Deploy cluster infrastructure
            await self._deploy_cluster_infrastructure(cluster, cluster_config)
            
            # Initialize Ray cluster
            await self._initialize_ray_cluster(cluster)
            
            # Store cluster
            self.clusters[cluster_id] = cluster
            cluster.status = "ready"
            
            logger.info(f"Created GPU cluster {cluster_id}")
            return cluster_id
            
        except Exception as e:
            cluster.status = "failed"
            logger.error(f"Failed to create cluster {cluster_id}: {e}")
            raise
    
    async def _deploy_cluster_infrastructure(
        self, 
        cluster: GPUCluster, 
        config: Dict[str, Any]
    ) -> None:
        """Deploy Kubernetes infrastructure for the cluster."""
        
        # Create node pools for different GPU types
        for node_type, pool_config in cluster.node_pools.items():
            await self._create_node_pool(cluster, node_type, pool_config)
        
        # Deploy cluster autoscaler
        await self._deploy_cluster_autoscaler(cluster)
        
        # Deploy GPU device plugin
        await self._deploy_gpu_device_plugin(cluster)
        
        # Deploy monitoring stack
        await self._deploy_monitoring_stack(cluster)
    
    async def _create_node_pool(
        self, 
        cluster: GPUCluster, 
        node_type: NodeType, 
        pool_config: Dict[str, Any]
    ) -> None:
        """Create a node pool for specific GPU type."""
        
        node_pool_spec = {
            "apiVersion": "v1",
            "kind": "NodePool",
            "metadata": {
                "name": f"{cluster.cluster_id}-{node_type.value}",
                "namespace": self.namespace,
                "labels": {
                    "cluster": cluster.cluster_id,
                    "node-type": node_type.value,
                    "gpu-type": node_type.value.replace("gpu-", ""),
                }
            },
            "spec": {
                "replicas": pool_config.get("min_nodes", 0),
                "template": {
                    "metadata": {
                        "labels": {
                            "node-type": node_type.value,
                            "gpu-type": node_type.value.replace("gpu-", ""),
                        }
                    },
                    "spec": {
                        "containers": [{
                            "name": "gpu-node",
                            "image": self._get_node_image(node_type),
                            "resources": self._get_node_resources(node_type),
                            "env": [
                                {"name": "CLUSTER_ID", "value": cluster.cluster_id},
                                {"name": "NODE_TYPE", "value": node_type.value},
                            ]
                        }],
                        "nodeSelector": {
                            "accelerator": node_type.value.replace("gpu-", "nvidia-tesla-"),
                        } if node_type != NodeType.CPU_ONLY else {},
                        "tolerations": [
                            {
                                "key": "nvidia.com/gpu",
                                "operator": "Exists",
                                "effect": "NoSchedule"
                            }
                        ] if node_type != NodeType.CPU_ONLY else []
                    }
                },
                "autoscaling": {
                    "enabled": True,
                    "minReplicas": pool_config.get("min_nodes", 0),
                    "maxReplicas": pool_config.get("max_nodes", 10),
                    "targetCPUUtilizationPercentage": 70,
                    "targetGPUUtilizationPercentage": 80,
                }
            }
        }
        
        # Apply node pool configuration
        try:
            self.custom_objects_api.create_namespaced_custom_object(
                group="nodepool.neuroflow.ai",
                version="v1",
                namespace=self.namespace,
                plural="nodepools",
                body=node_pool_spec
            )
            logger.info(f"Created node pool {node_type.value} for cluster {cluster.cluster_id}")
        except ApiException as e:
            logger.error(f"Failed to create node pool: {e}")
            raise
    
    def _get_node_image(self, node_type: NodeType) -> str:
        """Get container image for node type."""
        base_image = "neuroflow/gpu-node"
        
        if node_type == NodeType.CPU_ONLY:
            return f"{base_image}:cpu-latest"
        else:
            gpu_type = node_type.value.replace("gpu-", "")
            return f"{base_image}:{gpu_type}-latest"
    
    def _get_node_resources(self, node_type: NodeType) -> Dict[str, Any]:
        """Get resource requirements for node type."""
        
        resources = {
            NodeType.CPU_ONLY: {
                "requests": {"cpu": "2", "memory": "8Gi"},
                "limits": {"cpu": "4", "memory": "16Gi"}
            },
            NodeType.GPU_T4: {
                "requests": {"cpu": "4", "memory": "16Gi", "nvidia.com/gpu": "1"},
                "limits": {"cpu": "8", "memory": "32Gi", "nvidia.com/gpu": "1"}
            },
            NodeType.GPU_V100: {
                "requests": {"cpu": "8", "memory": "32Gi", "nvidia.com/gpu": "1"},
                "limits": {"cpu": "16", "memory": "64Gi", "nvidia.com/gpu": "1"}
            },
            NodeType.GPU_A100: {
                "requests": {"cpu": "16", "memory": "64Gi", "nvidia.com/gpu": "1"},
                "limits": {"cpu": "32", "memory": "128Gi", "nvidia.com/gpu": "1"}
            },
            NodeType.GPU_H100: {
                "requests": {"cpu": "32", "memory": "128Gi", "nvidia.com/gpu": "1"},
                "limits": {"cpu": "64", "memory": "256Gi", "nvidia.com/gpu": "1"}
            }
        }
        
        return resources.get(node_type, resources[NodeType.CPU_ONLY])
    
    async def _initialize_ray_cluster(self, cluster: GPUCluster) -> None:
        """Initialize Ray cluster for distributed training."""
        
        ray_cluster_spec = {
            "apiVersion": "ray.io/v1alpha1",
            "kind": "RayCluster",
            "metadata": {
                "name": f"ray-{cluster.cluster_id}",
                "namespace": self.namespace,
            },
            "spec": {
                "rayVersion": "2.8.0",
                "headGroupSpec": {
                    "replicas": 1,
                    "rayStartParams": {
                        "dashboard-host": "0.0.0.0",
                        "metrics-export-port": "8080",
                        "num-cpus": "0",
                    },
                    "template": {
                        "spec": {
                            "containers": [{
                                "name": "ray-head",
                                "image": "rayproject/ray:2.8.0-gpu",
                                "ports": [
                                    {"containerPort": 6379, "name": "gcs"},
                                    {"containerPort": 8265, "name": "dashboard"},
                                    {"containerPort": 10001, "name": "client"},
                                ],
                                "resources": {
                                    "requests": {"cpu": "2", "memory": "8Gi"},
                                    "limits": {"cpu": "4", "memory": "16Gi"}
                                },
                                "env": [
                                    {"name": "RAY_CLUSTER_ID", "value": cluster.cluster_id}
                                ]
                            }]
                        }
                    }
                },
                "workerGroupSpecs": [
                    {
                        "groupName": "gpu-workers",
                        "replicas": 2,
                        "minReplicas": 0,
                        "maxReplicas": 10,
                        "rayStartParams": {
                            "num-cpus": "8",
                            "num-gpus": "1",
                        },
                        "template": {
                            "spec": {
                                "containers": [{
                                    "name": "ray-worker",
                                    "image": "rayproject/ray:2.8.0-gpu",
                                    "resources": {
                                        "requests": {
                                            "cpu": "8",
                                            "memory": "32Gi",
                                            "nvidia.com/gpu": "1"
                                        },
                                        "limits": {
                                            "cpu": "16",
                                            "memory": "64Gi",
                                            "nvidia.com/gpu": "1"
                                        }
                                    }
                                }],
                                "nodeSelector": {
                                    "accelerator": "nvidia-tesla-v100"  # Default to V100
                                },
                                "tolerations": [
                                    {
                                        "key": "nvidia.com/gpu",
                                        "operator": "Exists",
                                        "effect": "NoSchedule"
                                    }
                                ]
                            }
                        }
                    }
                ]
            }
        }
        
        try:
            # Create Ray cluster
            self.custom_objects_api.create_namespaced_custom_object(
                group="ray.io",
                version="v1alpha1",
                namespace=self.namespace,
                plural="rayclusters",
                body=ray_cluster_spec
            )
            
            # Wait for Ray cluster to be ready
            await self._wait_for_ray_cluster_ready(cluster)
            
            logger.info(f"Initialized Ray cluster for {cluster.cluster_id}")
            
        except ApiException as e:
            logger.error(f"Failed to create Ray cluster: {e}")
            raise
    
    async def _wait_for_ray_cluster_ready(self, cluster: GPUCluster, timeout: int = 600) -> None:
        """Wait for Ray cluster to be ready."""
        
        start_time = datetime.now()
        
        while (datetime.now() - start_time).total_seconds() < timeout:
            try:
                # Check Ray cluster status
                ray_cluster = self.custom_objects_api.get_namespaced_custom_object(
                    group="ray.io",
                    version="v1alpha1",
                    namespace=self.namespace,
                    plural="rayclusters",
                    name=f"ray-{cluster.cluster_id}"
                )
                
                status = ray_cluster.get("status", {})
                if status.get("state") == "ready":
                    # Get Ray cluster connection info
                    cluster.ray_cluster_address = f"ray://{status.get('head_service_ip')}:10001"
                    cluster.ray_dashboard_url = f"http://{status.get('head_service_ip')}:8265"
                    return
                
            except ApiException:
                pass
            
            await asyncio.sleep(10)
        
        raise TimeoutError(f"Ray cluster for {cluster.cluster_id} did not become ready within {timeout} seconds")
    
    async def submit_training_job(self, job_config: Dict[str, Any]) -> str:
        """Submit a training job to the GPU cluster."""
        
        job = TrainingJob(
            job_id=f"job-{int(datetime.now().timestamp())}",
            name=job_config["name"],
            user_id=job_config["user_id"],
            workload_type=WorkloadType(job_config["workload_type"]),
            gpu_count=job_config["gpu_count"],
            gpu_type=NodeType(job_config["gpu_type"]),
            cpu_cores=job_config.get("cpu_cores", 8),
            memory_gb=job_config.get("memory_gb", 32),
            storage_gb=job_config.get("storage_gb", 100),
            framework=job_config["framework"],
            model_config=job_config["model_config"],
            training_script=job_config["training_script"],
            environment_vars=job_config.get("environment_vars", {}),
            priority=job_config.get("priority", 1),
            max_runtime_hours=job_config.get("max_runtime_hours", 24),
            preemptible=job_config.get("preemptible", False),
        )
        
        # Store job
        self.training_jobs[job.job_id] = job
        
        # Add to scheduling queue
        self.job_queue.append(job.job_id)
        self.job_queue.sort(key=lambda jid: self.training_jobs[jid].priority, reverse=True)
        
        logger.info(f"Submitted training job {job.job_id}")
        return job.job_id

    async def _job_scheduling_loop(self) -> None:
        """Main job scheduling loop."""

        while self.is_running:
            try:
                if self.job_queue:
                    job_id = self.job_queue[0]
                    job = self.training_jobs[job_id]

                    if job.status == "pending":
                        # Find suitable cluster
                        cluster = await self._find_suitable_cluster(job)

                        if cluster:
                            # Schedule job
                            await self._schedule_job_on_cluster(job, cluster)
                            self.job_queue.remove(job_id)
                        else:
                            # Try to create new cluster if needed
                            await self._maybe_create_cluster_for_job(job)

                await asyncio.sleep(5)  # Check every 5 seconds

            except Exception as e:
                logger.error(f"Error in job scheduling loop: {e}")
                await asyncio.sleep(10)

    async def _find_suitable_cluster(self, job: TrainingJob) -> Optional[GPUCluster]:
        """Find a suitable cluster for the job."""

        for cluster in self.clusters.values():
            if cluster.status != "ready":
                continue

            # Check if cluster has required GPU type
            if job.gpu_type not in cluster.node_pools:
                continue

            # Check available resources
            if cluster.available_gpus >= job.gpu_count:
                return cluster

        return None

    async def _schedule_job_on_cluster(self, job: TrainingJob, cluster: GPUCluster) -> None:
        """Schedule job on a specific cluster."""

        try:
            # Create Kubernetes job specification
            k8s_job_spec = await self._create_k8s_job_spec(job, cluster)

            # Submit job to Kubernetes
            k8s_job = self.batch_v1.create_namespaced_job(
                namespace=self.namespace,
                body=k8s_job_spec
            )

            # Submit Ray job for distributed training
            if job.workload_type in [WorkloadType.MULTI_GPU, WorkloadType.DISTRIBUTED]:
                ray_job_id = await self._submit_ray_job(job, cluster)
                job.ray_job_id = ray_job_id

            # Update job status
            job.status = "running"
            job.cluster_id = cluster.cluster_id
            job.started_at = datetime.now()

            # Update cluster resources
            cluster.allocated_gpus += job.gpu_count
            cluster.available_gpus -= job.gpu_count

            logger.info(f"Scheduled job {job.job_id} on cluster {cluster.cluster_id}")

        except Exception as e:
            job.status = "failed"
            logger.error(f"Failed to schedule job {job.job_id}: {e}")
            raise

    async def _create_k8s_job_spec(self, job: TrainingJob, cluster: GPUCluster) -> Dict[str, Any]:
        """Create Kubernetes job specification."""

        job_spec = {
            "apiVersion": "batch/v1",
            "kind": "Job",
            "metadata": {
                "name": f"training-{job.job_id}",
                "namespace": self.namespace,
                "labels": {
                    "job-id": job.job_id,
                    "user-id": job.user_id,
                    "workload-type": job.workload_type.value,
                    "framework": job.framework,
                }
            },
            "spec": {
                "ttlSecondsAfterFinished": 3600,  # Clean up after 1 hour
                "activeDeadlineSeconds": job.max_runtime_hours * 3600,
                "template": {
                    "metadata": {
                        "labels": {
                            "job-id": job.job_id,
                            "workload-type": job.workload_type.value,
                        }
                    },
                    "spec": {
                        "restartPolicy": "Never",
                        "containers": [{
                            "name": "training-container",
                            "image": self._get_training_image(job.framework),
                            "command": ["/bin/bash", "-c"],
                            "args": [job.training_script],
                            "resources": {
                                "requests": {
                                    "cpu": str(job.cpu_cores),
                                    "memory": f"{job.memory_gb}Gi",
                                    "nvidia.com/gpu": str(job.gpu_count),
                                    "ephemeral-storage": f"{job.storage_gb}Gi"
                                },
                                "limits": {
                                    "cpu": str(job.cpu_cores * 2),
                                    "memory": f"{job.memory_gb * 2}Gi",
                                    "nvidia.com/gpu": str(job.gpu_count),
                                    "ephemeral-storage": f"{job.storage_gb}Gi"
                                }
                            },
                            "env": [
                                {"name": "JOB_ID", "value": job.job_id},
                                {"name": "CLUSTER_ID", "value": cluster.cluster_id},
                                {"name": "GPU_COUNT", "value": str(job.gpu_count)},
                                {"name": "FRAMEWORK", "value": job.framework},
                                {"name": "RAY_ADDRESS", "value": cluster.ray_cluster_address or ""},
                            ] + [
                                {"name": k, "value": v}
                                for k, v in job.environment_vars.items()
                            ],
                            "volumeMounts": [
                                {
                                    "name": "model-storage",
                                    "mountPath": "/workspace/models"
                                },
                                {
                                    "name": "data-storage",
                                    "mountPath": "/workspace/data"
                                }
                            ]
                        }],
                        "volumes": [
                            {
                                "name": "model-storage",
                                "persistentVolumeClaim": {
                                    "claimName": f"model-storage-{job.job_id}"
                                }
                            },
                            {
                                "name": "data-storage",
                                "persistentVolumeClaim": {
                                    "claimName": "shared-data-storage"
                                }
                            }
                        ],
                        "nodeSelector": {
                            "accelerator": job.gpu_type.value.replace("gpu-", "nvidia-tesla-")
                        } if job.gpu_type != NodeType.CPU_ONLY else {},
                        "tolerations": [
                            {
                                "key": "nvidia.com/gpu",
                                "operator": "Exists",
                                "effect": "NoSchedule"
                            }
                        ] if job.gpu_type != NodeType.CPU_ONLY else [],
                        "affinity": {
                            "nodeAffinity": {
                                "requiredDuringSchedulingIgnoredDuringExecution": {
                                    "nodeSelectorTerms": [{
                                        "matchExpressions": [{
                                            "key": "node-type",
                                            "operator": "In",
                                            "values": [job.gpu_type.value]
                                        }]
                                    }]
                                }
                            }
                        } if job.preemptible else {}
                    }
                }
            }
        }

        return job_spec

    def _get_training_image(self, framework: str) -> str:
        """Get training container image for framework."""

        images = {
            "pytorch": "neuroflow/pytorch-gpu:latest",
            "tensorflow": "neuroflow/tensorflow-gpu:latest",
            "jax": "neuroflow/jax-gpu:latest",
            "huggingface": "neuroflow/transformers-gpu:latest",
            "ray": "rayproject/ray:2.8.0-gpu",
        }

        return images.get(framework, "neuroflow/pytorch-gpu:latest")

    async def _submit_ray_job(self, job: TrainingJob, cluster: GPUCluster) -> str:
        """Submit distributed training job to Ray cluster."""

        if not cluster.ray_cluster_address:
            raise ValueError(f"Ray cluster not available for {cluster.cluster_id}")

        try:
            # Connect to Ray cluster
            ray.init(address=cluster.ray_cluster_address)

            # Create Ray job configuration
            ray_job_config = {
                "entrypoint": job.training_script,
                "runtime_env": {
                    "env_vars": {
                        **job.environment_vars,
                        "JOB_ID": job.job_id,
                        "GPU_COUNT": str(job.gpu_count),
                    },
                    "pip": self._get_pip_requirements(job.framework),
                },
                "metadata": {
                    "job_id": job.job_id,
                    "user_id": job.user_id,
                    "framework": job.framework,
                }
            }

            # Submit job
            from ray.job_submission import JobSubmissionClient
            client = JobSubmissionClient(cluster.ray_cluster_address)
            ray_job_id = client.submit_job(**ray_job_config)

            logger.info(f"Submitted Ray job {ray_job_id} for training job {job.job_id}")
            return ray_job_id

        except Exception as e:
            logger.error(f"Failed to submit Ray job: {e}")
            raise
        finally:
            ray.shutdown()

    def _get_pip_requirements(self, framework: str) -> List[str]:
        """Get pip requirements for framework."""

        base_requirements = [
            "numpy>=1.21.0",
            "pandas>=1.3.0",
            "scikit-learn>=1.0.0",
            "matplotlib>=3.4.0",
            "tensorboard>=2.7.0",
        ]

        framework_requirements = {
            "pytorch": [
                "torch>=2.0.0",
                "torchvision>=0.15.0",
                "lightning>=2.0.0",
            ],
            "tensorflow": [
                "tensorflow>=2.12.0",
                "tensorflow-datasets>=4.8.0",
            ],
            "jax": [
                "jax[gpu]>=0.4.0",
                "flax>=0.6.0",
                "optax>=0.1.0",
            ],
            "huggingface": [
                "transformers>=4.20.0",
                "datasets>=2.0.0",
                "accelerate>=0.20.0",
            ]
        }

        return base_requirements + framework_requirements.get(framework, [])

    async def cancel_training_job(self, job_id: str) -> bool:
        """Cancel a training job."""

        if job_id not in self.training_jobs:
            return False

        job = self.training_jobs[job_id]

        try:
            # Cancel Kubernetes job
            if job.cluster_id:
                self.batch_v1.delete_namespaced_job(
                    name=f"training-{job_id}",
                    namespace=self.namespace,
                    propagation_policy="Background"
                )

            # Cancel Ray job if exists
            if job.ray_job_id and job.cluster_id:
                cluster = self.clusters[job.cluster_id]
                if cluster.ray_cluster_address:
                    from ray.job_submission import JobSubmissionClient
                    client = JobSubmissionClient(cluster.ray_cluster_address)
                    client.stop_job(job.ray_job_id)

            # Update job status
            job.status = "cancelled"
            job.completed_at = datetime.now()

            # Free up cluster resources
            if job.cluster_id:
                cluster = self.clusters[job.cluster_id]
                cluster.allocated_gpus -= job.gpu_count
                cluster.available_gpus += job.gpu_count

            # Remove from queue if still pending
            if job_id in self.job_queue:
                self.job_queue.remove(job_id)

            logger.info(f"Cancelled training job {job_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to cancel job {job_id}: {e}")
            return False
