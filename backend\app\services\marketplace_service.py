"""
Marketplace Service for NeuroFlowAI
===================================

Comprehensive marketplace for AI models, workflows, and data products.
Handles publishing, discovery, transactions, and revenue sharing.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from dataclasses import dataclass, field
import stripe
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..models.marketplace import MarketplaceItem, Transaction, Review
from ..models.user import User
import logging

logger = logging.getLogger(__name__)


class ItemType(str, Enum):
    """Marketplace item types."""
    MODEL = "model"
    WORKFLOW = "workflow"
    DATASET = "dataset"
    AGENT = "agent"
    PIPELINE = "pipeline"
    TEMPLATE = "template"


class ItemStatus(str, Enum):
    """Item publication status."""
    DRAFT = "draft"
    PENDING_REVIEW = "pending_review"
    APPROVED = "approved"
    PUBLISHED = "published"
    SUSPENDED = "suspended"
    DEPRECATED = "deprecated"


class PricingModel(str, Enum):
    """Pricing models for marketplace items."""
    FREE = "free"
    ONE_TIME = "one_time"
    SUBSCRIPTION = "subscription"
    USAGE_BASED = "usage_based"
    REVENUE_SHARE = "revenue_share"


@dataclass
class MarketplaceMetrics:
    """Marketplace performance metrics."""
    total_items: int = 0
    total_publishers: int = 0
    total_revenue: float = 0.0
    monthly_active_users: int = 0
    average_rating: float = 0.0
    top_categories: List[str] = field(default_factory=list)
    trending_items: List[str] = field(default_factory=list)


@dataclass
class RevenueShare:
    """Revenue sharing configuration."""
    platform_fee: float = 0.30  # 30% platform fee
    publisher_share: float = 0.70  # 70% to publisher
    referral_bonus: float = 0.05  # 5% referral bonus
    volume_discounts: Dict[str, float] = field(default_factory=lambda: {
        "bronze": 0.25,    # >$1K monthly
        "silver": 0.20,    # >$10K monthly
        "gold": 0.15,      # >$100K monthly
        "platinum": 0.10   # >$1M monthly
    })


class MarketplaceService:
    """
    Comprehensive marketplace service for NeuroFlowAI.
    
    Features:
    - Model and workflow publishing
    - Advanced search and discovery
    - Secure transactions and payments
    - Revenue sharing and analytics
    - Quality assurance and reviews
    - Recommendation engine
    """
    
    def __init__(self):
        # Stripe configuration
        stripe.api_key = "sk_test_..."  # Configure with your Stripe key
        
        # Revenue sharing configuration
        self.revenue_share = RevenueShare()
        
        # Marketplace metrics
        self.metrics = MarketplaceMetrics()
        
        # Search index
        self.search_index: Dict[str, Any] = {}
        
        # Recommendation engine
        self.recommendation_cache: Dict[str, List[str]] = {}
        
        # Featured items
        self.featured_items: List[str] = []
        
        # Categories
        self.categories = {
            "computer_vision": "Computer Vision",
            "nlp": "Natural Language Processing",
            "time_series": "Time Series Analysis",
            "recommendation": "Recommendation Systems",
            "generative_ai": "Generative AI",
            "reinforcement_learning": "Reinforcement Learning",
            "data_processing": "Data Processing",
            "automation": "Automation Workflows",
            "analytics": "Analytics & Insights",
            "deployment": "Deployment Tools"
        }
    
    async def publish_item(
        self, 
        user_id: str, 
        item_data: Dict[str, Any],
        db: Session
    ) -> str:
        """Publish an item to the marketplace."""
        
        # Validate item data
        validation_result = await self._validate_item_data(item_data)
        if not validation_result["valid"]:
            raise ValueError(f"Invalid item data: {validation_result['errors']}")
        
        # Create marketplace item
        marketplace_item = MarketplaceItem(
            title=item_data["title"],
            description=item_data["description"],
            item_type=ItemType(item_data["type"]),
            category=item_data["category"],
            publisher_id=user_id,
            pricing_model=PricingModel(item_data["pricing_model"]),
            price=item_data.get("price", 0.0),
            metadata=item_data.get("metadata", {}),
            status=ItemStatus.PENDING_REVIEW,
            created_at=datetime.now()
        )
        
        db.add(marketplace_item)
        db.commit()
        db.refresh(marketplace_item)
        
        # Start review process
        await self._initiate_review_process(marketplace_item.id, db)
        
        # Update search index
        await self._update_search_index(marketplace_item)
        
        logger.info(f"Item {marketplace_item.id} published by user {user_id}")
        return marketplace_item.id
    
    async def search_items(
        self,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "relevance",
        page: int = 1,
        page_size: int = 20,
        db: Session = None
    ) -> Dict[str, Any]:
        """Search marketplace items with advanced filtering."""
        
        # Build search query
        search_results = await self._execute_search(query, filters, sort_by)
        
        # Paginate results
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_results = search_results[start_idx:end_idx]
        
        # Enrich results with additional data
        enriched_results = []
        for item_id in paginated_results:
            item_data = await self._get_enriched_item_data(item_id, db)
            enriched_results.append(item_data)
        
        return {
            "items": enriched_results,
            "total": len(search_results),
            "page": page,
            "page_size": page_size,
            "total_pages": (len(search_results) + page_size - 1) // page_size,
            "facets": await self._generate_search_facets(search_results, db)
        }
    
    async def get_recommendations(
        self,
        user_id: str,
        item_id: Optional[str] = None,
        limit: int = 10,
        db: Session = None
    ) -> List[Dict[str, Any]]:
        """Get personalized recommendations for a user."""
        
        # Check cache first
        cache_key = f"{user_id}:{item_id or 'general'}"
        if cache_key in self.recommendation_cache:
            cached_recommendations = self.recommendation_cache[cache_key]
            return await self._enrich_recommendations(cached_recommendations[:limit], db)
        
        # Generate recommendations
        recommendations = []
        
        if item_id:
            # Item-based recommendations
            recommendations.extend(await self._get_similar_items(item_id, db))
        
        # User-based recommendations
        user_recommendations = await self._get_user_based_recommendations(user_id, db)
        recommendations.extend(user_recommendations)
        
        # Trending items
        trending = await self._get_trending_items(db)
        recommendations.extend(trending)
        
        # Remove duplicates and limit
        unique_recommendations = list(dict.fromkeys(recommendations))[:limit]
        
        # Cache recommendations
        self.recommendation_cache[cache_key] = unique_recommendations
        
        return await self._enrich_recommendations(unique_recommendations, db)
    
    async def purchase_item(
        self,
        user_id: str,
        item_id: str,
        payment_method: str,
        db: Session
    ) -> Dict[str, Any]:
        """Process item purchase."""
        
        # Get item details
        item = db.query(MarketplaceItem).filter(MarketplaceItem.id == item_id).first()
        if not item:
            raise ValueError("Item not found")
        
        if item.status != ItemStatus.PUBLISHED:
            raise ValueError("Item is not available for purchase")
        
        # Check if user already owns the item
        existing_transaction = db.query(Transaction).filter(
            Transaction.buyer_id == user_id,
            Transaction.item_id == item_id,
            Transaction.status == "completed"
        ).first()
        
        if existing_transaction and item.pricing_model == PricingModel.ONE_TIME:
            raise ValueError("Item already purchased")
        
        # Calculate pricing
        pricing_details = await self._calculate_pricing(item, user_id, db)
        
        # Process payment
        payment_result = await self._process_payment(
            user_id, 
            pricing_details["total_amount"], 
            payment_method,
            item_id
        )
        
        if payment_result["status"] != "succeeded":
            raise ValueError(f"Payment failed: {payment_result['error']}")
        
        # Create transaction record
        transaction = Transaction(
            buyer_id=user_id,
            seller_id=item.publisher_id,
            item_id=item_id,
            amount=pricing_details["total_amount"],
            platform_fee=pricing_details["platform_fee"],
            seller_amount=pricing_details["seller_amount"],
            payment_intent_id=payment_result["payment_intent_id"],
            status="completed",
            created_at=datetime.now()
        )
        
        db.add(transaction)
        db.commit()
        
        # Grant access to item
        await self._grant_item_access(user_id, item_id, db)
        
        # Update metrics
        await self._update_purchase_metrics(item_id, pricing_details["total_amount"])
        
        # Send notifications
        await self._send_purchase_notifications(transaction, db)
        
        logger.info(f"Item {item_id} purchased by user {user_id}")
        
        return {
            "transaction_id": transaction.id,
            "status": "completed",
            "amount": pricing_details["total_amount"],
            "access_granted": True
        }
    
    async def submit_review(
        self,
        user_id: str,
        item_id: str,
        rating: int,
        comment: str,
        db: Session
    ) -> str:
        """Submit a review for an item."""
        
        # Validate rating
        if not 1 <= rating <= 5:
            raise ValueError("Rating must be between 1 and 5")
        
        # Check if user has purchased the item
        transaction = db.query(Transaction).filter(
            Transaction.buyer_id == user_id,
            Transaction.item_id == item_id,
            Transaction.status == "completed"
        ).first()
        
        if not transaction:
            raise ValueError("You must purchase the item before reviewing")
        
        # Check for existing review
        existing_review = db.query(Review).filter(
            Review.user_id == user_id,
            Review.item_id == item_id
        ).first()
        
        if existing_review:
            # Update existing review
            existing_review.rating = rating
            existing_review.comment = comment
            existing_review.updated_at = datetime.now()
            db.commit()
            review_id = existing_review.id
        else:
            # Create new review
            review = Review(
                user_id=user_id,
                item_id=item_id,
                rating=rating,
                comment=comment,
                created_at=datetime.now()
            )
            db.add(review)
            db.commit()
            db.refresh(review)
            review_id = review.id
        
        # Update item rating
        await self._update_item_rating(item_id, db)
        
        logger.info(f"Review submitted for item {item_id} by user {user_id}")
        return review_id
    
    async def get_publisher_analytics(
        self,
        publisher_id: str,
        start_date: datetime,
        end_date: datetime,
        db: Session
    ) -> Dict[str, Any]:
        """Get analytics for a publisher."""
        
        # Get publisher's items
        items = db.query(MarketplaceItem).filter(
            MarketplaceItem.publisher_id == publisher_id
        ).all()
        
        item_ids = [item.id for item in items]
        
        # Get transactions in date range
        transactions = db.query(Transaction).filter(
            Transaction.item_id.in_(item_ids),
            Transaction.created_at >= start_date,
            Transaction.created_at <= end_date,
            Transaction.status == "completed"
        ).all()
        
        # Calculate metrics
        total_revenue = sum(t.seller_amount for t in transactions)
        total_sales = len(transactions)
        
        # Revenue by item
        revenue_by_item = {}
        for transaction in transactions:
            item_id = transaction.item_id
            revenue_by_item[item_id] = revenue_by_item.get(item_id, 0) + transaction.seller_amount
        
        # Get reviews
        reviews = db.query(Review).filter(
            Review.item_id.in_(item_ids),
            Review.created_at >= start_date,
            Review.created_at <= end_date
        ).all()
        
        average_rating = sum(r.rating for r in reviews) / len(reviews) if reviews else 0
        
        # Top performing items
        top_items = sorted(
            revenue_by_item.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        return {
            "total_revenue": total_revenue,
            "total_sales": total_sales,
            "average_rating": average_rating,
            "total_reviews": len(reviews),
            "revenue_by_item": revenue_by_item,
            "top_performing_items": top_items,
            "revenue_trend": await self._calculate_revenue_trend(
                publisher_id, start_date, end_date, db
            )
        }
    
    async def get_marketplace_metrics(self, db: Session) -> MarketplaceMetrics:
        """Get overall marketplace metrics."""
        
        # Update metrics
        self.metrics.total_items = db.query(MarketplaceItem).filter(
            MarketplaceItem.status == ItemStatus.PUBLISHED
        ).count()
        
        self.metrics.total_publishers = db.query(MarketplaceItem.publisher_id).distinct().count()
        
        # Calculate total revenue (last 30 days)
        thirty_days_ago = datetime.now() - timedelta(days=30)
        recent_transactions = db.query(Transaction).filter(
            Transaction.created_at >= thirty_days_ago,
            Transaction.status == "completed"
        ).all()
        
        self.metrics.total_revenue = sum(t.amount for t in recent_transactions)
        
        # Calculate average rating
        reviews = db.query(Review).all()
        if reviews:
            self.metrics.average_rating = sum(r.rating for r in reviews) / len(reviews)
        
        # Get top categories
        category_counts = {}
        items = db.query(MarketplaceItem).filter(
            MarketplaceItem.status == ItemStatus.PUBLISHED
        ).all()
        
        for item in items:
            category = item.category
            category_counts[category] = category_counts.get(category, 0) + 1
        
        self.metrics.top_categories = sorted(
            category_counts.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        # Get trending items
        self.metrics.trending_items = await self._get_trending_items(db, limit=10)
        
        return self.metrics
    
    # Private helper methods
    
    async def _validate_item_data(self, item_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate item data before publishing."""
        
        errors = []
        
        # Required fields
        required_fields = ["title", "description", "type", "category", "pricing_model"]
        for field in required_fields:
            if field not in item_data or not item_data[field]:
                errors.append(f"Missing required field: {field}")
        
        # Validate item type
        if "type" in item_data:
            try:
                ItemType(item_data["type"])
            except ValueError:
                errors.append(f"Invalid item type: {item_data['type']}")
        
        # Validate pricing model
        if "pricing_model" in item_data:
            try:
                PricingModel(item_data["pricing_model"])
            except ValueError:
                errors.append(f"Invalid pricing model: {item_data['pricing_model']}")
        
        # Validate price
        if item_data.get("pricing_model") != "free" and not item_data.get("price"):
            errors.append("Price is required for non-free items")
        
        # Validate category
        if "category" in item_data and item_data["category"] not in self.categories:
            errors.append(f"Invalid category: {item_data['category']}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    async def _initiate_review_process(self, item_id: str, db: Session) -> None:
        """Initiate the review process for a new item."""
        
        # For now, auto-approve items (in production, implement proper review)
        item = db.query(MarketplaceItem).filter(MarketplaceItem.id == item_id).first()
        if item:
            item.status = ItemStatus.PUBLISHED
            item.published_at = datetime.now()
            db.commit()
        
        logger.info(f"Item {item_id} approved and published")
    
    async def _update_search_index(self, item: MarketplaceItem) -> None:
        """Update the search index with new item."""
        
        # Simple in-memory search index (in production, use Elasticsearch)
        search_terms = [
            item.title.lower(),
            item.description.lower(),
            item.category.lower(),
            item.item_type.value.lower()
        ]
        
        for term in search_terms:
            words = term.split()
            for word in words:
                if word not in self.search_index:
                    self.search_index[word] = []
                if item.id not in self.search_index[word]:
                    self.search_index[word].append(item.id)
    
    async def _execute_search(
        self,
        query: str,
        filters: Optional[Dict[str, Any]],
        sort_by: str
    ) -> List[str]:
        """Execute search query and return item IDs."""
        
        # Simple keyword search (in production, use proper search engine)
        query_words = query.lower().split()
        matching_items = set()
        
        for word in query_words:
            if word in self.search_index:
                if not matching_items:
                    matching_items = set(self.search_index[word])
                else:
                    matching_items &= set(self.search_index[word])
        
        # Apply filters (simplified)
        if filters:
            # Filter by category, price range, etc.
            pass
        
        # Sort results (simplified)
        if sort_by == "price_low":
            # Sort by price ascending
            pass
        elif sort_by == "price_high":
            # Sort by price descending
            pass
        elif sort_by == "rating":
            # Sort by rating
            pass
        
        return list(matching_items)
    
    async def _get_enriched_item_data(self, item_id: str, db: Session) -> Dict[str, Any]:
        """Get enriched item data for search results."""
        
        item = db.query(MarketplaceItem).filter(MarketplaceItem.id == item_id).first()
        if not item:
            return {}
        
        # Get average rating
        reviews = db.query(Review).filter(Review.item_id == item_id).all()
        avg_rating = sum(r.rating for r in reviews) / len(reviews) if reviews else 0
        
        # Get purchase count
        purchase_count = db.query(Transaction).filter(
            Transaction.item_id == item_id,
            Transaction.status == "completed"
        ).count()
        
        return {
            "id": item.id,
            "title": item.title,
            "description": item.description,
            "category": item.category,
            "price": item.price,
            "pricing_model": item.pricing_model.value,
            "average_rating": avg_rating,
            "review_count": len(reviews),
            "purchase_count": purchase_count,
            "publisher_id": item.publisher_id,
            "created_at": item.created_at.isoformat(),
            "thumbnail_url": item.metadata.get("thumbnail_url", "")
        }
