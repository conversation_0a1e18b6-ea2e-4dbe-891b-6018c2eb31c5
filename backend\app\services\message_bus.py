"""
Message Bus Service for NeuroFlowAI
===================================

High-performance message bus for inter-agent communication.
Supports pub/sub patterns, message queuing, and event streaming.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Callable, Set
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque

from ..agents.base_agent import AgentMessage, MessageType
import logging

logger = logging.getLogger(__name__)


class MessagePriority(int, Enum):
    """Message priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5


class SubscriptionType(str, Enum):
    """Subscription types for message filtering."""
    ALL = "all"
    BY_TYPE = "by_type"
    BY_SENDER = "by_sender"
    BY_PATTERN = "by_pattern"
    BY_TOPIC = "by_topic"


class MessageBus:
    """
    High-performance message bus for agent communication.
    
    Features:
    - Asynchronous message delivery
    - Priority-based message queuing
    - Pub/Sub pattern support
    - Message persistence and replay
    - Dead letter queue handling
    - Performance monitoring
    """
    
    def __init__(self, max_queue_size: int = 10000):
        # Message queues (per agent)
        self.agent_queues: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Priority queues for different message priorities
        self.priority_queues: Dict[MessagePriority, deque] = {
            priority: deque() for priority in MessagePriority
        }
        
        # Pub/Sub subscriptions
        self.subscriptions: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        self.topic_subscribers: Dict[str, Set[str]] = defaultdict(set)
        
        # Message persistence
        self.message_history: deque = deque(maxlen=max_queue_size)
        self.dead_letter_queue: deque = deque(maxlen=1000)
        
        # Performance metrics
        self.metrics = {
            "messages_sent": 0,
            "messages_delivered": 0,
            "messages_failed": 0,
            "average_delivery_time": 0.0,
            "queue_sizes": {},
            "subscription_count": 0
        }
        
        # Configuration
        self.is_running = False
        self.max_delivery_attempts = 3
        self.message_ttl = timedelta(hours=24)
        
        # Event handlers
        self.event_handlers: Dict[str, List[Callable]] = defaultdict(list)
    
    async def start(self) -> None:
        """Start the message bus."""
        self.is_running = True
        logger.info("Message Bus starting...")
        
        # Start message processing loops
        asyncio.create_task(self._message_delivery_loop())
        asyncio.create_task(self._cleanup_loop())
        asyncio.create_task(self._metrics_update_loop())
        
        logger.info("Message Bus started successfully")
    
    async def stop(self) -> None:
        """Stop the message bus."""
        self.is_running = False
        logger.info("Message Bus stopped")
    
    async def send_message(self, message: AgentMessage) -> bool:
        """Send a message through the bus."""
        try:
            # Validate message
            if not self._validate_message(message):
                logger.warning(f"Invalid message rejected: {message.id}")
                return False
            
            # Add timestamp if not present
            if not message.timestamp:
                message.timestamp = datetime.now()
            
            # Store in message history
            self.message_history.append(message)
            
            # Route message based on type and receiver
            await self._route_message(message)
            
            # Update metrics
            self.metrics["messages_sent"] += 1
            
            logger.debug(f"Message {message.id} sent successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message {message.id}: {e}")
            self.metrics["messages_failed"] += 1
            return False
    
    async def _route_message(self, message: AgentMessage) -> None:
        """Route message to appropriate queues and subscribers."""
        
        # Direct delivery to specific agent
        if message.receiver_id and message.receiver_id != "broadcast":
            await self._queue_for_agent(message, message.receiver_id)
        
        # Broadcast to all subscribers
        elif message.receiver_id == "broadcast":
            await self._broadcast_message(message)
        
        # Publish to topic subscribers
        if hasattr(message, 'topic') and message.topic:
            await self._publish_to_topic(message, message.topic)
        
        # Handle pub/sub subscriptions
        await self._handle_subscriptions(message)
    
    async def _queue_for_agent(self, message: AgentMessage, agent_id: str) -> None:
        """Queue message for specific agent."""
        
        # Add to agent's queue
        self.agent_queues[agent_id].append(message)
        
        # Also add to priority queue for processing
        priority = MessagePriority(message.priority)
        self.priority_queues[priority].append((agent_id, message))
        
        logger.debug(f"Queued message {message.id} for agent {agent_id}")
    
    async def _broadcast_message(self, message: AgentMessage) -> None:
        """Broadcast message to all registered agents."""
        
        for agent_id in self.agent_queues.keys():
            if agent_id != message.sender_id:  # Don't send back to sender
                await self._queue_for_agent(message, agent_id)
        
        logger.debug(f"Broadcast message {message.id} to all agents")
    
    async def _publish_to_topic(self, message: AgentMessage, topic: str) -> None:
        """Publish message to topic subscribers."""
        
        subscribers = self.topic_subscribers.get(topic, set())
        
        for subscriber_id in subscribers:
            if subscriber_id != message.sender_id:
                await self._queue_for_agent(message, subscriber_id)
        
        logger.debug(f"Published message {message.id} to topic {topic} ({len(subscribers)} subscribers)")
    
    async def _handle_subscriptions(self, message: AgentMessage) -> None:
        """Handle pub/sub subscriptions."""
        
        for agent_id, subscriptions in self.subscriptions.items():
            if agent_id == message.sender_id:
                continue  # Don't send back to sender
            
            for subscription in subscriptions:
                if self._message_matches_subscription(message, subscription):
                    await self._queue_for_agent(message, agent_id)
    
    def _message_matches_subscription(
        self, 
        message: AgentMessage, 
        subscription: Dict[str, Any]
    ) -> bool:
        """Check if message matches subscription criteria."""
        
        sub_type = subscription.get("type", SubscriptionType.ALL)
        
        if sub_type == SubscriptionType.ALL:
            return True
        elif sub_type == SubscriptionType.BY_TYPE:
            return message.message_type == subscription.get("message_type")
        elif sub_type == SubscriptionType.BY_SENDER:
            return message.sender_id == subscription.get("sender_id")
        elif sub_type == SubscriptionType.BY_PATTERN:
            pattern = subscription.get("pattern", "")
            return pattern in str(message.content)
        elif sub_type == SubscriptionType.BY_TOPIC:
            return getattr(message, 'topic', None) == subscription.get("topic")
        
        return False
    
    async def _message_delivery_loop(self) -> None:
        """Main message delivery loop."""
        
        while self.is_running:
            try:
                # Process messages by priority (highest first)
                message_processed = False
                
                for priority in reversed(list(MessagePriority)):
                    if self.priority_queues[priority]:
                        agent_id, message = self.priority_queues[priority].popleft()
                        await self._deliver_message(agent_id, message)
                        message_processed = True
                        break
                
                # If no priority messages, brief pause
                if not message_processed:
                    await asyncio.sleep(0.01)
                
            except Exception as e:
                logger.error(f"Error in message delivery loop: {e}")
                await asyncio.sleep(0.1)
    
    async def _deliver_message(self, agent_id: str, message: AgentMessage) -> None:
        """Deliver message to specific agent."""
        
        delivery_start = datetime.now()
        
        try:
            # Trigger delivery event
            await self._trigger_event("message_delivery", {
                "agent_id": agent_id,
                "message": message
            })
            
            # Update metrics
            delivery_time = (datetime.now() - delivery_start).total_seconds()
            self._update_delivery_metrics(delivery_time)
            
            self.metrics["messages_delivered"] += 1
            
            logger.debug(f"Delivered message {message.id} to agent {agent_id}")
            
        except Exception as e:
            logger.error(f"Failed to deliver message {message.id} to {agent_id}: {e}")
            
            # Add to dead letter queue if max attempts reached
            if getattr(message, 'delivery_attempts', 0) >= self.max_delivery_attempts:
                self.dead_letter_queue.append({
                    "message": message,
                    "agent_id": agent_id,
                    "error": str(e),
                    "timestamp": datetime.now()
                })
            else:
                # Retry delivery
                message.delivery_attempts = getattr(message, 'delivery_attempts', 0) + 1
                await self._queue_for_agent(message, agent_id)
            
            self.metrics["messages_failed"] += 1
    
    def _update_delivery_metrics(self, delivery_time: float) -> None:
        """Update delivery time metrics."""
        
        current_avg = self.metrics["average_delivery_time"]
        total_delivered = self.metrics["messages_delivered"]
        
        if total_delivered == 0:
            self.metrics["average_delivery_time"] = delivery_time
        else:
            # Exponential moving average
            alpha = 0.1
            self.metrics["average_delivery_time"] = (
                alpha * delivery_time + (1 - alpha) * current_avg
            )
    
    async def subscribe(
        self, 
        agent_id: str, 
        subscription_type: SubscriptionType,
        **criteria
    ) -> str:
        """Subscribe agent to messages based on criteria."""
        
        subscription_id = f"sub_{agent_id}_{len(self.subscriptions[agent_id])}"
        
        subscription = {
            "id": subscription_id,
            "type": subscription_type,
            "agent_id": agent_id,
            "created_at": datetime.now(),
            **criteria
        }
        
        self.subscriptions[agent_id].append(subscription)
        self.metrics["subscription_count"] += 1
        
        logger.info(f"Agent {agent_id} subscribed with {subscription_type}: {subscription_id}")
        
        return subscription_id
    
    async def unsubscribe(self, agent_id: str, subscription_id: str) -> bool:
        """Unsubscribe agent from messages."""
        
        subscriptions = self.subscriptions.get(agent_id, [])
        
        for i, subscription in enumerate(subscriptions):
            if subscription["id"] == subscription_id:
                del subscriptions[i]
                self.metrics["subscription_count"] -= 1
                logger.info(f"Agent {agent_id} unsubscribed: {subscription_id}")
                return True
        
        return False
    
    async def subscribe_to_topic(self, agent_id: str, topic: str) -> None:
        """Subscribe agent to a specific topic."""
        
        self.topic_subscribers[topic].add(agent_id)
        logger.info(f"Agent {agent_id} subscribed to topic: {topic}")
    
    async def unsubscribe_from_topic(self, agent_id: str, topic: str) -> None:
        """Unsubscribe agent from a topic."""
        
        self.topic_subscribers[topic].discard(agent_id)
        logger.info(f"Agent {agent_id} unsubscribed from topic: {topic}")
    
    async def get_messages(
        self, 
        agent_id: str, 
        limit: int = 10,
        since: Optional[datetime] = None
    ) -> List[AgentMessage]:
        """Get messages for an agent."""
        
        messages = list(self.agent_queues[agent_id])
        
        # Filter by timestamp if provided
        if since:
            messages = [m for m in messages if m.timestamp >= since]
        
        # Apply limit
        return messages[-limit:] if limit else messages
    
    async def replay_messages(
        self, 
        agent_id: str,
        from_time: datetime,
        to_time: Optional[datetime] = None
    ) -> List[AgentMessage]:
        """Replay messages for an agent from history."""
        
        to_time = to_time or datetime.now()
        
        replayed_messages = []
        
        for message in self.message_history:
            if (message.receiver_id == agent_id and 
                from_time <= message.timestamp <= to_time):
                replayed_messages.append(message)
        
        logger.info(f"Replayed {len(replayed_messages)} messages for agent {agent_id}")
        
        return replayed_messages
    
    async def _cleanup_loop(self) -> None:
        """Cleanup expired messages and maintain queue health."""
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # Clean up expired messages from history
                while (self.message_history and 
                       current_time - self.message_history[0].timestamp > self.message_ttl):
                    self.message_history.popleft()
                
                # Clean up expired messages from agent queues
                for agent_id, queue in self.agent_queues.items():
                    expired_count = 0
                    while (queue and 
                           current_time - queue[0].timestamp > self.message_ttl):
                        queue.popleft()
                        expired_count += 1
                    
                    if expired_count > 0:
                        logger.debug(f"Cleaned up {expired_count} expired messages for agent {agent_id}")
                
                # Sleep before next cleanup
                await asyncio.sleep(300)  # Clean up every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(60)
    
    async def _metrics_update_loop(self) -> None:
        """Update performance metrics periodically."""
        
        while self.is_running:
            try:
                # Update queue size metrics
                self.metrics["queue_sizes"] = {
                    agent_id: len(queue) 
                    for agent_id, queue in self.agent_queues.items()
                }
                
                # Sleep before next update
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in metrics update loop: {e}")
                await asyncio.sleep(30)
    
    def _validate_message(self, message: AgentMessage) -> bool:
        """Validate message before processing."""
        
        if not message.id:
            return False
        
        if not message.sender_id:
            return False
        
        if not message.message_type:
            return False
        
        return True
    
    async def add_event_handler(self, event_type: str, handler: Callable) -> None:
        """Add event handler for message bus events."""
        
        self.event_handlers[event_type].append(handler)
        logger.debug(f"Added event handler for {event_type}")
    
    async def _trigger_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """Trigger event handlers."""
        
        handlers = self.event_handlers.get(event_type, [])
        
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event_data)
                else:
                    handler(event_data)
            except Exception as e:
                logger.error(f"Error in event handler for {event_type}: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get message bus performance metrics."""
        
        return {
            **self.metrics,
            "total_subscriptions": sum(len(subs) for subs in self.subscriptions.values()),
            "active_topics": len(self.topic_subscribers),
            "message_history_size": len(self.message_history),
            "dead_letter_queue_size": len(self.dead_letter_queue),
            "priority_queue_sizes": {
                priority.name: len(queue) 
                for priority, queue in self.priority_queues.items()
            }
        }
    
    def get_dead_letter_messages(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get messages from dead letter queue."""
        
        return list(self.dead_letter_queue)[-limit:]
    
    async def clear_agent_queue(self, agent_id: str) -> int:
        """Clear all messages for an agent."""
        
        queue_size = len(self.agent_queues[agent_id])
        self.agent_queues[agent_id].clear()
        
        logger.info(f"Cleared {queue_size} messages for agent {agent_id}")
        
        return queue_size
