"""
Enterprise Machine Learning Service for AI/ML Platform
======================================================

Comprehensive ML service providing:
- Multi-framework model support (PyTorch, TensorFlow, Scikit-learn, XGBoost, etc.)
- Advanced model lifecycle management
- Distributed training and inference
- Model versioning and lineage tracking
- Performance monitoring and optimization
- Integration with all AI/ML modules
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import pickle
import joblib
import numpy as np
import pandas as pd

# Core imports
from app.core.logging import get_logger, LoggerMixin
from app.core.exceptions import ModelError, DataError, ValidationError
from app.core.config import settings

# Model imports
from app.models.model import MLModel, ModelType, ModelFramework, ModelStatus
from app.models.dataset import Dataset
from app.models.experiment import Experiment, ExperimentRun

# Module imports - AI/ML capabilities
from modules.computer_vision import ComputerVisionModule
from modules.nlp import NLPModule
from modules.time_series import TimeSeriesModule
from modules.reinforcement_learning import ReinforcementLearningModule
from modules.generative_ai import GenerativeAIModule
from modules.automl import AutoMLModule
from modules.deep_learning import DeepLearningModule
from modules.graph_neural_networks import GraphNeuralNetworkModule
from modules.quantum_ml import QuantumMLModule
from modules.federated_learning import FederatedLearningModule
from modules.explainable_ai import ExplainableAIModule
from modules.edge_ai import EdgeAIModule

logger = get_logger(__name__)


class MLService(LoggerMixin):
    """Core machine learning service."""
    
    def __init__(self):
        self.models: Dict[str, Any] = {}
        self.model_metadata: Dict[str, Dict] = {}
        self.is_initialized = False
        
    async def initialize(self) -> None:
        """Initialize the ML service."""
        try:
            self.logger.info("Initializing ML Service")
            
            # Create model storage directories
            model_dir = Path(settings.ML_MODEL_STORAGE_PATH)
            model_dir.mkdir(parents=True, exist_ok=True)
            
            # Load existing models
            await self._load_existing_models()
            
            self.is_initialized = True
            self.logger.info("ML Service initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize ML Service: {e}")
            raise ModelError(f"ML Service initialization failed: {e}")
    
    async def cleanup(self) -> None:
        """Cleanup ML service resources."""
        try:
            self.logger.info("Cleaning up ML Service")
            
            # Save model metadata
            await self._save_model_metadata()
            
            # Clear models from memory
            self.models.clear()
            self.model_metadata.clear()
            
            self.is_initialized = False
            self.logger.info("ML Service cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during ML Service cleanup: {e}")
    
    async def _load_existing_models(self) -> None:
        """Load existing models from storage."""
        try:
            model_dir = Path(settings.ML_MODEL_STORAGE_PATH)
            
            for model_file in model_dir.glob("*.pkl"):
                model_name = model_file.stem
                
                try:
                    # Load model
                    with open(model_file, 'rb') as f:
                        model = pickle.load(f)
                    
                    self.models[model_name] = model
                    
                    # Load metadata if exists
                    metadata_file = model_dir / f"{model_name}_metadata.json"
                    if metadata_file.exists():
                        import json
                        with open(metadata_file, 'r') as f:
                            self.model_metadata[model_name] = json.load(f)
                    
                    self.logger.info(f"Loaded model: {model_name}")
                    
                except Exception as e:
                    self.logger.warning(f"Failed to load model {model_name}: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error loading existing models: {e}")
    
    async def _save_model_metadata(self) -> None:
        """Save model metadata to storage."""
        try:
            import json
            model_dir = Path(settings.ML_MODEL_STORAGE_PATH)
            
            for model_name, metadata in self.model_metadata.items():
                metadata_file = model_dir / f"{model_name}_metadata.json"
                with open(metadata_file, 'w') as f:
                    json.dump(metadata, f, indent=2)
                    
        except Exception as e:
            self.logger.error(f"Error saving model metadata: {e}")
    
    async def train_model(
        self,
        model_name: str,
        algorithm: str,
        data: pd.DataFrame,
        target_column: str,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Train a machine learning model."""
        try:
            self.logger.info(f"Starting model training: {model_name}")
            
            if not self.is_initialized:
                raise ModelError("ML Service not initialized")
            
            # Validate data
            if data.empty:
                raise DataError("Training data is empty")
            
            if target_column not in data.columns:
                raise DataError(f"Target column '{target_column}' not found in data")
            
            # Prepare data
            X = data.drop(columns=[target_column])
            y = data[target_column]
            
            # Initialize model based on algorithm
            model = await self._create_model(algorithm, config or {})
            
            # Train model
            model.fit(X, y)
            
            # Store model
            self.models[model_name] = model
            
            # Store metadata
            self.model_metadata[model_name] = {
                "algorithm": algorithm,
                "features": list(X.columns),
                "target": target_column,
                "training_samples": len(data),
                "config": config or {},
                "created_at": pd.Timestamp.now().isoformat()
            }
            
            # Save to disk
            await self._save_model(model_name, model)
            
            self.logger.info(f"Model training completed: {model_name}")
            
            return {
                "model_name": model_name,
                "status": "trained",
                "metadata": self.model_metadata[model_name]
            }
            
        except Exception as e:
            self.logger.error(f"Model training failed: {e}")
            raise ModelError(f"Training failed for model {model_name}: {e}")
    
    async def _create_model(self, algorithm: str, config: Dict[str, Any]):
        """Create a model instance based on algorithm."""
        from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
        from sklearn.linear_model import LogisticRegression, LinearRegression
        from sklearn.svm import SVC, SVR
        from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor
        
        algorithm_map = {
            "random_forest_classifier": RandomForestClassifier,
            "random_forest_regressor": RandomForestRegressor,
            "logistic_regression": LogisticRegression,
            "linear_regression": LinearRegression,
            "svm_classifier": SVC,
            "svm_regressor": SVR,
            "decision_tree_classifier": DecisionTreeClassifier,
            "decision_tree_regressor": DecisionTreeRegressor,
        }
        
        if algorithm not in algorithm_map:
            raise ModelError(f"Unsupported algorithm: {algorithm}")
        
        model_class = algorithm_map[algorithm]
        return model_class(**config)
    
    async def _save_model(self, model_name: str, model: Any) -> None:
        """Save model to disk."""
        try:
            model_dir = Path(settings.ML_MODEL_STORAGE_PATH)
            model_file = model_dir / f"{model_name}.pkl"
            
            with open(model_file, 'wb') as f:
                pickle.dump(model, f)
                
        except Exception as e:
            self.logger.error(f"Error saving model {model_name}: {e}")
            raise ModelError(f"Failed to save model {model_name}: {e}")
    
    async def predict(
        self,
        model_name: str,
        data: pd.DataFrame
    ) -> Dict[str, Any]:
        """Make predictions using a trained model."""
        try:
            self.logger.info(f"Making predictions with model: {model_name}")
            
            if model_name not in self.models:
                raise ModelError(f"Model '{model_name}' not found")
            
            model = self.models[model_name]
            metadata = self.model_metadata.get(model_name, {})
            
            # Validate features
            expected_features = metadata.get("features", [])
            if expected_features and not all(col in data.columns for col in expected_features):
                missing_features = [col for col in expected_features if col not in data.columns]
                raise DataError(f"Missing features: {missing_features}")
            
            # Make predictions
            if expected_features:
                X = data[expected_features]
            else:
                X = data
            
            predictions = model.predict(X)
            
            # Get prediction probabilities if available
            probabilities = None
            if hasattr(model, 'predict_proba'):
                try:
                    probabilities = model.predict_proba(X).tolist()
                except:
                    pass
            
            result = {
                "model_name": model_name,
                "predictions": predictions.tolist(),
                "probabilities": probabilities,
                "num_samples": len(data)
            }
            
            self.logger.info(f"Predictions completed for model: {model_name}")
            return result
            
        except Exception as e:
            self.logger.error(f"Prediction failed: {e}")
            raise ModelError(f"Prediction failed for model {model_name}: {e}")
    
    async def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Get information about a model."""
        if model_name not in self.models:
            raise ModelError(f"Model '{model_name}' not found")
        
        return {
            "model_name": model_name,
            "metadata": self.model_metadata.get(model_name, {}),
            "is_loaded": True
        }
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """List all available models."""
        return [
            {
                "model_name": name,
                "metadata": self.model_metadata.get(name, {}),
                "is_loaded": True
            }
            for name in self.models.keys()
        ]
    
    async def delete_model(self, model_name: str) -> Dict[str, Any]:
        """Delete a model."""
        try:
            if model_name not in self.models:
                raise ModelError(f"Model '{model_name}' not found")
            
            # Remove from memory
            del self.models[model_name]
            if model_name in self.model_metadata:
                del self.model_metadata[model_name]
            
            # Remove from disk
            model_dir = Path(settings.ML_MODEL_STORAGE_PATH)
            model_file = model_dir / f"{model_name}.pkl"
            metadata_file = model_dir / f"{model_name}_metadata.json"
            
            if model_file.exists():
                model_file.unlink()
            if metadata_file.exists():
                metadata_file.unlink()
            
            self.logger.info(f"Model deleted: {model_name}")
            
            return {
                "model_name": model_name,
                "status": "deleted"
            }
            
        except Exception as e:
            self.logger.error(f"Error deleting model {model_name}: {e}")
            raise ModelError(f"Failed to delete model {model_name}: {e}")
