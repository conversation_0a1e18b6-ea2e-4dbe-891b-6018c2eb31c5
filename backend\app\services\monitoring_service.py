"""
Monitoring Service for the Enterprise AI/ML Platform.

This service provides monitoring, metrics collection, and health checking
functionality for the platform.
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import psutil
import json

from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry
from app.core.logging import get_logger, LoggerMixin
from app.core.config import settings

logger = get_logger(__name__)


class MonitoringService(LoggerMixin):
    """Core monitoring service for the platform."""
    
    def __init__(self):
        self.is_running = False
        self.metrics_registry = CollectorRegistry()
        self.start_time = time.time()
        
        # Initialize Prometheus metrics
        self._init_metrics()
        
        # System metrics storage
        self.system_metrics = {}
        self.ml_metrics = {}
        self.api_metrics = {}
        
    def _init_metrics(self):
        """Initialize Prometheus metrics."""
        # API metrics
        self.api_requests_total = Counter(
            'api_requests_total',
            'Total API requests',
            ['method', 'endpoint', 'status'],
            registry=self.metrics_registry
        )
        
        self.api_request_duration = Histogram(
            'api_request_duration_seconds',
            'API request duration',
            ['method', 'endpoint'],
            registry=self.metrics_registry
        )
        
        # System metrics
        self.system_cpu_usage = Gauge(
            'system_cpu_usage_percent',
            'System CPU usage percentage',
            registry=self.metrics_registry
        )
        
        self.system_memory_usage = Gauge(
            'system_memory_usage_bytes',
            'System memory usage in bytes',
            registry=self.metrics_registry
        )
        
        self.system_disk_usage = Gauge(
            'system_disk_usage_percent',
            'System disk usage percentage',
            registry=self.metrics_registry
        )
        
        # ML metrics
        self.ml_models_total = Gauge(
            'ml_models_total',
            'Total number of ML models',
            registry=self.metrics_registry
        )
        
        self.ml_predictions_total = Counter(
            'ml_predictions_total',
            'Total ML predictions made',
            ['model_name'],
            registry=self.metrics_registry
        )
        
        self.ml_training_duration = Histogram(
            'ml_training_duration_seconds',
            'ML model training duration',
            ['model_name', 'algorithm'],
            registry=self.metrics_registry
        )
        
        # Database metrics
        self.db_connections_active = Gauge(
            'db_connections_active',
            'Active database connections',
            registry=self.metrics_registry
        )
        
        self.db_query_duration = Histogram(
            'db_query_duration_seconds',
            'Database query duration',
            ['operation'],
            registry=self.metrics_registry
        )
    
    async def start(self) -> None:
        """Start the monitoring service."""
        try:
            self.logger.info("Starting Monitoring Service")
            
            self.is_running = True
            
            # Start background monitoring tasks
            asyncio.create_task(self._collect_system_metrics())
            asyncio.create_task(self._health_check_loop())
            
            self.logger.info("Monitoring Service started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start Monitoring Service: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the monitoring service."""
        try:
            self.logger.info("Stopping Monitoring Service")
            
            self.is_running = False
            
            # Save final metrics
            await self._save_metrics_snapshot()
            
            self.logger.info("Monitoring Service stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping Monitoring Service: {e}")
    
    async def _collect_system_metrics(self) -> None:
        """Collect system metrics periodically."""
        while self.is_running:
            try:
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=1)
                self.system_cpu_usage.set(cpu_percent)
                
                # Memory usage
                memory = psutil.virtual_memory()
                self.system_memory_usage.set(memory.used)
                
                # Disk usage
                disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100
                self.system_disk_usage.set(disk_percent)
                
                # Store in local metrics
                self.system_metrics.update({
                    'cpu_percent': cpu_percent,
                    'memory_used': memory.used,
                    'memory_total': memory.total,
                    'memory_percent': memory.percent,
                    'disk_used': disk.used,
                    'disk_total': disk.total,
                    'disk_percent': disk_percent,
                    'timestamp': datetime.utcnow().isoformat()
                })
                
                await asyncio.sleep(30)  # Collect every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error collecting system metrics: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _health_check_loop(self) -> None:
        """Perform periodic health checks."""
        while self.is_running:
            try:
                health_status = await self.get_health_status()
                
                # Log health status
                if health_status['status'] == 'healthy':
                    self.logger.debug("Health check passed")
                else:
                    self.logger.warning(f"Health check issues: {health_status}")
                
                await asyncio.sleep(60)  # Health check every minute
                
            except Exception as e:
                self.logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(120)  # Wait longer on error
    
    async def _save_metrics_snapshot(self) -> None:
        """Save current metrics snapshot."""
        try:
            snapshot = {
                'timestamp': datetime.utcnow().isoformat(),
                'system_metrics': self.system_metrics,
                'ml_metrics': self.ml_metrics,
                'api_metrics': self.api_metrics
            }
            
            # Save to file (in production, this would go to a time-series database)
            import os
            os.makedirs('logs/metrics', exist_ok=True)
            
            filename = f"logs/metrics/snapshot_{int(time.time())}.json"
            with open(filename, 'w') as f:
                json.dump(snapshot, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving metrics snapshot: {e}")
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get overall health status of the platform."""
        try:
            health_checks = {}
            overall_status = "healthy"
            
            # System health
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            disk_percent = (psutil.disk_usage('/').used / psutil.disk_usage('/').total) * 100
            
            health_checks['system'] = {
                'status': 'healthy' if cpu_percent < 80 and memory_percent < 80 and disk_percent < 80 else 'warning',
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent
            }
            
            if health_checks['system']['status'] != 'healthy':
                overall_status = 'warning'
            
            # Database health (placeholder)
            health_checks['database'] = {
                'status': 'healthy',  # Would check actual DB connection
                'connections': 'active'
            }
            
            # ML service health
            health_checks['ml_service'] = {
                'status': 'healthy',
                'models_loaded': len(self.ml_metrics.get('models', {}))
            }
            
            # API health
            uptime = time.time() - self.start_time
            health_checks['api'] = {
                'status': 'healthy',
                'uptime_seconds': uptime
            }
            
            return {
                'status': overall_status,
                'timestamp': datetime.utcnow().isoformat(),
                'checks': health_checks
            }
            
        except Exception as e:
            self.logger.error(f"Error getting health status: {e}")
            return {
                'status': 'error',
                'timestamp': datetime.utcnow().isoformat(),
                'error': str(e)
            }
    
    def record_api_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record API request metrics."""
        try:
            self.api_requests_total.labels(
                method=method,
                endpoint=endpoint,
                status=str(status_code)
            ).inc()
            
            self.api_request_duration.labels(
                method=method,
                endpoint=endpoint
            ).observe(duration)
            
            # Store in local metrics
            if 'requests' not in self.api_metrics:
                self.api_metrics['requests'] = []
            
            self.api_metrics['requests'].append({
                'method': method,
                'endpoint': endpoint,
                'status_code': status_code,
                'duration': duration,
                'timestamp': datetime.utcnow().isoformat()
            })
            
            # Keep only last 1000 requests
            if len(self.api_metrics['requests']) > 1000:
                self.api_metrics['requests'] = self.api_metrics['requests'][-1000:]
                
        except Exception as e:
            self.logger.error(f"Error recording API request metrics: {e}")
    
    def record_ml_prediction(self, model_name: str, duration: float = None):
        """Record ML prediction metrics."""
        try:
            self.ml_predictions_total.labels(model_name=model_name).inc()
            
            # Store in local metrics
            if 'predictions' not in self.ml_metrics:
                self.ml_metrics['predictions'] = []
            
            prediction_record = {
                'model_name': model_name,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            if duration is not None:
                prediction_record['duration'] = duration
            
            self.ml_metrics['predictions'].append(prediction_record)
            
            # Keep only last 1000 predictions
            if len(self.ml_metrics['predictions']) > 1000:
                self.ml_metrics['predictions'] = self.ml_metrics['predictions'][-1000:]
                
        except Exception as e:
            self.logger.error(f"Error recording ML prediction metrics: {e}")
    
    def record_ml_training(self, model_name: str, algorithm: str, duration: float):
        """Record ML training metrics."""
        try:
            self.ml_training_duration.labels(
                model_name=model_name,
                algorithm=algorithm
            ).observe(duration)
            
            # Store in local metrics
            if 'training' not in self.ml_metrics:
                self.ml_metrics['training'] = []
            
            self.ml_metrics['training'].append({
                'model_name': model_name,
                'algorithm': algorithm,
                'duration': duration,
                'timestamp': datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            self.logger.error(f"Error recording ML training metrics: {e}")
    
    def update_ml_models_count(self, count: int):
        """Update the count of ML models."""
        try:
            self.ml_models_total.set(count)
            
            self.ml_metrics['models_count'] = {
                'count': count,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error updating ML models count: {e}")
    
    async def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of all metrics."""
        try:
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'system': self.system_metrics,
                'ml': self.ml_metrics,
                'api': {
                    'total_requests': len(self.api_metrics.get('requests', [])),
                    'recent_requests': self.api_metrics.get('requests', [])[-10:]  # Last 10
                },
                'uptime_seconds': time.time() - self.start_time
            }
            
        except Exception as e:
            self.logger.error(f"Error getting metrics summary: {e}")
            return {'error': str(e)}
