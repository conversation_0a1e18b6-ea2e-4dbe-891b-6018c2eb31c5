"""
WebSocket Service for the Enterprise AI/ML Platform.

This service provides real-time communication capabilities for the platform,
including live updates, notifications, and real-time monitoring.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect

from app.core.logging import get_logger, LoggerMixin

logger = get_logger(__name__)


class ConnectionManager:
    """Manages WebSocket connections."""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_connections: Dict[str, List[str]] = {}  # user_id -> [client_ids]
        self.connection_metadata: Dict[str, Dict] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str, user_id: str = None):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        
        self.active_connections[client_id] = websocket
        self.connection_metadata[client_id] = {
            'user_id': user_id,
            'connected_at': datetime.utcnow().isoformat(),
            'last_activity': datetime.utcnow().isoformat()
        }
        
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = []
            self.user_connections[user_id].append(client_id)
        
        logger.info(f"WebSocket connected: {client_id} (user: {user_id})")
    
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection."""
        if client_id in self.active_connections:
            metadata = self.connection_metadata.get(client_id, {})
            user_id = metadata.get('user_id')
            
            # Remove from active connections
            del self.active_connections[client_id]
            del self.connection_metadata[client_id]
            
            # Remove from user connections
            if user_id and user_id in self.user_connections:
                if client_id in self.user_connections[user_id]:
                    self.user_connections[user_id].remove(client_id)
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
            
            logger.info(f"WebSocket disconnected: {client_id} (user: {user_id})")
    
    async def send_personal_message(self, message: str, client_id: str):
        """Send a message to a specific client."""
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                await websocket.send_text(message)
                
                # Update last activity
                if client_id in self.connection_metadata:
                    self.connection_metadata[client_id]['last_activity'] = datetime.utcnow().isoformat()
                
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                self.disconnect(client_id)
    
    async def send_user_message(self, message: str, user_id: str):
        """Send a message to all connections of a specific user."""
        if user_id in self.user_connections:
            client_ids = self.user_connections[user_id].copy()
            for client_id in client_ids:
                await self.send_personal_message(message, client_id)
    
    async def broadcast(self, message: str):
        """Broadcast a message to all connected clients."""
        client_ids = list(self.active_connections.keys())
        for client_id in client_ids:
            await self.send_personal_message(message, client_id)
    
    def get_connection_count(self) -> int:
        """Get the number of active connections."""
        return len(self.active_connections)
    
    def get_user_count(self) -> int:
        """Get the number of unique users connected."""
        return len(self.user_connections)
    
    def get_connections_info(self) -> Dict[str, Any]:
        """Get information about all connections."""
        return {
            'total_connections': self.get_connection_count(),
            'unique_users': self.get_user_count(),
            'connections': self.connection_metadata
        }


class WebSocketManager(LoggerMixin):
    """Main WebSocket service manager."""
    
    def __init__(self):
        self.connection_manager = ConnectionManager()
        self.message_handlers: Dict[str, callable] = {}
        self.is_running = False
        
        # Register default message handlers
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default message handlers."""
        self.message_handlers.update({
            'ping': self._handle_ping,
            'subscribe': self._handle_subscribe,
            'unsubscribe': self._handle_unsubscribe,
            'get_status': self._handle_get_status
        })
    
    async def connect(self, websocket: WebSocket, client_id: str, user_id: str = None):
        """Handle new WebSocket connection."""
        await self.connection_manager.connect(websocket, client_id, user_id)
        
        # Send welcome message
        welcome_message = {
            'type': 'welcome',
            'client_id': client_id,
            'timestamp': datetime.utcnow().isoformat(),
            'message': 'Connected to AI/ML Platform WebSocket'
        }
        await self.send_message(client_id, welcome_message)
    
    def disconnect(self, client_id: str):
        """Handle WebSocket disconnection."""
        self.connection_manager.disconnect(client_id)
    
    async def handle_message(self, client_id: str, message: str):
        """Handle incoming WebSocket message."""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type in self.message_handlers:
                handler = self.message_handlers[message_type]
                await handler(client_id, data)
            else:
                await self.send_error(client_id, f"Unknown message type: {message_type}")
                
        except json.JSONDecodeError:
            await self.send_error(client_id, "Invalid JSON message")
        except Exception as e:
            self.logger.error(f"Error handling message from {client_id}: {e}")
            await self.send_error(client_id, "Internal server error")
    
    async def send_message(self, client_id: str, message: Dict[str, Any]):
        """Send a structured message to a client."""
        message_str = json.dumps(message)
        await self.connection_manager.send_personal_message(message_str, client_id)
    
    async def send_user_message(self, user_id: str, message: Dict[str, Any]):
        """Send a structured message to all connections of a user."""
        message_str = json.dumps(message)
        await self.connection_manager.send_user_message(message_str, user_id)
    
    async def broadcast_message(self, message: Dict[str, Any]):
        """Broadcast a structured message to all clients."""
        message_str = json.dumps(message)
        await self.connection_manager.broadcast(message_str)
    
    async def send_error(self, client_id: str, error_message: str):
        """Send an error message to a client."""
        error_msg = {
            'type': 'error',
            'message': error_message,
            'timestamp': datetime.utcnow().isoformat()
        }
        await self.send_message(client_id, error_msg)
    
    # Message Handlers
    async def _handle_ping(self, client_id: str, data: Dict[str, Any]):
        """Handle ping message."""
        pong_message = {
            'type': 'pong',
            'timestamp': datetime.utcnow().isoformat()
        }
        await self.send_message(client_id, pong_message)
    
    async def _handle_subscribe(self, client_id: str, data: Dict[str, Any]):
        """Handle subscription to events."""
        topics = data.get('topics', [])
        
        # Store subscription info (in a real implementation, this would be more sophisticated)
        metadata = self.connection_manager.connection_metadata.get(client_id, {})
        metadata['subscriptions'] = topics
        
        response = {
            'type': 'subscription_confirmed',
            'topics': topics,
            'timestamp': datetime.utcnow().isoformat()
        }
        await self.send_message(client_id, response)
    
    async def _handle_unsubscribe(self, client_id: str, data: Dict[str, Any]):
        """Handle unsubscription from events."""
        topics = data.get('topics', [])
        
        # Remove subscription info
        metadata = self.connection_manager.connection_metadata.get(client_id, {})
        current_subscriptions = metadata.get('subscriptions', [])
        updated_subscriptions = [t for t in current_subscriptions if t not in topics]
        metadata['subscriptions'] = updated_subscriptions
        
        response = {
            'type': 'unsubscription_confirmed',
            'topics': topics,
            'timestamp': datetime.utcnow().isoformat()
        }
        await self.send_message(client_id, response)
    
    async def _handle_get_status(self, client_id: str, data: Dict[str, Any]):
        """Handle status request."""
        status = {
            'type': 'status',
            'connections': self.connection_manager.get_connections_info(),
            'timestamp': datetime.utcnow().isoformat()
        }
        await self.send_message(client_id, status)
    
    # Event Broadcasting Methods
    async def broadcast_ml_event(self, event_type: str, data: Dict[str, Any]):
        """Broadcast ML-related events."""
        message = {
            'type': 'ml_event',
            'event_type': event_type,
            'data': data,
            'timestamp': datetime.utcnow().isoformat()
        }
        await self.broadcast_message(message)
    
    async def broadcast_system_event(self, event_type: str, data: Dict[str, Any]):
        """Broadcast system-related events."""
        message = {
            'type': 'system_event',
            'event_type': event_type,
            'data': data,
            'timestamp': datetime.utcnow().isoformat()
        }
        await self.broadcast_message(message)
    
    async def send_notification(self, user_id: str, notification: Dict[str, Any]):
        """Send a notification to a specific user."""
        message = {
            'type': 'notification',
            'notification': notification,
            'timestamp': datetime.utcnow().isoformat()
        }
        await self.send_user_message(user_id, message)
    
    async def send_progress_update(self, user_id: str, task_id: str, progress: float, status: str):
        """Send a progress update for a long-running task."""
        message = {
            'type': 'progress_update',
            'task_id': task_id,
            'progress': progress,
            'status': status,
            'timestamp': datetime.utcnow().isoformat()
        }
        await self.send_user_message(user_id, message)
    
    def register_handler(self, message_type: str, handler: callable):
        """Register a custom message handler."""
        self.message_handlers[message_type] = handler
    
    def get_stats(self) -> Dict[str, Any]:
        """Get WebSocket service statistics."""
        return {
            'active_connections': self.connection_manager.get_connection_count(),
            'unique_users': self.connection_manager.get_user_count(),
            'registered_handlers': list(self.message_handlers.keys()),
            'is_running': self.is_running
        }


# Global WebSocket manager instance
websocket_manager = WebSocketManager()
