"""
Machine Learning background tasks for the Enterprise AI/ML Platform.

This module contains Celery tasks for ML operations that need to run
asynchronously in the background.
"""

import asyncio
import time
from typing import Dict, Any, Optional
from celery import Celery
import pandas as pd

from app.core.config import settings
from app.core.logging import get_logger
from app.services.ml_service import MLService
from app.services.websocket_service import websocket_manager

logger = get_logger(__name__)

# Initialize Celery
celery_app = Celery(
    "aiml_platform",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=['app.tasks.ml_tasks']
)

# Celery configuration
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)


@celery_app.task(bind=True)
def train_model_task(self, task_id: str, user_id: str, config: Dict[str, Any]):
    """
    Background task for training ML models.
    
    Args:
        task_id: Unique task identifier
        user_id: ID of the user who initiated the task
        config: Model training configuration
    """
    try:
        logger.info(f"Starting model training task: {task_id}")
        
        # Update task status
        self.update_state(
            state='PROGRESS',
            meta={'progress': 0, 'status': 'Initializing training...'}
        )
        
        # Send WebSocket update
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 0.0, 'Initializing training...'
        ))
        
        # Initialize ML service
        ml_service = MLService()
        asyncio.run(ml_service.initialize())
        
        # Extract configuration
        model_name = config.get('model_name')
        algorithm = config.get('algorithm')
        data_source = config.get('data_source')
        target_column = config.get('target_column')
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'progress': 10, 'status': 'Loading data...'}
        )
        
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 0.1, 'Loading data...'
        ))
        
        # Load data (placeholder - in real implementation, this would load from various sources)
        if data_source.endswith('.csv'):
            data = pd.read_csv(data_source)
        else:
            # For demo purposes, create sample data
            import numpy as np
            np.random.seed(42)
            data = pd.DataFrame({
                'feature1': np.random.randn(1000),
                'feature2': np.random.randn(1000),
                'feature3': np.random.randn(1000),
                'target': np.random.randint(0, 2, 1000)
            })
            target_column = 'target'
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'progress': 30, 'status': 'Preprocessing data...'}
        )
        
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 0.3, 'Preprocessing data...'
        ))
        
        # Data preprocessing (placeholder)
        time.sleep(2)  # Simulate preprocessing time
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'progress': 50, 'status': 'Training model...'}
        )
        
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 0.5, 'Training model...'
        ))
        
        # Train the model
        start_time = time.time()
        result = asyncio.run(ml_service.train_model(
            model_name=model_name,
            algorithm=algorithm,
            data=data,
            target_column=target_column,
            config=config.get('model_params', {})
        ))
        training_time = time.time() - start_time
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'progress': 80, 'status': 'Evaluating model...'}
        )
        
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 0.8, 'Evaluating model...'
        ))
        
        # Model evaluation (placeholder)
        time.sleep(1)
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'progress': 95, 'status': 'Saving model...'}
        )
        
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 0.95, 'Saving model...'
        ))
        
        # Cleanup
        asyncio.run(ml_service.cleanup())
        
        # Final result
        final_result = {
            'task_id': task_id,
            'model_name': model_name,
            'algorithm': algorithm,
            'training_time': training_time,
            'status': 'completed',
            'result': result
        }
        
        # Update progress to complete
        self.update_state(
            state='SUCCESS',
            meta={'progress': 100, 'status': 'Training completed successfully!'}
        )
        
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 1.0, 'Training completed successfully!'
        ))
        
        # Send completion notification
        asyncio.run(websocket_manager.send_notification(
            user_id,
            {
                'type': 'model_training_complete',
                'model_name': model_name,
                'task_id': task_id,
                'training_time': training_time
            }
        ))
        
        logger.info(f"Model training task completed: {task_id}")
        return final_result
        
    except Exception as e:
        logger.error(f"Model training task failed: {task_id}, error: {e}")
        
        # Update task state to failure
        self.update_state(
            state='FAILURE',
            meta={'progress': 0, 'status': f'Training failed: {str(e)}'}
        )
        
        # Send failure notification
        asyncio.run(websocket_manager.send_notification(
            user_id,
            {
                'type': 'model_training_failed',
                'task_id': task_id,
                'error': str(e)
            }
        ))
        
        raise


@celery_app.task(bind=True)
def batch_prediction_task(self, task_id: str, user_id: str, config: Dict[str, Any]):
    """
    Background task for batch predictions.
    
    Args:
        task_id: Unique task identifier
        user_id: ID of the user who initiated the task
        config: Prediction configuration
    """
    try:
        logger.info(f"Starting batch prediction task: {task_id}")
        
        # Update task status
        self.update_state(
            state='PROGRESS',
            meta={'progress': 0, 'status': 'Initializing predictions...'}
        )
        
        # Send WebSocket update
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 0.0, 'Initializing predictions...'
        ))
        
        # Initialize ML service
        ml_service = MLService()
        asyncio.run(ml_service.initialize())
        
        # Extract configuration
        model_name = config.get('model_name')
        data_source = config.get('data_source')
        output_path = config.get('output_path')
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'progress': 20, 'status': 'Loading data...'}
        )
        
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 0.2, 'Loading data...'
        ))
        
        # Load data
        if data_source.endswith('.csv'):
            data = pd.read_csv(data_source)
        else:
            # For demo purposes, create sample data
            import numpy as np
            np.random.seed(42)
            data = pd.DataFrame({
                'feature1': np.random.randn(1000),
                'feature2': np.random.randn(1000),
                'feature3': np.random.randn(1000)
            })
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'progress': 50, 'status': 'Making predictions...'}
        )
        
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 0.5, 'Making predictions...'
        ))
        
        # Make predictions
        start_time = time.time()
        result = asyncio.run(ml_service.predict(model_name, data))
        prediction_time = time.time() - start_time
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'progress': 80, 'status': 'Saving results...'}
        )
        
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 0.8, 'Saving results...'
        ))
        
        # Save results
        if output_path:
            predictions_df = pd.DataFrame({
                'prediction': result['predictions']
            })
            if result.get('probabilities'):
                for i, prob in enumerate(zip(*result['probabilities'])):
                    predictions_df[f'probability_class_{i}'] = prob
            
            predictions_df.to_csv(output_path, index=False)
        
        # Cleanup
        asyncio.run(ml_service.cleanup())
        
        # Final result
        final_result = {
            'task_id': task_id,
            'model_name': model_name,
            'num_predictions': result['num_samples'],
            'prediction_time': prediction_time,
            'output_path': output_path,
            'status': 'completed'
        }
        
        # Update progress to complete
        self.update_state(
            state='SUCCESS',
            meta={'progress': 100, 'status': 'Predictions completed successfully!'}
        )
        
        asyncio.run(websocket_manager.send_progress_update(
            user_id, task_id, 1.0, 'Predictions completed successfully!'
        ))
        
        # Send completion notification
        asyncio.run(websocket_manager.send_notification(
            user_id,
            {
                'type': 'batch_prediction_complete',
                'model_name': model_name,
                'task_id': task_id,
                'num_predictions': result['num_samples']
            }
        ))
        
        logger.info(f"Batch prediction task completed: {task_id}")
        return final_result
        
    except Exception as e:
        logger.error(f"Batch prediction task failed: {task_id}, error: {e}")
        
        # Update task state to failure
        self.update_state(
            state='FAILURE',
            meta={'progress': 0, 'status': f'Predictions failed: {str(e)}'}
        )
        
        # Send failure notification
        asyncio.run(websocket_manager.send_notification(
            user_id,
            {
                'type': 'batch_prediction_failed',
                'task_id': task_id,
                'error': str(e)
            }
        ))
        
        raise


@celery_app.task
def cleanup_old_models():
    """Periodic task to cleanup old models."""
    try:
        logger.info("Starting model cleanup task")
        
        # This would implement logic to clean up old, unused models
        # For now, just log the task execution
        
        logger.info("Model cleanup task completed")
        return {"status": "completed", "message": "Old models cleaned up"}
        
    except Exception as e:
        logger.error(f"Model cleanup task failed: {e}")
        raise


# Periodic tasks configuration
celery_app.conf.beat_schedule = {
    'cleanup-old-models': {
        'task': 'app.tasks.ml_tasks.cleanup_old_models',
        'schedule': 3600.0,  # Run every hour
    },
}
celery_app.conf.timezone = 'UTC'
