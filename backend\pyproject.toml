[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "aiml-platform-backend"
version = "2.0.0"
description = "Enterprise AI/ML Platform - FastAPI Backend"
authors = [{name = "AI Platform Team", email = "<EMAIL>"}]
readme = "README.md"
requires-python = ">=3.12, <3.13"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    # Core Framework
    "fastapi",
    "uvicorn[standard]",
    "pydantic",
    "pydantic-settings",
    
    # Database & ORM
    "sqlalchemy",
    "asyncpg",
    "alembic",
    "motor>=3.3.2",  # Async MongoDB
    "redis[hiredis]",
    
    # Authentication & Security
    "python-jose[cryptography]",
    "passlib[bcrypt]",
    "python-multipart>=0.0.6",
    
    # Task Queue
    "celery[redis]",
    
    # ML Core
    "scikit-learn",
    "pandas",
    "numpy",
    "scipy>=1.11.4",
    
    # AutoML
    "autogluon.tabular>=0.8.2",
    "autogluon.multimodal>=0.8.2",
    "autogluon.timeseries>=0.8.2",
    
    # Deep Learning
    "torch>=2.1.1",
    "torchvision>=0.16.1",
    "transformers>=4.36.0",
    "datasets>=2.14.7",
    
    # Computer Vision
    "opencv-python>=4.8.1",
    "Pillow>=10.1.0",
    "albumentations>=1.3.1",
    
    # NLP
    "spacy>=3.7.2",
    "nltk>=3.8.1",
    "sentence-transformers>=2.2.2",
    
    # Time Series
    "prophet>=1.1.4",
    "statsmodels>=0.14.0",
    
    # Monitoring & Observability
    "prometheus-client>=0.19.0",
    "prometheus-fastapi-instrumentator>=6.1.0",
    "structlog>=23.2.0",
    
    # Utilities
    "httpx>=0.25.2",
    "python-dotenv>=1.0.0",
    "typer>=0.9.0",
    "rich>=13.7.0",
    "tenacity>=8.2.3",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-xdist>=3.3.1",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "bandit>=1.7.5",
    "pre-commit>=3.5.0",
    "ipython>=8.17.2",
    "jupyter>=1.0.0",
    "notebook>=7.0.6",
    "sphinx>=7.2.6",
    "sphinx-rtd-theme>=1.3.0",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
    "httpie>=3.2.2",
    "safety>=2.3.5",
    "watchdog>=3.0.0",
]
test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.2",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
]
lint = [
    "ruff>=0.1.6",
    "black>=23.11.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
]
all = [
    "aiml-platform-backend[dev,test,lint]",
]

[project.scripts]
aiml-platform = "app.cli:main"

[tool.hatch.version]
path = "app/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["app"]

# Ruff configuration
[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"app/tests/*" = ["S101"]

# Black configuration
[tool.black]
target-version = ['py311']
line-length = 88
include = '\.pyi?$'
exclude = '''
/(
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

# MyPy configuration
[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_untyped_calls = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
implicit_reexport = false
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "autogluon.*",
    "torch.*",
    "torchvision.*",
    "cv2.*",
    "albumentations.*",
    "prophet.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m 'not slow'')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

# Coverage configuration
[tool.coverage.run]
source = ["app"]
omit = [
    "app/tests/*",
    "app/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\.)?abstractmethod",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "enterprise-aiml-platform"
version = "2.0.0"
description = "Enterprise AI/ML Platform - Production-ready no-code AI/ML platform"
readme = "README.md"
license = "MIT"
requires-python = ">=3.12, <3.13"
authors = [
    { name = "Enterprise AI/ML Platform Team", email = "<EMAIL>" },
]
keywords = [
    "ai",
    "ml",
    "machine-learning",
    "artificial-intelligence",
    "no-code",
    "platform",
    "enterprise",
    "fastapi",
    "python",
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Framework :: FastAPI",
]

dependencies = [
    "fastapi",
    "uvicorn[standard]",
    "pydantic",
    "pydantic-settings",
    "sqlalchemy",
    "alembic",
    "asyncpg",
    "psycopg2-binary",
    "redis",
    "celery",
    "python-multipart",
    "python-jose[cryptography]",
    "passlib[bcrypt]",
    "python-dotenv",
    "httpx",
    "aiofiles",
    "jinja2",
    "pandas",
    "numpy",
    "scikit-learn",
    "xgboost",
    "lightgbm",
    "catboost",
    "autogluon",
    "torch",
    "torchvision",
    "transformers",
    "datasets",
    "accelerate",
    "diffusers",
    "opencv-python",
    "pillow",
    "matplotlib",
    "seaborn",
    "plotly",
    "streamlit",
    "gradio",
    "prometheus-client",
    "structlog",
    "python-json-logger",
    "sentry-sdk[fastapi]",
    "boto3",
    "azure-storage-blob",
    "google-cloud-storage",
    "kubernetes",
    "docker",
    "pyyaml",
    "click",
    "rich",
    "typer",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-asyncio",
    "pytest-cov",
    "pytest-mock",
    "pytest-xdist",
    "black",
    "isort",
    "flake8",
    "mypy",
    "bandit",
    "pre-commit",
    "ipython",
    "jupyter",
    "notebook",
    "sphinx",
    "sphinx-rtd-theme",
    "factory-boy",
    "faker",
    "httpie",
    "safety",
    "watchdog",
]

test = [
    "pytest",
    "pytest-asyncio",
    "pytest-cov",
    "pytest-mock",
    "httpx",
    "factory-boy",
    "faker",
]

docs = [
    "sphinx",
    "sphinx-rtd-theme",
    "sphinx-autodoc-typehints",
]

[project.urls]
Homepage = "https://github.com/aiml-platform/platform"
Documentation = "https://docs.aiml-platform.com"
Repository = "https://github.com/aiml-platform/platform.git"
"Bug Tracker" = "https://github.com/aiml-platform/platform/issues"
Changelog = "https://github.com/aiml-platform/platform/blob/main/CHANGELOG.md"

[project.scripts]
aiml-platform = "app.cli:main"

[tool.hatch.version]
path = "app/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
  | alembic
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88
skip_gitignore = true
skip_glob = ["migrations/*", "alembic/*"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "celery.*",
    "redis.*",
    "sklearn.*",
    "xgboost.*",
    "lightgbm.*",
    "catboost.*",
    "autogluon.*",
    "torch.*",
    "torchvision.*",
    "transformers.*",
    "datasets.*",
    "diffusers.*",
    "cv2.*",
    "matplotlib.*",
    "seaborn.*",
    "plotly.*",
    "streamlit.*",
    "gradio.*",
    "boto3.*",
    "azure.*",
    "google.*",
    "kubernetes.*",
    "docker.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow running tests",
    "auth: Tests requiring authentication",
    "ml: Machine learning related tests",
    "api: API endpoint tests",
    "database: Database related tests",
    "celery: Celery task tests",
    "websocket: WebSocket tests",
]
asyncio_mode = "auto"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/alembic/*",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "migrations", "alembic"]
skips = ["B101", "B601"]
