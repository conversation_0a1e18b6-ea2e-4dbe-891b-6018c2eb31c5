[tool:pytest]
# Pytest configuration for Enterprise AI/ML Platform Backend

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --durations=10
    --maxfail=5

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    auth: Tests requiring authentication
    ml: Machine learning related tests
    api: API endpoint tests
    database: Database related tests
    celery: Celery task tests
    websocket: WebSocket tests

# Test timeout
timeout = 300

# Asyncio mode
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:tensorflow.*
    ignore::UserWarning:torch.*

# Environment variables for testing
env =
    ENVIRONMENT = test
    DATABASE_URL = sqlite:///./test.db
    SECRET_KEY = test-secret-key-for-testing-only
    ALGORITHM = HS256
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    REDIS_URL = redis://localhost:6379/1
    CELERY_BROKER_URL = redis://localhost:6379/2
    CELERY_RESULT_BACKEND = redis://localhost:6379/3
