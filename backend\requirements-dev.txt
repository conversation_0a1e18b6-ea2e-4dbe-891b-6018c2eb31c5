# Development dependencies for Enterprise AI/ML Platform Backend
# These are additional packages needed for development, testing, and code quality

# Include production requirements
-r requirements.txt

# Development Tools
ipython
jupyter
notebook

# Testing
pytest
pytest-asyncio
pytest-cov
pytest-mock
pytest-xdist
httpx
factory-boy
faker

# Code Quality
black
isort
flake8
mypy
bandit
pre-commit

# Documentation
sphinx
sphinx-rtd-theme
sphinx-autodoc-typehints

# Debugging
pdb++
ipdb

# Performance Profiling
py-spy
memory-profiler
line-profiler

# Database Tools
alembic
pgcli

# API Testing
httpie

# Type Checking
types-redis
types-requests
types-PyYAML

# Linting Extensions
flake8-docstrings
flake8-import-order
flake8-bugbear

# Security
safety

# Environment Management
python-dotenv

# Development Server
watchdog
