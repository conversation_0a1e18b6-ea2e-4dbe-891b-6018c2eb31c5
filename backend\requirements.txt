absl-py==2.3.0
accelerate==1.7.0
adagio==0.2.6
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.6
aiohttp-cors==0.8.1
aiohttp-retry==2.9.1
aiomysql==0.2.0
aiosignal==1.3.2
aiosqlite==0.21.0
alabaster==1.0.0
albucore==0.0.24
albumentations==2.0.8
ale-py==0.11.1
alembic==1.16.1
amqp==5.3.1
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
appdirs==1.4.4
astunparse==1.6.3
asyncpg==0.30.0
asyncssh==2.21.0
atpublic==6.0.1
attrs==25.3.0
audioread==3.0.1
Authlib==1.6.0
autogluon==1.3.1
autogluon.common==1.3.1
autogluon.core==1.3.1
autogluon.features==1.3.1
autogluon.multimodal==1.3.1
autogluon.tabular==1.3.1
autogluon.timeseries==1.3.1
azure-core==1.34.0
azure-storage-blob==12.25.1
babel==2.17.0
bandit==1.8.3
bayesian-optimization==2.0.4
bcrypt==4.3.0
beartype==0.21.0
beautifulsoup4==4.13.4
billiard==4.2.1
black==25.1.0
blinker==1.9.0
blis==1.3.0
boto3==1.38.27
botocore==1.38.27
cachetools==5.5.2
catalogue==2.0.10
catboost==1.2.8
category_encoders==2.8.1
celery==5.5.2
Cerberus==1.3.7
certifi==2025.4.26
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
click==8.2.1
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cloudpathlib==0.21.1
cloudpickle==3.1.1
cmdstanpy==1.2.5
colorama==0.4.6
coloredlogs==15.0.1
colorful==0.5.6
colorlog==6.9.0
confection==0.1.5
configobj==5.0.9
contourpy==1.3.2
coreforecast==0.0.15
coverage==7.8.2
cryptography==45.0.3
cssselect==1.3.0
cssutils==2.11.1
cycler==0.12.1
cymem==2.0.11
Cython==3.1.1
dask==2025.5.1
databricks-sdk==0.55.0
datasets==3.6.0
decorator==5.2.1
defusedxml==0.7.1
Deprecated==1.2.18
dictdiffer==0.9.0
diffusers==0.33.1
dill==0.3.8
diskcache==5.6.3
distlib==0.3.9
distro==1.9.0
dm-tree==0.1.9
dnspython==2.7.0
docker==7.1.0
docker-pycreds==0.4.0
docplex==2.29.245
docutils==0.21.2
dparse==0.6.4
dpath==2.2.0
dulwich==0.22.8
dvc==3.59.2
dvc-data==3.16.10
dvc-http==2.32.0
dvc-objects==5.1.0
dvc-render==1.0.2
dvc-studio-client==0.21.0
dvc-task==0.40.2
dynaconf==3.2.11
ecdsa==0.19.1
einops==0.8.1
emails==0.6
entrypoints==0.4
evaluate==0.4.3
Faker==37.3.0
Farama-Notifications==0.0.4
fastai==2.8.2
fastapi==0.115.12
fastcore==1.8.2
fastdownload==0.0.7
fastprogress==1.0.3
fasttransform==0.0.2
feature-engine==1.8.3
filelock==3.16.1
flake8==7.2.0
Flask==3.1.1
flatbuffers==25.2.10
flatten-dict==0.4.2
flower==2.0.1
flufl.lock==8.2.0
fonttools==4.58.1
frozenlist==1.6.0
fs==2.4.16
fsspec==2025.3.0
fugue==0.9.1
funcy==2.0
future==1.0.0
gast==0.6.0
gdown==5.2.0
gitdb==4.0.12
GitPython==3.1.44
gluonts==0.16.1
google-api-core==2.24.2
google-auth==2.40.2
google-cloud-core==2.4.3
google-cloud-storage==3.1.0
google-crc32c==1.7.1
google-pasta==0.2.0
google-resumable-media==2.7.2
googleapis-common-protos==1.70.0
grandalf==0.8
graphene==3.4.3
graphql-core==3.2.6
graphql-relay==3.2.0
graphviz==0.20.3
greenlet==3.2.2
grpcio==1.71.0
gto==1.7.2
gunicorn==23.0.0
gymnasium==1.1.1
h11==0.16.0
h5py==3.13.0
hiredis==3.2.1
holidays==0.73
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.32.3
humanfriendly==10.0
humanize==4.12.3
hydra-core==1.3.2
hyperopt==0.2.7
idna==3.10
igraph==0.11.8
imageio==2.37.0
imagesize==1.4.1
importlib_metadata==8.6.1
importlib_resources==6.5.2
iniconfig==2.1.0
isodate==0.7.2
isort==6.0.1
iterative-telemetry==0.0.10
itsdangerous==2.2.0
Jinja2==3.1.6
jmespath==1.0.1
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
keras==3.10.0
kiwisolver==1.4.8
kombu==5.5.3
langcodes==3.5.0
language_data==1.3.0
lazy_loader==0.4
Levenshtein==0.27.1
libclang==18.1.1
librosa==0.11.0
lightgbm==4.6.0
lightning==2.5.1.post0
lightning-utilities==0.14.3
lime==*******
limits==5.2.0
llvmlite==0.44.0
locket==1.0.0
lxml==5.4.0
lz4==4.4.4
Mako==1.3.10
marisa-trie==1.2.1
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==4.0.0
matplotlib==3.10.3
mccabe==0.7.0
mdurl==0.1.2
memory-profiler==0.61.0
ml_dtypes==0.5.1
mlflow==2.22.0
mlflow-skinny==2.22.0
mlforecast==0.13.6
model-index==0.1.11
more-itertools==10.7.0
motor==3.7.1
mpmath==1.3.0
msgpack==1.1.0
multidict==6.4.4
multiprocess==0.70.16
murmurhash==1.0.13
mypy==1.16.0
mypy_extensions==1.1.0
namex==0.1.0
narwhals==1.41.0
networkx==3.5
nlpaug==1.1.11
nltk==3.8.1
numba==0.61.2
numpy==2.1.3
nvidia-cuda-runtime-cu12==12.9.37
nvidia-ml-py3==7.352.0
omegaconf==2.3.0
onnx==1.18.0
onnxruntime==1.22.0
opencensus==0.11.4
opencensus-context==0.1.3
opencv-python==*********
opencv-python-headless==*********
opendatalab==0.0.10
openmim==0.3.9
opentelemetry-api==1.33.1
opentelemetry-sdk==1.33.1
opentelemetry-semantic-conventions==0.54b1
openvino==2025.1.0
openvino-telemetry==2025.1.0
openxlab==0.0.11
opt_einsum==3.4.0
optree==0.16.0
optuna==4.3.0
ordered-set==4.1.0
orjson==3.10.18
packaging==24.2
pandas==2.2.3
partd==1.4.2
passlib==1.7.4
pathspec==0.12.1
patsy==1.0.1
pbr==6.1.1
pdf2image==1.17.0
pillow==11.2.1
platformdirs==4.3.8
plotly==6.1.2
pluggy==1.6.0
plum-dispatch==2.5.7
pmdarima==2.0.4
pooch==1.8.2
premailer==3.10.0
preshed==3.0.10
prometheus-fastapi-instrumentator==7.1.0
prometheus_client==0.22.0
prompt_toolkit==3.0.51
propcache==0.3.1
prophet==1.1.7
proto-plus==1.26.1
protobuf==5.29.5
psutil==6.1.1
py-spy==0.4.0
py4j==0.10.9.9
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycodestyle==2.13.0
pycparser==2.22
pycryptodome==3.23.0
pydantic==2.9.2
pydantic-settings==2.9.1
pydantic_core==2.23.4
pydot==4.0.0
pydub==0.25.1
pyflakes==3.3.2
pygit2==1.18.0
Pygments==2.19.1
pygtrie==2.5.0
PyJWT==2.9.0
pymongo==4.13.0
PyMySQL==1.1.1
pyparsing==3.2.3
pyreadline3==3.5.4
PySocks==1.7.1
pytesseract==0.3.13
pytest==8.3.5
pytest-asyncio==1.0.0
pytest-cov==6.1.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-jose==3.5.0
python-Levenshtein==0.27.1
python-multipart==0.0.20
pytorch-lightning==2.5.1.post0
pytorch-metric-learning==2.8.1
pytz==2025.2
pywin32==310
PyYAML==6.0.2
qiskit==2.0.2
qiskit-aer==0.17.0
qiskit-algorithms==0.3.1
qiskit-optimization==0.6.1
RapidFuzz==3.13.0
ray==2.44.1
redis==5.3.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rich==14.0.0
roman-numerals-py==3.1.0
rpds-py==0.25.1
rsa==4.9.1
ruamel.yaml==0.18.12
ruamel.yaml.clib==0.2.12
rustworkx==0.16.0
s3transfer==0.13.0
safetensors==0.5.3
safety==3.3.0
safety-schemas==0.0.10
scikit-base==0.12.3
scikit-image==0.25.2
scikit-learn==1.6.1
scipy==1.15.3
scmrepo==3.3.11
seaborn==0.13.2
semver==3.0.4
sentence-transformers==4.1.0
sentencepiece==0.2.0
sentry-sdk==2.29.1
seqeval==1.2.2
setproctitle==1.3.6
setuptools==80.9.0
shap==0.47.2
shellingham==1.5.4
shortuuid==1.0.13
shtab==1.7.2
simsimd==6.2.1
six==1.17.0
sktime==0.37.0
slicer==0.0.8
slowapi==0.1.9
smart-open==7.1.0
smmap==5.0.2
sniffio==1.3.1
snowballstemmer==3.0.1
soundfile==0.13.1
soupsieve==2.7
soxr==0.5.0.post1
spacy==3.8.7
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SpeechRecognition==3.14.3
Sphinx==8.2.3
sphinx-rtd-theme==3.0.2
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jquery==4.1
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
SQLAlchemy==2.0.41
sqlparse==0.5.3
sqltrie==0.11.2
srsly==2.5.1
stable_baselines3==2.6.0
stanio==0.5.1
starlette==0.46.2
statsforecast==2.0.1
statsmodels==0.14.4
stevedore==5.4.1
stringzilla==3.12.5
structlog==25.3.0
symengine==0.13.0
sympy==1.13.1
tabulate==0.9.0
tensorboard==2.19.0
tensorboard-data-server==0.7.2
tensorboardX==2.6.2.2
tensorflow==2.19.0
tensorflow-probability==0.25.0
tensorrt==10.11.0.33
tensorrt_cu12==10.11.0.33
tensorrt_cu12_bindings==10.11.0.33
tensorrt_cu12_libs==10.11.0.33
termcolor==3.1.0
text-unidecode==1.3
texttable==1.7.0
thinc==8.3.6
threadpoolctl==3.6.0
tifffile==2025.5.26
timm==1.0.3
tokenizers==0.21.1
tomlkit==0.13.2
toolz==0.12.1
torch==2.6.0
torch-geometric==2.6.1
torchaudio==2.6.0
torchmetrics==1.7.2
torchvision==0.21.0
tornado==6.5.1
tqdm==4.67.1
transformers==4.49.0
triad==0.9.8
typer==0.16.0
typing-inspection==0.4.1
typing_extensions==4.13.2
tzdata==2025.2
urllib3==2.4.0
utilsforecast==0.2.10
uv==0.7.9
uvicorn==0.34.2
vine==5.1.0
virtualenv==20.31.2
voluptuous==0.15.2
waitress==3.0.2
wandb==0.19.11
wasabi==1.1.3
watchfiles==1.0.5
wcwidth==0.2.13
weasel==0.4.1
websockets==15.0.1
Werkzeug==3.1.3
wheel==0.45.1
window_ops==0.0.15
wrapt==1.17.2
xformers==0.0.29.post3
xgboost==3.0.2
xxhash==3.5.0
yarl==1.20.0
zc.lockfile==3.0.post1
zipp==3.22.0
zstandard==0.23.0
