"""
Pytest configuration and fixtures for the Enterprise AI/ML Platform backend tests.

This module provides common test fixtures and configuration for all tests.
"""

import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator, Generator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import <PERSON><PERSON><PERSON>ool

from app.main import app
from app.core.database import get_db, Base
from app.core.config import settings

# Test database URL
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

# Create test session
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Drop tables after test
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session) -> TestClient:
    """Create a test client with database dependency override."""
    
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture
def test_user_data():
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "name": "Test User",
        "role": "user"
    }


@pytest.fixture
def test_admin_data():
    """Sample admin user data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "adminpassword123",
        "name": "Admin User",
        "role": "admin"
    }


@pytest.fixture
def auth_headers(client: TestClient, test_user_data):
    """Create authentication headers for testing."""
    # Register user
    response = client.post("/api/v1/auth/register", json=test_user_data)
    assert response.status_code == 201
    
    # Login user
    login_data = {
        "email": test_user_data["email"],
        "password": test_user_data["password"]
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_headers(client: TestClient, test_admin_data):
    """Create admin authentication headers for testing."""
    # Register admin
    response = client.post("/api/v1/auth/register", json=test_admin_data)
    assert response.status_code == 201
    
    # Login admin
    login_data = {
        "email": test_admin_data["email"],
        "password": test_admin_data["password"]
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def sample_ml_data():
    """Sample ML training data for testing."""
    import pandas as pd
    import numpy as np
    
    np.random.seed(42)
    data = pd.DataFrame({
        'feature1': np.random.randn(100),
        'feature2': np.random.randn(100),
        'feature3': np.random.randn(100),
        'target': np.random.randint(0, 2, 100)
    })
    
    return data


@pytest.fixture
def sample_model_config():
    """Sample model configuration for testing."""
    return {
        "model_name": "test_model",
        "algorithm": "random_forest_classifier",
        "target_column": "target",
        "model_params": {
            "n_estimators": 10,
            "random_state": 42
        }
    }


@pytest.fixture
def mock_redis():
    """Mock Redis client for testing."""
    from unittest.mock import MagicMock
    
    mock_redis = MagicMock()
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = True
    mock_redis.exists.return_value = False
    
    return mock_redis


@pytest.fixture
def mock_celery():
    """Mock Celery for testing."""
    from unittest.mock import MagicMock
    
    mock_task = MagicMock()
    mock_task.delay.return_value.id = "test-task-id"
    mock_task.delay.return_value.status = "PENDING"
    
    return mock_task


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "auth: mark test as requiring authentication"
    )
    config.addinivalue_line(
        "markers", "ml: mark test as ML-related"
    )


# Async test configuration
@pytest_asyncio.fixture
async def async_client() -> AsyncGenerator[TestClient, None]:
    """Create an async test client."""
    async with TestClient(app) as client:
        yield client
