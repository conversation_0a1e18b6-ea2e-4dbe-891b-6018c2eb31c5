"""
Enterprise ML Service Tests
===========================

Comprehensive test suite for the Enterprise ML Service and all AI/ML modules.

Features:
- Unit tests for all modules
- Integration tests
- Performance benchmarks
- Error handling tests
- Mock data generation
- Test fixtures and utilities
"""

import pytest
import asyncio
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
import json
import tempfile
import os

# Import the services and modules to test
from app.services.enterprise_ml_service import EnterpriseMLService
from modules.computer_vision_enterprise import ComputerVisionModule
from modules.nlp_enterprise import NLPModule
from modules.time_series_enterprise import TimeSeriesModule
from modules.automl_enterprise import AutoMLModule
from modules.deep_learning_enterprise import DeepLearningModule
from modules.reinforcement_learning_enterprise import ReinforcementLearningModule
from modules.generative_ai_enterprise import GenerativeAIModule


class TestEnterpriseMLService:
    """Test suite for Enterprise ML Service."""
    
    @pytest.fixture
    async def ml_service(self):
        """Create ML service instance for testing."""
        service = EnterpriseMLService()
        await service.initialize()
        yield service
        await service.cleanup()
    
    @pytest.mark.asyncio
    async def test_service_initialization(self, ml_service):
        """Test ML service initialization."""
        assert ml_service.is_initialized
        assert len(ml_service.modules) > 0
        
        # Check that core modules are loaded
        expected_modules = [
            'computer_vision', 'nlp', 'time_series', 
            'automl', 'deep_learning'
        ]
        
        for module_name in expected_modules:
            assert module_name in ml_service.modules
    
    @pytest.mark.asyncio
    async def test_module_listing(self, ml_service):
        """Test module listing functionality."""
        modules_info = await ml_service.list_modules()
        
        assert isinstance(modules_info, dict)
        assert len(modules_info) > 0
        
        # Check module info structure
        for module_name, info in modules_info.items():
            assert 'name' in info
            assert 'available' in info
            assert 'capabilities' in info
    
    @pytest.mark.asyncio
    async def test_health_check(self, ml_service):
        """Test health check functionality."""
        health_status = await ml_service.health_check()
        
        assert isinstance(health_status, dict)
        assert 'service' in health_status
        assert 'status' in health_status
        assert 'modules' in health_status
        assert health_status['status'] in ['healthy', 'unhealthy', 'degraded']


class TestComputerVisionModule:
    """Test suite for Computer Vision Module."""
    
    @pytest.fixture
    async def cv_module(self):
        """Create CV module instance for testing."""
        module = ComputerVisionModule()
        await module.initialize()
        yield module
        await module.cleanup()
    
    def create_test_image(self, size=(224, 224, 3)):
        """Create test image data."""
        return np.random.randint(0, 255, size, dtype=np.uint8)
    
    @pytest.mark.asyncio
    async def test_module_initialization(self, cv_module):
        """Test CV module initialization."""
        assert cv_module.name == "Computer Vision"
        assert cv_module.version == "2.0.0"
        assert len(cv_module.capabilities) > 0
    
    @pytest.mark.asyncio
    async def test_image_classification(self, cv_module):
        """Test image classification functionality."""
        # Create test image
        test_image = self.create_test_image()
        
        parameters = {
            'image': test_image,
            'model': 'resnet50',
            'top_k': 5
        }
        
        result = await cv_module.execute_task('classify_image', parameters)
        
        assert result['success'] is True
        assert 'result' in result
        assert 'processing_time' in result
    
    @pytest.mark.asyncio
    async def test_object_detection(self, cv_module):
        """Test object detection functionality."""
        # Create test image
        test_image = self.create_test_image()
        
        parameters = {
            'image': test_image,
            'model': 'yolov8n',
            'confidence': 0.5
        }
        
        result = await cv_module.execute_task('detect_objects', parameters)
        
        # Should succeed even if no objects detected
        assert 'success' in result
        assert 'processing_time' in result
    
    @pytest.mark.asyncio
    async def test_invalid_task(self, cv_module):
        """Test handling of invalid tasks."""
        result = await cv_module.execute_task('invalid_task', {})
        
        assert result['success'] is False
        assert 'error' in result


class TestNLPModule:
    """Test suite for NLP Module."""
    
    @pytest.fixture
    async def nlp_module(self):
        """Create NLP module instance for testing."""
        module = NLPModule()
        await module.initialize()
        yield module
        await module.cleanup()
    
    @pytest.mark.asyncio
    async def test_module_initialization(self, nlp_module):
        """Test NLP module initialization."""
        assert nlp_module.name == "Natural Language Processing"
        assert nlp_module.version == "2.0.0"
        assert 'text_classification' in nlp_module.capabilities
    
    @pytest.mark.asyncio
    async def test_sentiment_analysis(self, nlp_module):
        """Test sentiment analysis functionality."""
        parameters = {
            'text': 'This is a great product! I love it.'
        }
        
        result = await nlp_module.execute_task('analyze_sentiment', parameters)
        
        assert 'success' in result
        assert 'processing_time' in result
    
    @pytest.mark.asyncio
    async def test_text_classification(self, nlp_module):
        """Test text classification functionality."""
        parameters = {
            'text': 'This is a sample text for classification.',
            'model': 'text_classification'
        }
        
        result = await nlp_module.execute_task('classify_text', parameters)
        
        assert 'success' in result
        assert 'processing_time' in result
    
    @pytest.mark.asyncio
    async def test_named_entity_recognition(self, nlp_module):
        """Test named entity recognition."""
        parameters = {
            'text': 'John Smith works at Microsoft in Seattle.'
        }
        
        result = await nlp_module.execute_task('extract_entities', parameters)
        
        assert 'success' in result
        assert 'processing_time' in result
    
    @pytest.mark.asyncio
    async def test_empty_text(self, nlp_module):
        """Test handling of empty text."""
        parameters = {'text': ''}
        
        result = await nlp_module.execute_task('analyze_sentiment', parameters)
        
        assert result['success'] is False
        assert 'error' in result


class TestTimeSeriesModule:
    """Test suite for Time Series Module."""
    
    @pytest.fixture
    async def ts_module(self):
        """Create time series module instance for testing."""
        module = TimeSeriesModule()
        await module.initialize()
        yield module
        await module.cleanup()
    
    def create_test_time_series(self, length=100):
        """Create test time series data."""
        # Generate synthetic time series with trend and noise
        trend = np.linspace(0, 10, length)
        noise = np.random.normal(0, 1, length)
        seasonal = np.sin(np.linspace(0, 4*np.pi, length))
        return trend + seasonal + noise
    
    @pytest.mark.asyncio
    async def test_module_initialization(self, ts_module):
        """Test time series module initialization."""
        assert ts_module.name == "Time Series Analysis"
        assert ts_module.version == "2.0.0"
        assert 'forecasting' in ts_module.capabilities
    
    @pytest.mark.asyncio
    async def test_forecasting(self, ts_module):
        """Test time series forecasting."""
        # Create test data
        test_data = self.create_test_time_series().tolist()
        
        parameters = {
            'data': test_data,
            'forecast_periods': 10,
            'method': 'linear_regression'  # Use simple method for testing
        }
        
        result = await ts_module.execute_task('forecast', parameters)
        
        assert 'success' in result
        assert 'processing_time' in result
    
    @pytest.mark.asyncio
    async def test_anomaly_detection(self, ts_module):
        """Test anomaly detection."""
        # Create test data with anomalies
        test_data = self.create_test_time_series()
        test_data[50] = 100  # Add anomaly
        
        parameters = {
            'data': test_data.tolist(),
            'method': 'isolation_forest',
            'contamination': 0.1
        }
        
        result = await ts_module.execute_task('detect_anomalies', parameters)
        
        assert 'success' in result
        assert 'processing_time' in result


class TestAutoMLModule:
    """Test suite for AutoML Module."""
    
    @pytest.fixture
    async def automl_module(self):
        """Create AutoML module instance for testing."""
        module = AutoMLModule()
        await module.initialize()
        yield module
        await module.cleanup()
    
    def create_test_dataset(self, n_samples=100, n_features=5, task='classification'):
        """Create test dataset."""
        from sklearn.datasets import make_classification, make_regression
        
        if task == 'classification':
            X, y = make_classification(
                n_samples=n_samples,
                n_features=n_features,
                n_classes=2,
                random_state=42
            )
        else:
            X, y = make_regression(
                n_samples=n_samples,
                n_features=n_features,
                random_state=42
            )
        
        return X.tolist(), y.tolist()
    
    @pytest.mark.asyncio
    async def test_module_initialization(self, automl_module):
        """Test AutoML module initialization."""
        assert automl_module.name == "Automated Machine Learning"
        assert automl_module.version == "2.0.0"
        assert 'model_selection' in automl_module.capabilities
    
    @pytest.mark.asyncio
    async def test_model_selection(self, automl_module):
        """Test automated model selection."""
        # Create test dataset
        X, y = self.create_test_dataset(n_samples=50)  # Small dataset for testing
        
        parameters = {
            'X': X,
            'y': y,
            'task_type': 'classification',
            'cv_folds': 3  # Reduced for faster testing
        }
        
        result = await automl_module.execute_task('select_model', parameters)
        
        assert 'success' in result
        assert 'processing_time' in result
    
    @pytest.mark.asyncio
    async def test_hyperparameter_optimization(self, automl_module):
        """Test hyperparameter optimization."""
        # Create test dataset
        X, y = self.create_test_dataset(n_samples=50)
        
        parameters = {
            'X': X,
            'y': y,
            'model_name': 'random_forest',
            'method': 'random_search',
            'n_trials': 5  # Reduced for faster testing
        }
        
        result = await automl_module.execute_task('optimize_hyperparameters', parameters)
        
        assert 'success' in result
        assert 'processing_time' in result


class TestDeepLearningModule:
    """Test suite for Deep Learning Module."""
    
    @pytest.fixture
    async def dl_module(self):
        """Create deep learning module instance for testing."""
        module = DeepLearningModule()
        await module.initialize()
        yield module
        await module.cleanup()
    
    @pytest.mark.asyncio
    async def test_module_initialization(self, dl_module):
        """Test deep learning module initialization."""
        assert dl_module.name == "Deep Learning"
        assert dl_module.version == "2.0.0"
        assert 'custom_networks' in dl_module.capabilities
    
    @pytest.mark.asyncio
    async def test_custom_network_creation(self, dl_module):
        """Test custom network creation."""
        parameters = {
            'network_type': 'mlp',
            'architecture_config': {
                'input_size': 10,
                'hidden_sizes': [64, 32],
                'output_size': 2,
                'task_type': 'classification'
            }
        }
        
        result = await dl_module.execute_task('create_custom_network', parameters)
        
        assert 'success' in result
        assert 'processing_time' in result


# Performance benchmarks
class TestPerformanceBenchmarks:
    """Performance benchmark tests."""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """Test handling of concurrent requests."""
        service = EnterpriseMLService()
        await service.initialize()
        
        try:
            # Create multiple concurrent tasks
            tasks = []
            for i in range(5):
                task = service.execute_task(
                    'computer_vision',
                    'classify_image',
                    {'image': np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)}
                )
                tasks.append(task)
            
            # Execute concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check that all tasks completed
            assert len(results) == 5
            
            # Check for any exceptions
            exceptions = [r for r in results if isinstance(r, Exception)]
            assert len(exceptions) == 0, f"Found exceptions: {exceptions}"
            
        finally:
            await service.cleanup()


# Test utilities
class TestUtilities:
    """Utility functions for testing."""
    
    @staticmethod
    def create_mock_image(width=224, height=224, channels=3):
        """Create mock image data."""
        return np.random.randint(0, 255, (height, width, channels), dtype=np.uint8)
    
    @staticmethod
    def create_mock_text_data():
        """Create mock text data."""
        return [
            "This is a positive review. Great product!",
            "Terrible experience. Would not recommend.",
            "Average product. Nothing special.",
            "Excellent quality and fast delivery.",
            "Poor customer service and low quality."
        ]
    
    @staticmethod
    def create_mock_time_series(length=100, trend=True, seasonal=True, noise=True):
        """Create mock time series data."""
        x = np.linspace(0, 10, length)
        data = np.zeros(length)
        
        if trend:
            data += x
        
        if seasonal:
            data += np.sin(2 * np.pi * x)
        
        if noise:
            data += np.random.normal(0, 0.1, length)
        
        return data.tolist()


# Fixtures for common test data
@pytest.fixture
def sample_image():
    """Sample image for testing."""
    return TestUtilities.create_mock_image()

@pytest.fixture
def sample_text():
    """Sample text for testing."""
    return "This is a sample text for testing NLP functionality."

@pytest.fixture
def sample_time_series():
    """Sample time series for testing."""
    return TestUtilities.create_mock_time_series()

@pytest.fixture
def sample_dataset():
    """Sample dataset for testing."""
    from sklearn.datasets import make_classification
    X, y = make_classification(n_samples=100, n_features=10, random_state=42)
    return X.tolist(), y.tolist()


# Configuration for pytest
def pytest_configure(config):
    """Configure pytest."""
    config.addinivalue_line(
        "markers", "asyncio: mark test as async"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
