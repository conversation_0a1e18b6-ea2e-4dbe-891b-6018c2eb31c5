# NeuroFlowAI Platform - Complete Kubernetes Deployment
# =====================================================
# Production-ready deployment with all components

apiVersion: v1
kind: Namespace
metadata:
  name: neuroflow-ai
  labels:
    name: neuroflow-ai
    tier: production

---
# ConfigMap for platform configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: neuroflow-config
  namespace: neuroflow-ai
data:
  # Database configuration
  POSTGRES_HOST: "postgres-service"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "neuroflow"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  
  # API configuration
  API_HOST: "0.0.0.0"
  API_PORT: "8000"
  FRONTEND_URL: "https://app.neuroflow.ai"
  
  # Agent configuration
  AGENT_ORCHESTRATOR_ENABLED: "true"
  MAX_CONCURRENT_WORKFLOWS: "100"
  
  # GPU configuration
  GPU_ORCHESTRATOR_ENABLED: "true"
  RAY_CLUSTER_ENABLED: "true"
  
  # Monitoring configuration
  PROMETHEUS_ENABLED: "true"
  GRAFANA_ENABLED: "true"
  JAEGER_ENABLED: "true"

---
# Secret for sensitive configuration
apiVersion: v1
kind: Secret
metadata:
  name: neuroflow-secrets
  namespace: neuroflow-ai
type: Opaque
data:
  # Base64 encoded secrets
  POSTGRES_PASSWORD: bmV1cm9mbG93MTIz  # neuroflow123
  REDIS_PASSWORD: cmVkaXNwYXNzd29yZA==  # redispassword
  JWT_SECRET: c3VwZXJzZWNyZXRqd3RrZXk=  # supersecretjwtkey
  OPENAI_API_KEY: ""  # Add your OpenAI API key
  HUGGINGFACE_TOKEN: ""  # Add your HuggingFace token

---
# PostgreSQL Database
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: neuroflow-ai
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: neuroflow-config
              key: POSTGRES_DB
        - name: POSTGRES_USER
          value: "neuroflow"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: neuroflow-secrets
              key: POSTGRES_PASSWORD
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: neuroflow-ai
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: neuroflow-ai
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi

---
# Redis Cache
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: neuroflow-ai
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: neuroflow-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: neuroflow-ai
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379

---
# NeuroFlowAI Backend API
apiVersion: apps/v1
kind: Deployment
metadata:
  name: neuroflow-backend
  namespace: neuroflow-ai
spec:
  replicas: 3
  selector:
    matchLabels:
      app: neuroflow-backend
  template:
    metadata:
      labels:
        app: neuroflow-backend
    spec:
      containers:
      - name: backend
        image: neuroflow/backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: neuroflow-config
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: neuroflow-config
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: neuroflow-config
              key: POSTGRES_DB
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: neuroflow-secrets
              key: POSTGRES_PASSWORD
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: neuroflow-config
              key: REDIS_HOST
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: neuroflow-secrets
              key: JWT_SECRET
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"

---
apiVersion: v1
kind: Service
metadata:
  name: neuroflow-backend-service
  namespace: neuroflow-ai
spec:
  selector:
    app: neuroflow-backend
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP

---
# NeuroFlowAI Frontend
apiVersion: apps/v1
kind: Deployment
metadata:
  name: neuroflow-frontend
  namespace: neuroflow-ai
spec:
  replicas: 2
  selector:
    matchLabels:
      app: neuroflow-frontend
  template:
    metadata:
      labels:
        app: neuroflow-frontend
    spec:
      containers:
      - name: frontend
        image: neuroflow/frontend:latest
        ports:
        - containerPort: 3000
        env:
        - name: REACT_APP_API_URL
          value: "https://api.neuroflow.ai"
        - name: REACT_APP_WS_URL
          value: "wss://api.neuroflow.ai/ws"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"

---
apiVersion: v1
kind: Service
metadata:
  name: neuroflow-frontend-service
  namespace: neuroflow-ai
spec:
  selector:
    app: neuroflow-frontend
  ports:
  - port: 3000
    targetPort: 3000
  type: ClusterIP

---
# Agent Orchestrator
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-orchestrator
  namespace: neuroflow-ai
spec:
  replicas: 2
  selector:
    matchLabels:
      app: agent-orchestrator
  template:
    metadata:
      labels:
        app: agent-orchestrator
    spec:
      containers:
      - name: orchestrator
        image: neuroflow/agent-orchestrator:latest
        env:
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: neuroflow-config
              key: REDIS_HOST
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: neuroflow-config
              key: POSTGRES_HOST
        - name: MAX_CONCURRENT_WORKFLOWS
          valueFrom:
            configMapKeyRef:
              name: neuroflow-config
              key: MAX_CONCURRENT_WORKFLOWS
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"

---
# GPU Orchestrator
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gpu-orchestrator
  namespace: neuroflow-ai
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gpu-orchestrator
  template:
    metadata:
      labels:
        app: gpu-orchestrator
    spec:
      serviceAccountName: gpu-orchestrator-sa
      containers:
      - name: gpu-orchestrator
        image: neuroflow/gpu-orchestrator:latest
        env:
        - name: KUBERNETES_NAMESPACE
          value: "neuroflow-gpu"
        - name: RAY_CLUSTER_ENABLED
          valueFrom:
            configMapKeyRef:
              name: neuroflow-config
              key: RAY_CLUSTER_ENABLED
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"

---
# Service Account for GPU Orchestrator
apiVersion: v1
kind: ServiceAccount
metadata:
  name: gpu-orchestrator-sa
  namespace: neuroflow-ai

---
# ClusterRole for GPU Orchestrator
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: gpu-orchestrator-role
rules:
- apiGroups: [""]
  resources: ["nodes", "pods", "services", "persistentvolumeclaims"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["ray.io"]
  resources: ["rayclusters", "rayservices", "rayjobs"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
# ClusterRoleBinding for GPU Orchestrator
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: gpu-orchestrator-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: gpu-orchestrator-role
subjects:
- kind: ServiceAccount
  name: gpu-orchestrator-sa
  namespace: neuroflow-ai

---
# Ingress for external access
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: neuroflow-ingress
  namespace: neuroflow-ai
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
spec:
  tls:
  - hosts:
    - app.neuroflow.ai
    - api.neuroflow.ai
    secretName: neuroflow-tls
  rules:
  - host: app.neuroflow.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: neuroflow-frontend-service
            port:
              number: 3000
  - host: api.neuroflow.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: neuroflow-backend-service
            port:
              number: 8000
