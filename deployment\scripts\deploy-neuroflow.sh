#!/bin/bash

# NeuroFlowAI Platform Deployment Script
# =====================================
# Complete production deployment for Kubernetes

set -e

# Configuration
NAMESPACE="neuroflow-ai"
MONITORING_NAMESPACE="neuroflow-monitoring"
GPU_NAMESPACE="neuroflow-gpu"
DOMAIN="neuroflow.ai"
ENVIRONMENT=${ENVIRONMENT:-"production"}
CLUSTER_NAME=${CLUSTER_NAME:-"neuroflow-cluster"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    # Check if helm is installed
    if ! command -v helm &> /dev/null; then
        log_error "helm is not installed. Please install helm first."
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "docker is not installed. Please install docker first."
        exit 1
    fi
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Setup namespaces
setup_namespaces() {
    log_info "Setting up namespaces..."
    
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace $MONITORING_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace $GPU_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # Label namespaces
    kubectl label namespace $NAMESPACE environment=$ENVIRONMENT --overwrite
    kubectl label namespace $MONITORING_NAMESPACE environment=$ENVIRONMENT --overwrite
    kubectl label namespace $GPU_NAMESPACE environment=$ENVIRONMENT --overwrite
    
    log_success "Namespaces created successfully"
}

# Install NVIDIA GPU Operator
install_gpu_operator() {
    log_info "Installing NVIDIA GPU Operator..."
    
    # Add NVIDIA Helm repository
    helm repo add nvidia https://helm.ngc.nvidia.com/nvidia
    helm repo update
    
    # Install GPU Operator
    helm upgrade --install gpu-operator nvidia/gpu-operator \
        --namespace gpu-operator-resources \
        --create-namespace \
        --set driver.enabled=true \
        --set toolkit.enabled=true \
        --set devicePlugin.enabled=true \
        --set dcgmExporter.enabled=true \
        --set gfd.enabled=true \
        --set migManager.enabled=false \
        --set nodeStatusExporter.enabled=true \
        --wait
    
    log_success "NVIDIA GPU Operator installed"
}

# Install Ray Operator
install_ray_operator() {
    log_info "Installing Ray Operator..."
    
    # Install Ray CRDs
    kubectl apply -f https://raw.githubusercontent.com/ray-project/kuberay/master/ray-operator/config/crd/bases/ray.io_rayclusters.yaml
    kubectl apply -f https://raw.githubusercontent.com/ray-project/kuberay/master/ray-operator/config/crd/bases/ray.io_rayservices.yaml
    kubectl apply -f https://raw.githubusercontent.com/ray-project/kuberay/master/ray-operator/config/crd/bases/ray.io_rayjobs.yaml
    
    # Install Ray Operator
    helm repo add kuberay https://ray-project.github.io/kuberay-helm/
    helm repo update
    
    helm upgrade --install ray-operator kuberay/ray-operator \
        --namespace ray-system \
        --create-namespace \
        --wait
    
    log_success "Ray Operator installed"
}

# Install cert-manager for TLS
install_cert_manager() {
    log_info "Installing cert-manager..."
    
    helm repo add jetstack https://charts.jetstack.io
    helm repo update
    
    helm upgrade --install cert-manager jetstack/cert-manager \
        --namespace cert-manager \
        --create-namespace \
        --set installCRDs=true \
        --wait
    
    # Create ClusterIssuer for Let's Encrypt
    cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: admin@${DOMAIN}
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
    
    log_success "cert-manager installed"
}

# Install NGINX Ingress Controller
install_nginx_ingress() {
    log_info "Installing NGINX Ingress Controller..."
    
    helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
    helm repo update
    
    helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
        --namespace ingress-nginx \
        --create-namespace \
        --set controller.service.type=LoadBalancer \
        --set controller.metrics.enabled=true \
        --set controller.podAnnotations."prometheus\.io/scrape"="true" \
        --set controller.podAnnotations."prometheus\.io/port"="10254" \
        --wait
    
    log_success "NGINX Ingress Controller installed"
}

# Build and push Docker images
build_and_push_images() {
    log_info "Building and pushing Docker images..."
    
    # Set image registry
    REGISTRY=${REGISTRY:-"neuroflow"}
    
    # Build backend image
    log_info "Building backend image..."
    docker build -t ${REGISTRY}/backend:latest -f backend/Dockerfile backend/
    docker push ${REGISTRY}/backend:latest
    
    # Build frontend image
    log_info "Building frontend image..."
    docker build -t ${REGISTRY}/frontend:latest -f frontend/Dockerfile frontend/
    docker push ${REGISTRY}/frontend:latest
    
    # Build agent orchestrator image
    log_info "Building agent orchestrator image..."
    docker build -t ${REGISTRY}/agent-orchestrator:latest -f backend/Dockerfile.orchestrator backend/
    docker push ${REGISTRY}/agent-orchestrator:latest
    
    # Build GPU orchestrator image
    log_info "Building GPU orchestrator image..."
    docker build -t ${REGISTRY}/gpu-orchestrator:latest -f backend/Dockerfile.gpu backend/
    docker push ${REGISTRY}/gpu-orchestrator:latest
    
    log_success "Docker images built and pushed"
}

# Deploy NeuroFlowAI platform
deploy_platform() {
    log_info "Deploying NeuroFlowAI platform..."
    
    # Apply platform configuration
    kubectl apply -f deployment/kubernetes/neuroflow-platform.yaml
    
    # Wait for deployments to be ready
    log_info "Waiting for deployments to be ready..."
    kubectl wait --for=condition=available --timeout=600s deployment/postgres -n $NAMESPACE
    kubectl wait --for=condition=available --timeout=600s deployment/redis -n $NAMESPACE
    kubectl wait --for=condition=available --timeout=600s deployment/neuroflow-backend -n $NAMESPACE
    kubectl wait --for=condition=available --timeout=600s deployment/neuroflow-frontend -n $NAMESPACE
    kubectl wait --for=condition=available --timeout=600s deployment/agent-orchestrator -n $NAMESPACE
    kubectl wait --for=condition=available --timeout=600s deployment/gpu-orchestrator -n $NAMESPACE
    
    log_success "NeuroFlowAI platform deployed"
}

# Deploy monitoring stack
deploy_monitoring() {
    log_info "Deploying monitoring stack..."
    
    # Apply monitoring configuration
    kubectl apply -f deployment/kubernetes/monitoring-stack.yaml
    
    # Wait for monitoring deployments
    log_info "Waiting for monitoring deployments to be ready..."
    kubectl wait --for=condition=available --timeout=600s deployment/prometheus -n $MONITORING_NAMESPACE
    kubectl wait --for=condition=available --timeout=600s deployment/grafana -n $MONITORING_NAMESPACE
    kubectl wait --for=condition=available --timeout=600s deployment/jaeger -n $MONITORING_NAMESPACE
    kubectl wait --for=condition=available --timeout=600s deployment/alertmanager -n $MONITORING_NAMESPACE
    
    log_success "Monitoring stack deployed"
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    # Wait for PostgreSQL to be ready
    kubectl wait --for=condition=ready pod -l app=postgres -n $NAMESPACE --timeout=300s
    
    # Run database migrations
    kubectl exec -n $NAMESPACE deployment/neuroflow-backend -- python -m alembic upgrade head
    
    # Create initial admin user
    kubectl exec -n $NAMESPACE deployment/neuroflow-backend -- python -c "
from app.core.auth import create_user
from app.core.database import get_db
db = next(get_db())
create_user(db, '<EMAIL>', 'admin123', is_admin=True)
print('Admin user created: <EMAIL> / admin123')
"
    
    log_success "Database setup completed"
}

# Configure DNS and SSL
configure_dns_ssl() {
    log_info "Configuring DNS and SSL..."
    
    # Get LoadBalancer IP
    EXTERNAL_IP=""
    while [ -z $EXTERNAL_IP ]; do
        log_info "Waiting for external IP..."
        EXTERNAL_IP=$(kubectl get svc ingress-nginx-controller -n ingress-nginx --template="{{range .status.loadBalancer.ingress}}{{.ip}}{{end}}")
        [ -z "$EXTERNAL_IP" ] && sleep 10
    done
    
    log_info "External IP: $EXTERNAL_IP"
    log_warning "Please configure your DNS to point the following domains to $EXTERNAL_IP:"
    log_warning "  - app.$DOMAIN"
    log_warning "  - api.$DOMAIN"
    log_warning "  - monitoring.$DOMAIN"
    
    # Wait for user confirmation
    read -p "Press enter after configuring DNS..."
    
    log_success "DNS configuration noted"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check all pods are running
    log_info "Checking pod status..."
    kubectl get pods -n $NAMESPACE
    kubectl get pods -n $MONITORING_NAMESPACE
    
    # Check services
    log_info "Checking services..."
    kubectl get svc -n $NAMESPACE
    kubectl get svc -n $MONITORING_NAMESPACE
    
    # Check ingress
    log_info "Checking ingress..."
    kubectl get ingress -n $NAMESPACE
    
    # Test API endpoint
    log_info "Testing API endpoint..."
    if curl -f -s https://api.$DOMAIN/health > /dev/null; then
        log_success "API endpoint is responding"
    else
        log_warning "API endpoint is not responding yet"
    fi
    
    log_success "Deployment verification completed"
}

# Print access information
print_access_info() {
    log_success "NeuroFlowAI Platform Deployment Complete!"
    echo ""
    echo "Access URLs:"
    echo "  Frontend:   https://app.$DOMAIN"
    echo "  API:        https://api.$DOMAIN"
    echo "  Grafana:    https://monitoring.$DOMAIN"
    echo ""
    echo "Default Credentials:"
    echo "  Admin:      <EMAIL> / admin123"
    echo "  Grafana:    admin / neuroflow123"
    echo ""
    echo "Useful Commands:"
    echo "  kubectl get pods -n $NAMESPACE"
    echo "  kubectl logs -f deployment/neuroflow-backend -n $NAMESPACE"
    echo "  kubectl port-forward svc/grafana-service 3000:3000 -n $MONITORING_NAMESPACE"
    echo ""
}

# Main deployment function
main() {
    log_info "Starting NeuroFlowAI Platform Deployment"
    log_info "Environment: $ENVIRONMENT"
    log_info "Cluster: $CLUSTER_NAME"
    log_info "Domain: $DOMAIN"
    echo ""
    
    check_prerequisites
    setup_namespaces
    install_gpu_operator
    install_ray_operator
    install_cert_manager
    install_nginx_ingress
    build_and_push_images
    deploy_platform
    deploy_monitoring
    setup_database
    configure_dns_ssl
    verify_deployment
    print_access_info
    
    log_success "Deployment completed successfully!"
}

# Handle script arguments
case "${1:-}" in
    "prerequisites")
        check_prerequisites
        ;;
    "namespaces")
        setup_namespaces
        ;;
    "gpu")
        install_gpu_operator
        ;;
    "ray")
        install_ray_operator
        ;;
    "cert-manager")
        install_cert_manager
        ;;
    "ingress")
        install_nginx_ingress
        ;;
    "images")
        build_and_push_images
        ;;
    "platform")
        deploy_platform
        ;;
    "monitoring")
        deploy_monitoring
        ;;
    "database")
        setup_database
        ;;
    "verify")
        verify_deployment
        ;;
    "")
        main
        ;;
    *)
        echo "Usage: $0 [prerequisites|namespaces|gpu|ray|cert-manager|ingress|images|platform|monitoring|database|verify]"
        echo "Run without arguments to deploy everything"
        exit 1
        ;;
esac
