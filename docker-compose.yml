# Enterprise AI/ML Platform - Docker Compose
# Production-ready multi-service architecture

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: aiml_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-aiml_platform}
      POSTGRES_USER: ${POSTGRES_USER:-aiml_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - aiml_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-aiml_user}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: aiml_mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USER:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-secure_password}
      MONGO_INITDB_DATABASE: ${MONGODB_DB_NAME:-aiml_platform}
    volumes:
      - mongodb_data:/data/db
      - ./database/mongo-init:/docker-entrypoint-initdb.d
    ports:
      - "27017:27017"
    networks:
      - aiml_network
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: aiml_redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-secure_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - aiml_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: aiml_backend
    environment:
      - DEBUG=false
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER:-aiml_user}:${POSTGRES_PASSWORD:-secure_password}@postgres:5432/${POSTGRES_DB:-aiml_platform}
      - MONGODB_URL=mongodb://${MONGO_ROOT_USER:-admin}:${MONGO_ROOT_PASSWORD:-secure_password}@mongodb:27017
      - REDIS_URL=redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-change-in-production}
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/2
    volumes:
      - ./backend/app:/app
      - ml_models:/app/models
      - ml_data:/app/data
    ports:
      - "8000:8000"
    networks:
      - aiml_network
    depends_on:
      postgres:
        condition: service_healthy
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: aiml_celery_worker
    command: celery -A app.tasks.celery_app worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER:-aiml_user}:${POSTGRES_PASSWORD:-secure_password}@postgres:5432/${POSTGRES_DB:-aiml_platform}
      - MONGODB_URL=mongodb://${MONGO_ROOT_USER:-admin}:${MONGO_ROOT_PASSWORD:-secure_password}@mongodb:27017
      - REDIS_URL=redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/2
    volumes:
      - ./backend/app:/app
      - ml_models:/app/models
      - ml_data:/app/data
    networks:
      - aiml_network
    depends_on:
      - backend
      - redis
    restart: unless-stopped

  # Celery Beat Scheduler
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: aiml_celery_beat
    command: celery -A app.tasks.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER:-aiml_user}:${POSTGRES_PASSWORD:-secure_password}@postgres:5432/${POSTGRES_DB:-aiml_platform}
      - MONGODB_URL=mongodb://${MONGO_ROOT_USER:-admin}:${MONGO_ROOT_PASSWORD:-secure_password}@mongodb:27017
      - REDIS_URL=redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/2
    volumes:
      - ./backend/app:/app
    networks:
      - aiml_network
    depends_on:
      - backend
      - redis
    restart: unless-stopped

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: aiml_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api/v1
      - REACT_APP_WS_URL=ws://localhost:8000/ws
    ports:
      - "3000:80"
    networks:
      - aiml_network
    depends_on:
      - backend
    restart: unless-stopped

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: aiml_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - aiml_network
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: aiml_prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - aiml_network
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: aiml_grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    networks:
      - aiml_network
    depends_on:
      - prometheus
    restart: unless-stopped

  # Elasticsearch for Logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: aiml_elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - aiml_network
    restart: unless-stopped

  # Kibana for Log Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: aiml_kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - aiml_network
    depends_on:
      - elasticsearch
    restart: unless-stopped

  # Logstash for Log Processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: aiml_logstash
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    networks:
      - aiml_network
    depends_on:
      - elasticsearch
    restart: unless-stopped

  # MinIO for Object Storage
  minio:
    image: minio/minio:latest
    container_name: aiml_minio
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin123}
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - aiml_network
    restart: unless-stopped

  # Jupyter Lab for Data Science
  jupyter:
    build:
      context: ./jupyter
      dockerfile: Dockerfile
    container_name: aiml_jupyter
    environment:
      - JUPYTER_TOKEN=${JUPYTER_TOKEN:-secure_token}
    volumes:
      - ./notebooks:/home/<USER>/work
      - ml_data:/home/<USER>/data
      - ml_models:/home/<USER>/models
    ports:
      - "8888:8888"
    networks:
      - aiml_network
    restart: unless-stopped

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
  ml_models:
  ml_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:
  minio_data:

networks:
  aiml_network:
    driver: bridge
