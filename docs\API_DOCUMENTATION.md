# 🚀 Enterprise AI/ML Platform - Complete API Documentation

## 📋 Table of Contents

1. [Authentication](#authentication)
2. [API Versioning](#api-versioning)
3. [Rate Limiting](#rate-limiting)
4. [Error Handling](#error-handling)
5. [Models API v2](#models-api-v2)
6. [Computer Vision API](#computer-vision-api)
7. [NLP API](#nlp-api)
8. [AutoML API](#automl-api)
9. [Data Management API](#data-management-api)
10. [Monitoring & Analytics](#monitoring--analytics)
11. [Code Examples](#code-examples)

## 🔐 Authentication

### JWT Token Authentication

All API endpoints require authentication using JWT tokens.

```bash
# Login to get access token
curl -X POST "https://api.aiml-platform.com/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "secure_password"
  }'
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

### Using the Token

Include the token in the Authorization header:

```bash
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  "https://api.aiml-platform.com/api/v2/models/"
```

## 🔄 API Versioning

The platform supports multiple API versions:

- **v1**: Legacy API (deprecated)
- **v2**: Current stable API with enhanced features
- **v3**: Beta API with experimental features

**Base URLs:**
- v1: `https://api.aiml-platform.com/api/v1/`
- v2: `https://api.aiml-platform.com/api/v2/`
- v3: `https://api.aiml-platform.com/api/v3/`

## ⚡ Rate Limiting

Rate limits are enforced per user and endpoint type:

| Endpoint Type | Limit | Window |
|---------------|-------|--------|
| Authentication | 10 requests | 5 minutes |
| API Calls | 100 requests | 1 minute |
| ML Inference | 50 requests | 1 minute |
| File Upload | 10 requests | 1 hour |
| Default | 1000 requests | 1 hour |

**Rate Limit Headers:**
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## 🚨 Error Handling

### Standard Error Response

```json
{
  "error": "validation_error",
  "message": "Invalid input parameters",
  "details": {
    "field": "model_name",
    "issue": "Model name must be between 1 and 100 characters"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

### HTTP Status Codes

| Code | Meaning |
|------|---------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 429 | Rate Limited |
| 500 | Internal Server Error |

## 🤖 Models API v2

### List Models

```bash
GET /api/v2/models/
```

**Parameters:**
- `page` (int): Page number (default: 1)
- `page_size` (int): Items per page (default: 20, max: 100)
- `model_type` (string): Filter by model type
- `framework` (string): Filter by framework
- `status` (string): Filter by status
- `search` (string): Search in name/description
- `sort_by` (string): Sort field (default: created_at)
- `sort_order` (string): asc/desc (default: desc)

**Example:**
```bash
curl -H "Authorization: Bearer TOKEN" \
  "https://api.aiml-platform.com/api/v2/models/?model_type=classification&page=1&page_size=10"
```

**Response:**
```json
{
  "models": [
    {
      "id": "model_123",
      "name": "Image Classifier v2",
      "version": "2.1.0",
      "type": "classification",
      "framework": "pytorch",
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T12:00:00Z",
      "size_mb": 245.7,
      "accuracy": 0.94,
      "performance_metrics": {
        "precision": 0.93,
        "recall": 0.95,
        "f1_score": 0.94
      },
      "tags": ["computer_vision", "production"],
      "description": "High-accuracy image classification model",
      "author": "data_scientist_1",
      "status": "active"
    }
  ],
  "total_count": 1,
  "page": 1,
  "page_size": 10,
  "has_next": false,
  "has_previous": false,
  "filters_applied": {
    "model_type": "classification"
  }
}
```

### Get Model Details

```bash
GET /api/v2/models/{model_id}
```

**Parameters:**
- `include_history` (bool): Include performance history (default: true)
- `include_usage` (bool): Include usage statistics (default: true)

**Example:**
```bash
curl -H "Authorization: Bearer TOKEN" \
  "https://api.aiml-platform.com/api/v2/models/model_123?include_history=true"
```

### Train New Model

```bash
POST /api/v2/models/train
```

**Request Body:**
```json
{
  "name": "Custom Image Classifier",
  "type": "classification",
  "framework": "autogluon",
  "dataset_id": "dataset_456",
  "hyperparameters": {
    "learning_rate": 0.001,
    "batch_size": 32,
    "epochs": 100
  },
  "training_config": {
    "early_stopping": true,
    "validation_split": 0.2,
    "augmentation": true
  },
  "tags": ["custom", "experimental"],
  "description": "Custom model for specific use case",
  "auto_deploy": false
}
```

**Response:**
```json
{
  "task_id": "task_789",
  "status": "queued",
  "message": "Model training queued successfully",
  "estimated_duration": "45 minutes",
  "priority": "normal",
  "auto_deploy": false
}
```

### Deploy Model

```bash
POST /api/v2/models/{model_id}/deploy
```

**Request Body:**
```json
{
  "deployment_config": {
    "instance_type": "gpu.medium",
    "min_instances": 1,
    "max_instances": 10,
    "cpu_limit": "2000m",
    "memory_limit": "4Gi"
  },
  "environment": "production",
  "auto_scale": true
}
```

## 👁️ Computer Vision API

### Image Classification

```bash
POST /api/v2/cv/classify
```

**Request (multipart/form-data):**
```bash
curl -X POST \
  -H "Authorization: Bearer TOKEN" \
  -F "image=@/path/to/image.jpg" \
  -F "model_name=resnet50" \
  -F "top_k=5" \
  "https://api.aiml-platform.com/api/v2/cv/classify"
```

**Response:**
```json
{
  "results": [
    {
      "class_id": 281,
      "class_name": "tabby_cat",
      "confidence": 0.94,
      "metadata": {
        "model_name": "resnet50",
        "inference_time": 0.045
      }
    },
    {
      "class_id": 285,
      "class_name": "egyptian_cat",
      "confidence": 0.03,
      "metadata": {
        "model_name": "resnet50",
        "inference_time": 0.045
      }
    }
  ],
  "processing_time": 0.067,
  "image_size": [224, 224]
}
```

### Object Detection

```bash
POST /api/v2/cv/detect
```

**Request:**
```bash
curl -X POST \
  -H "Authorization: Bearer TOKEN" \
  -F "image=@/path/to/image.jpg" \
  -F "model_name=yolov8n" \
  -F "confidence_threshold=0.5" \
  "https://api.aiml-platform.com/api/v2/cv/detect"
```

**Response:**
```json
{
  "detections": [
    {
      "bbox": {
        "x1": 100.5,
        "y1": 150.2,
        "x2": 300.8,
        "y2": 400.1,
        "confidence": 0.89,
        "class_id": 0,
        "class_name": "person"
      },
      "metadata": {
        "model_name": "yolov8n",
        "inference_time": 0.023
      }
    }
  ],
  "total_detections": 1,
  "processing_time": 0.034
}
```

## 🗣️ NLP API

### Text Classification

```bash
POST /api/v2/nlp/classify
```

**Request:**
```json
{
  "text": "This product is amazing! I love it so much.",
  "model_name": "sentiment_classifier",
  "return_probabilities": true
}
```

**Response:**
```json
{
  "classification": {
    "label": "positive",
    "confidence": 0.92,
    "probabilities": {
      "positive": 0.92,
      "negative": 0.05,
      "neutral": 0.03
    }
  },
  "processing_time": 0.012
}
```

### Named Entity Recognition

```bash
POST /api/v2/nlp/ner
```

**Request:**
```json
{
  "text": "John Smith works at Google in Mountain View, California.",
  "model_name": "ner_model"
}
```

**Response:**
```json
{
  "entities": [
    {
      "text": "John Smith",
      "label": "PERSON",
      "start": 0,
      "end": 10,
      "confidence": 0.99
    },
    {
      "text": "Google",
      "label": "ORG",
      "start": 20,
      "end": 26,
      "confidence": 0.95
    },
    {
      "text": "Mountain View, California",
      "label": "GPE",
      "start": 30,
      "end": 55,
      "confidence": 0.88
    }
  ],
  "processing_time": 0.018
}
```

## 🔮 AutoML API

### Start AutoML Training

```bash
POST /api/v2/automl/train
```

**Request:**
```json
{
  "dataset_id": "dataset_123",
  "problem_type": "classification",
  "target_column": "label",
  "time_limit": 3600,
  "quality_preset": "high_quality",
  "eval_metric": "accuracy",
  "training_config": {
    "feature_engineering": true,
    "ensemble": true,
    "hyperparameter_tuning": true
  }
}
```

**Response:**
```json
{
  "automl_job_id": "automl_456",
  "status": "running",
  "estimated_completion": "2024-01-15T14:30:00Z",
  "progress": {
    "current_step": "feature_engineering",
    "completion_percentage": 15
  }
}
```

### Get AutoML Results

```bash
GET /api/v2/automl/{job_id}/results
```

**Response:**
```json
{
  "job_id": "automl_456",
  "status": "completed",
  "best_model": {
    "model_id": "automl_model_789",
    "algorithm": "LightGBM",
    "score": 0.94,
    "metrics": {
      "accuracy": 0.94,
      "precision": 0.93,
      "recall": 0.95,
      "f1_score": 0.94
    }
  },
  "leaderboard": [
    {
      "rank": 1,
      "algorithm": "LightGBM",
      "score": 0.94,
      "training_time": 245.6
    },
    {
      "rank": 2,
      "algorithm": "RandomForest",
      "score": 0.91,
      "training_time": 123.4
    }
  ],
  "feature_importance": [
    {
      "feature": "feature_1",
      "importance": 0.35
    },
    {
      "feature": "feature_2",
      "importance": 0.28
    }
  ]
}
```

## 📊 Data Management API

### Upload Dataset

```bash
POST /api/v2/data/upload
```

**Request (multipart/form-data):**
```bash
curl -X POST \
  -H "Authorization: Bearer TOKEN" \
  -F "file=@/path/to/dataset.csv" \
  -F "name=Customer Data" \
  -F "description=Customer behavior dataset" \
  -F "tags=customer,behavior,analytics" \
  "https://api.aiml-platform.com/api/v2/data/upload"
```

**Response:**
```json
{
  "dataset_id": "dataset_123",
  "name": "Customer Data",
  "size_mb": 45.7,
  "rows": 10000,
  "columns": 25,
  "status": "processing",
  "upload_time": "2024-01-15T10:00:00Z"
}
```

## 📈 Monitoring & Analytics

### Get Model Performance

```bash
GET /api/v2/models/{model_id}/performance
```

**Parameters:**
- `metric_type` (string): Specific metric type
- `time_range` (string): 1h, 1d, 7d, 30d, 90d

**Response:**
```json
{
  "model_id": "model_123",
  "time_range": "7d",
  "metrics": {
    "accuracy": {
      "current": 0.94,
      "trend": "stable",
      "history": [
        {"timestamp": "2024-01-14T00:00:00Z", "value": 0.93},
        {"timestamp": "2024-01-15T00:00:00Z", "value": 0.94}
      ]
    },
    "latency": {
      "current": 45.2,
      "trend": "improving",
      "history": [
        {"timestamp": "2024-01-14T00:00:00Z", "value": 48.1},
        {"timestamp": "2024-01-15T00:00:00Z", "value": 45.2}
      ]
    }
  },
  "usage_stats": {
    "total_predictions": 15420,
    "daily_average": 2203,
    "peak_rps": 12.5
  }
}
```

## 💻 Code Examples

### Python SDK Example

```python
import requests
from aiml_platform import AIMLClient

# Initialize client
client = AIMLClient(
    api_key="your_api_key",
    base_url="https://api.aiml-platform.com"
)

# Train a model
training_job = client.models.train(
    name="My Custom Model",
    dataset_id="dataset_123",
    model_type="classification",
    framework="autogluon"
)

print(f"Training job started: {training_job.id}")

# Wait for completion
training_job.wait_for_completion()

# Deploy the model
deployment = client.models.deploy(
    model_id=training_job.model_id,
    environment="production"
)

print(f"Model deployed at: {deployment.endpoint_url}")

# Make predictions
result = client.predict(
    model_id=training_job.model_id,
    data={"feature1": 1.0, "feature2": 2.0}
)

print(f"Prediction: {result.prediction}")
```

### JavaScript SDK Example

```javascript
import { AIMLClient } from '@aiml-platform/sdk';

const client = new AIMLClient({
  apiKey: 'your_api_key',
  baseUrl: 'https://api.aiml-platform.com'
});

// Upload and classify image
async function classifyImage(imageFile) {
  const result = await client.cv.classify({
    image: imageFile,
    modelName: 'resnet50',
    topK: 5
  });
  
  console.log('Classification results:', result.results);
  return result;
}

// Analyze text sentiment
async function analyzeSentiment(text) {
  const result = await client.nlp.classify({
    text: text,
    modelName: 'sentiment_classifier'
  });
  
  console.log('Sentiment:', result.classification.label);
  return result;
}
```

### cURL Examples

```bash
# Batch image classification
curl -X POST \
  -H "Authorization: Bearer TOKEN" \
  -F "images=@image1.jpg" \
  -F "images=@image2.jpg" \
  -F "images=@image3.jpg" \
  -F "model_name=resnet50" \
  "https://api.aiml-platform.com/api/v2/cv/classify/batch"

# Real-time model monitoring
curl -H "Authorization: Bearer TOKEN" \
  "https://api.aiml-platform.com/api/v2/models/model_123/metrics/realtime"

# Model comparison
curl -X POST \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model_ids": ["model_123", "model_456"],
    "test_dataset_id": "dataset_789",
    "metrics": ["accuracy", "precision", "recall"]
  }' \
  "https://api.aiml-platform.com/api/v2/models/compare"
```

## 🔧 Advanced Features

### Webhooks

Configure webhooks to receive notifications about model training, deployment, and performance:

```bash
POST /api/v2/webhooks
```

```json
{
  "url": "https://your-app.com/webhook",
  "events": ["model.training.completed", "model.deployed", "model.performance.degraded"],
  "secret": "webhook_secret_key"
}
```

### Batch Processing

Process multiple items efficiently:

```bash
POST /api/v2/batch/process
```

```json
{
  "job_type": "image_classification",
  "model_id": "model_123",
  "input_urls": [
    "s3://bucket/image1.jpg",
    "s3://bucket/image2.jpg"
  ],
  "output_location": "s3://bucket/results/",
  "callback_url": "https://your-app.com/batch-complete"
}
```

---

## 📞 Support

- **Documentation**: https://docs.aiml-platform.com
- **API Status**: https://status.aiml-platform.com
- **Support Email**: <EMAIL>
- **Community Forum**: https://community.aiml-platform.com
