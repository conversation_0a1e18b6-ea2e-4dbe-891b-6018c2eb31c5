# 🚀 Enterprise AI/ML Platform - Comprehensive API Reference

> **Complete API documentation for the market-leading Enterprise AI/ML Platform with 18+ AI domains, real-time processing, and enterprise-grade security.**

---

## 📋 **API Overview**

### **Base Information**
- **Base URL**: `https://api.aiml-platform.com/v1`
- **Authentication**: <PERSON><PERSON> (JWT)
- **Content Type**: `application/json`
- **Rate Limiting**: 1000 requests/minute (Enterprise: Unlimited)
- **API Version**: `v1.0.0`

### **Quick Start**
```bash
# Get API token
curl -X POST https://api.aiml-platform.com/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Use token for API calls
curl -X GET https://api.aiml-platform.com/v1/models \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

## 🔐 **Authentication & Authorization**

### **Login**
```http
POST /v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "role": "data_scientist",
    "permissions": ["model:create", "model:deploy", "data:read"]
  }
}
```

### **Token Refresh**
```http
POST /v1/auth/refresh
Authorization: Bearer REFRESH_TOKEN
```

---

## 🤖 **AutoML & Tabular Data**

### **Train AutoML Model**
```http
POST /v1/automl/tabular/train
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "dataset_id": "dataset_123",
  "target_column": "target",
  "problem_type": "classification",
  "time_limit": 3600,
  "quality_preset": "best_quality",
  "experiment_name": "Customer Churn Prediction"
}
```

**Response:**
```json
{
  "task_id": "task_456",
  "status": "training_started",
  "estimated_time": "30-60 minutes",
  "experiment_id": "exp_789",
  "message": "AutoML training initiated successfully"
}
```

### **Get Training Status**
```http
GET /v1/automl/tabular/status/{task_id}
Authorization: Bearer JWT_TOKEN
```

**Response:**
```json
{
  "task_id": "task_456",
  "status": "training",
  "progress": 75,
  "current_step": "Model ensemble creation",
  "estimated_remaining": "8 minutes",
  "metrics": {
    "best_accuracy": 0.94,
    "models_trained": 45,
    "time_elapsed": "22 minutes"
  }
}
```

### **Get Model Results**
```http
GET /v1/automl/tabular/results/{task_id}
Authorization: Bearer JWT_TOKEN
```

**Response:**
```json
{
  "model_id": "model_123",
  "status": "completed",
  "metrics": {
    "accuracy": 0.962,
    "precision": 0.958,
    "recall": 0.965,
    "f1_score": 0.961,
    "auc_roc": 0.987
  },
  "leaderboard": [
    {
      "model": "WeightedEnsemble_L3",
      "score": 0.962,
      "pred_time": 0.001,
      "fit_time": 1247.3
    }
  ],
  "feature_importance": [
    {"feature": "age", "importance": 0.23},
    {"feature": "income", "importance": 0.19}
  ]
}
```

---

## 🖼️ **Computer Vision**

### **Image Classification**
```http
POST /v1/computer-vision/classify
Authorization: Bearer JWT_TOKEN
Content-Type: multipart/form-data

file: [image_file]
model_id: "cv_model_123" (optional)
confidence_threshold: 0.8 (optional)
```

**Response:**
```json
{
  "predictions": [
    {
      "class": "cat",
      "confidence": 0.95,
      "bbox": null
    },
    {
      "class": "dog", 
      "confidence": 0.03,
      "bbox": null
    }
  ],
  "processing_time": 0.087,
  "model_used": "efficientnet_b0",
  "image_info": {
    "width": 224,
    "height": 224,
    "format": "JPEG"
  }
}
```

### **Object Detection**
```http
POST /v1/computer-vision/detect
Authorization: Bearer JWT_TOKEN
Content-Type: multipart/form-data

file: [image_file]
confidence_threshold: 0.5
```

**Response:**
```json
{
  "detections": [
    {
      "class": "person",
      "confidence": 0.92,
      "bbox": [100, 50, 200, 300],
      "area": 15000
    },
    {
      "class": "car",
      "confidence": 0.87,
      "bbox": [300, 100, 500, 250],
      "area": 30000
    }
  ],
  "total_detections": 2,
  "processing_time": 0.156,
  "model_used": "yolov8n"
}
```

### **Train Custom Vision Model**
```http
POST /v1/computer-vision/train
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "dataset_id": "cv_dataset_123",
  "model_type": "classification",
  "architecture": "efficientnet_b3",
  "epochs": 50,
  "batch_size": 32,
  "learning_rate": 0.001,
  "augmentation": true
}
```

---

## 📝 **Natural Language Processing**

### **Sentiment Analysis**
```http
POST /v1/nlp/sentiment
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "text": "I love this product! It's amazing and works perfectly.",
  "model": "roberta-base" (optional)
}
```

**Response:**
```json
{
  "sentiment": "positive",
  "confidence": 0.94,
  "scores": {
    "positive": 0.94,
    "negative": 0.04,
    "neutral": 0.02
  },
  "processing_time": 0.023
}
```

### **Named Entity Recognition**
```http
POST /v1/nlp/ner
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "text": "Apple Inc. was founded by Steve Jobs in Cupertino, California.",
  "model": "bert-base-ner" (optional)
}
```

**Response:**
```json
{
  "entities": [
    {
      "text": "Apple Inc.",
      "label": "ORG",
      "confidence": 0.99,
      "start": 0,
      "end": 10
    },
    {
      "text": "Steve Jobs",
      "label": "PERSON", 
      "confidence": 0.98,
      "start": 26,
      "end": 36
    },
    {
      "text": "Cupertino",
      "label": "LOC",
      "confidence": 0.95,
      "start": 40,
      "end": 49
    }
  ],
  "processing_time": 0.045
}
```

### **Text Generation**
```http
POST /v1/nlp/generate
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "prompt": "The future of artificial intelligence is",
  "max_length": 100,
  "temperature": 0.7,
  "model": "gpt2-medium" (optional)
}
```

**Response:**
```json
{
  "generated_text": "The future of artificial intelligence is bright and full of possibilities. As we continue to advance in machine learning and deep learning technologies, we can expect to see AI systems that are more capable, efficient, and beneficial to humanity.",
  "prompt": "The future of artificial intelligence is",
  "model_used": "gpt2-medium",
  "processing_time": 1.234
}
```

---

## 📈 **Time Series Analysis**

### **Create Forecast**
```http
POST /v1/time-series/forecast
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "dataset_id": "ts_dataset_123",
  "target_column": "sales",
  "date_column": "date",
  "forecast_horizon": 30,
  "model": "prophet",
  "seasonality": "auto",
  "confidence_intervals": true
}
```

**Response:**
```json
{
  "forecast_id": "forecast_456",
  "status": "completed",
  "forecast": [
    {
      "date": "2024-01-01",
      "predicted_value": 1250.5,
      "lower_bound": 1180.2,
      "upper_bound": 1320.8
    }
  ],
  "metrics": {
    "mape": 8.5,
    "rmse": 45.2,
    "mae": 32.1
  },
  "model_info": {
    "model_type": "prophet",
    "seasonality_detected": ["yearly", "weekly"],
    "trend": "increasing"
  }
}
```

### **Anomaly Detection**
```http
POST /v1/time-series/anomaly-detection
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "dataset_id": "ts_dataset_123",
  "target_column": "cpu_usage",
  "method": "isolation_forest",
  "contamination": 0.1
}
```

**Response:**
```json
{
  "anomalies": [
    {
      "timestamp": "2024-01-15T14:30:00Z",
      "value": 95.2,
      "anomaly_score": 0.87,
      "severity": "high"
    }
  ],
  "total_anomalies": 12,
  "anomaly_rate": 0.08,
  "model_performance": {
    "precision": 0.92,
    "recall": 0.88,
    "f1_score": 0.90
  }
}
```

---

## 🎨 **Generative AI**

### **Text-to-Image Generation**
```http
POST /v1/generative-ai/text-to-image
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "prompt": "A futuristic city with flying cars and neon lights",
  "style": "cyberpunk",
  "resolution": "1024x1024",
  "num_images": 1,
  "guidance_scale": 7.5
}
```

**Response:**
```json
{
  "images": [
    {
      "url": "https://storage.aiml-platform.com/generated/img_123.png",
      "prompt": "A futuristic city with flying cars and neon lights",
      "seed": 42,
      "guidance_scale": 7.5
    }
  ],
  "generation_time": 8.5,
  "model_used": "stable-diffusion-xl"
}
```

### **Code Generation**
```http
POST /v1/generative-ai/code-generation
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "description": "Create a Python function to calculate fibonacci numbers",
  "language": "python",
  "style": "clean",
  "include_tests": true
}
```

**Response:**
```json
{
  "generated_code": "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)\n\ndef test_fibonacci():\n    assert fibonacci(0) == 0\n    assert fibonacci(1) == 1\n    assert fibonacci(10) == 55",
  "explanation": "This function calculates the nth Fibonacci number using recursion...",
  "language": "python",
  "complexity": "O(2^n)",
  "suggestions": ["Consider using memoization for better performance"]
}
```

---

## 🎯 **Model Management**

### **List Models**
```http
GET /v1/models?page=1&limit=20&type=classification
Authorization: Bearer JWT_TOKEN
```

**Response:**
```json
{
  "models": [
    {
      "id": "model_123",
      "name": "Customer Churn Predictor",
      "type": "classification",
      "status": "deployed",
      "accuracy": 0.94,
      "created_at": "2024-01-01T10:00:00Z",
      "last_updated": "2024-01-15T14:30:00Z",
      "predictions_count": 15420,
      "deployment_url": "https://api.aiml-platform.com/v1/predict/model_123"
    }
  ],
  "total": 45,
  "page": 1,
  "limit": 20,
  "has_next": true
}
```

### **Deploy Model**
```http
POST /v1/models/{model_id}/deploy
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "environment": "production",
  "auto_scaling": {
    "min_replicas": 2,
    "max_replicas": 10,
    "target_cpu": 70
  },
  "resource_limits": {
    "cpu": "2000m",
    "memory": "4Gi"
  }
}
```

**Response:**
```json
{
  "deployment_id": "deploy_456",
  "status": "deploying",
  "endpoint_url": "https://api.aiml-platform.com/v1/predict/model_123",
  "estimated_time": "3-5 minutes",
  "deployment_config": {
    "environment": "production",
    "replicas": 2,
    "resources": {
      "cpu": "2000m",
      "memory": "4Gi"
    }
  }
}
```

### **Make Predictions**
```http
POST /v1/predict/{model_id}
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "features": {
    "age": 35,
    "income": 75000,
    "credit_score": 720,
    "account_length": 24
  }
}
```

**Response:**
```json
{
  "prediction": "no_churn",
  "confidence": 0.87,
  "probabilities": {
    "churn": 0.13,
    "no_churn": 0.87
  },
  "feature_importance": {
    "credit_score": 0.35,
    "income": 0.28,
    "account_length": 0.22,
    "age": 0.15
  },
  "prediction_id": "pred_789",
  "processing_time": 0.012
}
```

---

## 📊 **Analytics & Monitoring**

### **Model Performance Metrics**
```http
GET /v1/analytics/models/{model_id}/performance?period=7d
Authorization: Bearer JWT_TOKEN
```

**Response:**
```json
{
  "model_id": "model_123",
  "period": "7d",
  "metrics": {
    "accuracy": 0.94,
    "precision": 0.92,
    "recall": 0.96,
    "f1_score": 0.94,
    "auc_roc": 0.98
  },
  "predictions": {
    "total": 15420,
    "daily_average": 2203,
    "peak_hour": "14:00-15:00"
  },
  "performance_trend": [
    {
      "date": "2024-01-01",
      "accuracy": 0.94,
      "predictions": 2150
    }
  ],
  "drift_detection": {
    "data_drift": false,
    "concept_drift": false,
    "last_check": "2024-01-15T12:00:00Z"
  }
}
```

### **System Health**
```http
GET /v1/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T15:30:00Z",
  "version": "2.0.0",
  "services": {
    "api": "healthy",
    "database": "healthy",
    "cache": "healthy",
    "ml_service": "healthy",
    "gpu_cluster": "healthy"
  },
  "metrics": {
    "cpu_usage": 45.2,
    "memory_usage": 62.8,
    "disk_usage": 34.1,
    "active_models": 127,
    "requests_per_minute": 1250
  }
}
```

---

## 🔧 **Error Handling**

### **Error Response Format**
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "target_column",
      "issue": "Column 'target' not found in dataset"
    },
    "request_id": "req_123456",
    "timestamp": "2024-01-15T15:30:00Z"
  }
}
```

### **Common Error Codes**
| Code | Status | Description |
|------|--------|-------------|
| `AUTHENTICATION_REQUIRED` | 401 | Missing or invalid authentication token |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks required permissions |
| `RESOURCE_NOT_FOUND` | 404 | Requested resource does not exist |
| `VALIDATION_ERROR` | 422 | Invalid input parameters |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `INTERNAL_ERROR` | 500 | Internal server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

---

## 📚 **SDKs & Libraries**

### **Python SDK**
```python
from aiml_platform import AimlClient

# Initialize client
client = AimlClient(api_key="your_api_key")

# Train AutoML model
task = client.automl.train_tabular(
    dataset_id="dataset_123",
    target_column="target",
    problem_type="classification"
)

# Make predictions
prediction = client.predict(
    model_id="model_123",
    features={"age": 35, "income": 75000}
)
```

### **JavaScript SDK**
```javascript
import { AimlClient } from '@aiml-platform/sdk';

// Initialize client
const client = new AimlClient({ apiKey: 'your_api_key' });

// Analyze sentiment
const result = await client.nlp.analyzeSentiment({
  text: "I love this product!"
});

console.log(result.sentiment); // "positive"
```

---

## 📞 **Support & Resources**

- **📖 Documentation**: https://docs.aiml-platform.com
- **🎮 Interactive API Explorer**: https://api.aiml-platform.com/docs
- **💬 Community Forum**: https://community.aiml-platform.com
- **📧 Support Email**: <EMAIL>
- **📱 Status Page**: https://status.aiml-platform.com

---

*📅 Last Updated: December 2024*  
*🏷️ API Version: v1.0.0*  
*👥 API Team: Enterprise AI/ML Platform*
