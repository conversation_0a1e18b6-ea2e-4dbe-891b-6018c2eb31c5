"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { 
  Brain,
  Eye,
  MessageSquare,
  TrendingUp,
  Zap,
  Users,
  Database,
  BarChart3,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Play,
  Settings,
  Download
} from "lucide-react"

const stats = [
  {
    title: "Active Models",
    value: "24",
    change: "+12%",
    icon: Brain,
    color: "text-blue-600"
  },
  {
    title: "Total Predictions",
    value: "1.2M",
    change: "+23%",
    icon: Activity,
    color: "text-green-600"
  },
  {
    title: "Data Processed",
    value: "847GB",
    change: "+8%",
    icon: Database,
    color: "text-purple-600"
  },
  {
    title: "Accuracy Rate",
    value: "98.7%",
    change: "+0.3%",
    icon: BarChart3,
    color: "text-orange-600"
  }
]

const recentModels = [
  {
    name: "Customer Sentiment Analysis",
    type: "NLP",
    status: "deployed",
    accuracy: 96.8,
    lastUpdated: "2 hours ago",
    icon: MessageSquare
  },
  {
    name: "Product Image Classifier",
    type: "Computer Vision",
    status: "training",
    accuracy: 94.2,
    lastUpdated: "5 minutes ago",
    icon: Eye
  },
  {
    name: "Sales Forecasting",
    type: "Time Series",
    status: "deployed",
    accuracy: 92.1,
    lastUpdated: "1 day ago",
    icon: TrendingUp
  },
  {
    name: "Fraud Detection",
    type: "AutoML",
    status: "testing",
    accuracy: 99.1,
    lastUpdated: "30 minutes ago",
    icon: Zap
  }
]

const quickActions = [
  {
    title: "Create New Model",
    description: "Start building a new AI/ML model",
    icon: Plus,
    action: "create"
  },
  {
    title: "Import Dataset",
    description: "Upload and prepare your data",
    icon: Database,
    action: "import"
  },
  {
    title: "Run Experiment",
    description: "Test model performance",
    icon: Play,
    action: "experiment"
  },
  {
    title: "Deploy Model",
    description: "Push model to production",
    icon: Settings,
    action: "deploy"
  }
]

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState("overview")

  const getStatusColor = (status: string) => {
    switch (status) {
      case "deployed": return "bg-green-100 text-green-800"
      case "training": return "bg-blue-100 text-blue-800"
      case "testing": return "bg-yellow-100 text-yellow-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "deployed": return CheckCircle
      case "training": return Clock
      case "testing": return AlertCircle
      default: return Clock
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">AI/ML Dashboard</h1>
            <p className="text-sm text-muted-foreground">
              Manage your AI models and experiments
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export Data
            </Button>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              New Model
            </Button>
          </div>
        </div>
      </div>

      <div className="container py-8">
        {/* Stats Overview */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                    <p className="text-xs text-green-600">{stat.change} from last month</p>
                  </div>
                  <div className={`p-2 rounded-lg bg-muted ${stat.color}`}>
                    <stat.icon className="h-6 w-6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Recent Models */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Models</CardTitle>
                <CardDescription>
                  Your latest AI/ML models and their performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentModels.map((model, index) => {
                    const StatusIcon = getStatusIcon(model.status)
                    return (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className="p-2 rounded-lg bg-muted">
                            <model.icon className="h-5 w-5" />
                          </div>
                          <div>
                            <h4 className="font-medium">{model.name}</h4>
                            <p className="text-sm text-muted-foreground">{model.type}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <p className="text-sm font-medium">{model.accuracy}% accuracy</p>
                            <p className="text-xs text-muted-foreground">{model.lastUpdated}</p>
                          </div>
                          <Badge className={getStatusColor(model.status)}>
                            <StatusIcon className="mr-1 h-3 w-3" />
                            {model.status}
                          </Badge>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Performance Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Model Performance Trends</CardTitle>
                <CardDescription>
                  Track accuracy and performance over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center bg-muted/30 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Performance chart would be displayed here</p>
                    <p className="text-sm text-muted-foreground">Integration with Recharts for real data visualization</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common tasks and workflows
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {quickActions.map((action, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="w-full justify-start h-auto p-4"
                  >
                    <action.icon className="mr-3 h-5 w-5" />
                    <div className="text-left">
                      <div className="font-medium">{action.title}</div>
                      <div className="text-xs text-muted-foreground">
                        {action.description}
                      </div>
                    </div>
                  </Button>
                ))}
              </CardContent>
            </Card>

            {/* System Status */}
            <Card>
              <CardHeader>
                <CardTitle>System Status</CardTitle>
                <CardDescription>
                  Platform health and resources
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>CPU Usage</span>
                    <span>67%</span>
                  </div>
                  <Progress value={67} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Memory</span>
                    <span>45%</span>
                  </div>
                  <Progress value={45} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>GPU Usage</span>
                    <span>89%</span>
                  </div>
                  <Progress value={89} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Storage</span>
                    <span>34%</span>
                  </div>
                  <Progress value={34} className="h-2" />
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest platform events
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="text-sm">
                      <p className="font-medium">Model deployed successfully</p>
                      <p className="text-muted-foreground">2 minutes ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="text-sm">
                      <p className="font-medium">Training started</p>
                      <p className="text-muted-foreground">15 minutes ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <div className="text-sm">
                      <p className="font-medium">Dataset uploaded</p>
                      <p className="text-muted-foreground">1 hour ago</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
