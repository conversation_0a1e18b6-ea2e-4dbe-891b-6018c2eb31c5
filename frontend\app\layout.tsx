import type { <PERSON><PERSON><PERSON>, View<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON> } from "next/font/google";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Analytics } from "@/components/analytics";
import { ProgressBar } from "@/components/progress-bar";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: "Enterprise AI/ML Platform",
    template: "%s | Enterprise AI/ML Platform",
  },
  description: "Production-ready, enterprise-grade No Code AI/ML platform with comprehensive capabilities across all domains of artificial intelligence and machine learning.",
  keywords: [
    "AI",
    "Machine Learning",
    "No Code",
    "Enterprise",
    "AutoML",
    "Deep Learning",
    "Computer Vision",
    "NLP",
    "Time Series",
    "Reinforcement Learning",
    "Generative AI",
    "MLOps",
  ],
  authors: [{ name: "AI Platform Team" }],
  creator: "AI Platform Team",
  publisher: "Enterprise AI/ML Platform",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://aiml-platform.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://aiml-platform.com",
    title: "Enterprise AI/ML Platform",
    description: "Production-ready, enterprise-grade No Code AI/ML platform",
    siteName: "Enterprise AI/ML Platform",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Enterprise AI/ML Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Enterprise AI/ML Platform",
    description: "Production-ready, enterprise-grade No Code AI/ML platform",
    images: ["/og-image.png"],
    creator: "@aimlplatform",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${inter.variable} antialiased min-h-screen bg-background font-sans`}
        suppressHydrationWarning
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <TooltipProvider>
            <ProgressBar />
            <div className="relative flex min-h-screen flex-col">
              <div className="flex-1">{children}</div>
            </div>
            <Toaster />
            <Analytics />
          </TooltipProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
