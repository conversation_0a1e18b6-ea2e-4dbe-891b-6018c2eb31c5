import { Suspense } from "react"
import { HeroSection } from "@/components/sections/hero-section"
import { FeaturesSection } from "@/components/sections/features-section"
import { TechStackSection } from "@/components/sections/tech-stack-section"
import { CapabilitiesSection } from "@/components/sections/capabilities-section"
import { DemoSection } from "@/components/sections/demo-section"
import { TestimonialsSection } from "@/components/sections/testimonials-section"
import { PricingSection } from "@/components/sections/pricing-section"
import { CTASection } from "@/components/sections/cta-section"
import { Header } from "@/components/layout/header"
import { Footer } from "@/components/layout/footer"
import { LoadingSpinner } from "@/components/ui/loading-spinner"

export default function HomePage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      <main className="flex-1">
        <Suspense fallback={<LoadingSpinner />}>
          <HeroSection />
        </Suspense>

        <Suspense fallback={<LoadingSpinner />}>
          <FeaturesSection />
        </Suspense>

        <Suspense fallback={<LoadingSpinner />}>
          <CapabilitiesSection />
        </Suspense>

        <Suspense fallback={<LoadingSpinner />}>
          <TechStackSection />
        </Suspense>

        <Suspense fallback={<LoadingSpinner />}>
          <DemoSection />
        </Suspense>

        <Suspense fallback={<LoadingSpinner />}>
          <TestimonialsSection />
        </Suspense>

        <Suspense fallback={<LoadingSpinner />}>
          <PricingSection />
        </Suspense>

        <Suspense fallback={<LoadingSpinner />}>
          <CTASection />
        </Suspense>
      </main>

      <Footer />
    </div>
  )
}
