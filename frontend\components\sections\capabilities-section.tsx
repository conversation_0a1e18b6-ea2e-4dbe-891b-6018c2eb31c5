"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  Brain,
  Eye,
  MessageSquare,
  TrendingUp,
  Gamepad2,
  Sparkles,
  Network,
  Atom,
  Shield,
  Smartphone,
  Headphones,
  Settings,
  BarChart3,
  Zap,
  ArrowRight
} from "lucide-react"

const capabilities = {
  core: [
    {
      icon: Zap,
      title: "AutoML & Tabular Data",
      description: "Automated machine learning with AutoGluon integration",
      features: ["Automated model selection", "Hyperparameter optimization", "Feature engineering", "RAPIDS acceleration"],
      badge: "Popular"
    },
    {
      icon: Eye,
      title: "Computer Vision",
      description: "Advanced image and video analysis capabilities",
      features: ["Image classification", "Object detection", "Semantic segmentation", "Face recognition"],
      badge: "Advanced"
    },
    {
      icon: MessageSquare,
      title: "Natural Language Processing",
      description: "Comprehensive text analysis and generation",
      features: ["Sentiment analysis", "Named entity recognition", "Text generation", "Language translation"],
      badge: "AI-Powered"
    },
    {
      icon: TrendingUp,
      title: "Time Series Analysis",
      description: "Forecasting and anomaly detection for temporal data",
      features: ["Prophet forecasting", "ARIMA models", "Anomaly detection", "Seasonal decomposition"],
      badge: "Predictive"
    }
  ],
  advanced: [
    {
      icon: Sparkles,
      title: "Generative AI",
      description: "Large language models and content generation",
      features: ["GPT integration", "Image generation", "Code generation", "Multimodal AI"],
      badge: "Latest"
    },
    {
      icon: Gamepad2,
      title: "Reinforcement Learning",
      description: "Intelligent agents and decision-making systems",
      features: ["DQN algorithms", "PPO training", "Multi-agent systems", "Custom environments"],
      badge: "Intelligent"
    },
    {
      icon: Network,
      title: "Graph Neural Networks",
      description: "Network analysis and graph-based learning",
      features: ["Node classification", "Link prediction", "Graph attention", "Knowledge graphs"],
      badge: "Network"
    },
    {
      icon: Atom,
      title: "Quantum Machine Learning",
      description: "Quantum computing for ML optimization",
      features: ["Variational circuits", "Quantum optimization", "Hybrid models", "Qiskit integration"],
      badge: "Quantum"
    }
  ],
  specialized: [
    {
      icon: Shield,
      title: "Federated Learning",
      description: "Privacy-preserving distributed training",
      features: ["Differential privacy", "Secure aggregation", "Non-IID data", "Edge federation"],
      badge: "Private"
    },
    {
      icon: Smartphone,
      title: "Edge AI",
      description: "Model optimization for edge deployment",
      features: ["Model quantization", "Pruning techniques", "ONNX conversion", "Mobile optimization"],
      badge: "Mobile"
    },
    {
      icon: Headphones,
      title: "Audio Processing",
      description: "Speech and audio analysis capabilities",
      features: ["Speech recognition", "Audio classification", "Music analysis", "Voice synthesis"],
      badge: "Audio"
    },
    {
      icon: Settings,
      title: "MLOps",
      description: "Complete ML lifecycle management",
      features: ["Model registry", "Experiment tracking", "CI/CD pipelines", "Monitoring"],
      badge: "DevOps"
    }
  ],
  analytics: [
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description: "Model interpretability and business insights",
      features: ["SHAP explanations", "LIME analysis", "A/B testing", "Causal inference"],
      badge: "Insights"
    }
  ]
}

export function CapabilitiesSection() {
  const [activeTab, setActiveTab] = useState("core")

  const getAllCapabilities = () => {
    return Object.values(capabilities).flat()
  }

  return (
    <section className="py-24">
      <div className="container">
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="px-3 py-1">
            AI/ML Capabilities
          </Badge>
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            18+ Specialized
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              {" "}AI Domains
            </span>
          </h2>
          <p className="mx-auto max-w-[700px] text-lg text-muted-foreground">
            From traditional machine learning to cutting-edge quantum AI, our platform 
            covers every domain of artificial intelligence and machine learning.
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 lg:w-fit lg:mx-auto">
            <TabsTrigger value="core">Core ML</TabsTrigger>
            <TabsTrigger value="advanced">Advanced AI</TabsTrigger>
            <TabsTrigger value="specialized">Specialized</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="core" className="mt-12">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {capabilities.core.map((capability, index) => (
                <CapabilityCard key={index} capability={capability} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="mt-12">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {capabilities.advanced.map((capability, index) => (
                <CapabilityCard key={index} capability={capability} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="specialized" className="mt-12">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {capabilities.specialized.map((capability, index) => (
                <CapabilityCard key={index} capability={capability} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="mt-12">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {capabilities.analytics.map((capability, index) => (
                <CapabilityCard key={index} capability={capability} />
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Stats */}
        <div className="mt-16 grid grid-cols-2 gap-8 md:grid-cols-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">18+</div>
            <div className="text-sm text-muted-foreground">AI/ML Domains</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">50+</div>
            <div className="text-sm text-muted-foreground">Pre-built Models</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">99.9%</div>
            <div className="text-sm text-muted-foreground">Accuracy Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">10TB+</div>
            <div className="text-sm text-muted-foreground">Data Processed</div>
          </div>
        </div>

        {/* CTA */}
        <div className="mt-16 text-center">
          <Button size="lg" className="group">
            Explore All Capabilities
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </div>
      </div>
    </section>
  )
}

function CapabilityCard({ capability }: { capability: any }) {
  return (
    <Card className="group relative overflow-hidden border-0 bg-background/50 backdrop-blur transition-all hover:shadow-lg hover:shadow-primary/5">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
            <capability.icon className="h-6 w-6 text-primary" />
          </div>
          <Badge variant="secondary" className="text-xs">
            {capability.badge}
          </Badge>
        </div>
        <CardTitle className="text-lg">{capability.title}</CardTitle>
        <CardDescription className="text-sm">
          {capability.description}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <ul className="space-y-2">
          {capability.features.map((feature: string, index: number) => (
            <li key={index} className="flex items-center text-sm text-muted-foreground">
              <div className="mr-2 h-1.5 w-1.5 rounded-full bg-primary/60" />
              {feature}
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}
