import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { 
  ArrowRight,
  Rocket,
  Shield,
  Zap,
  Users,
  CheckCircle,
  Star,
  Clock,
  Globe
} from "lucide-react"

const benefits = [
  {
    icon: Rocket,
    title: "Deploy in Minutes",
    description: "Get your first AI model running in under 5 minutes"
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description: "SOC2 compliant with bank-grade encryption"
  },
  {
    icon: Zap,
    title: "Lightning Fast",
    description: "Sub-second inference with auto-scaling"
  },
  {
    icon: Users,
    title: "Expert Support",
    description: "24/7 support from AI/ML experts"
  }
]

const stats = [
  { label: "Active Users", value: "50K+", icon: Users },
  { label: "Models Deployed", value: "100K+", icon: Rocket },
  { label: "Uptime", value: "99.9%", icon: Shield },
  { label: "Countries", value: "150+", icon: Globe }
]

const guarantees = [
  "14-day free trial",
  "No setup fees",
  "Cancel anytime",
  "Money-back guarantee",
  "Free migration support",
  "Dedicated onboarding"
]

export function CTASection() {
  return (
    <section className="py-24 bg-gradient-to-br from-primary/5 via-background to-muted/20">
      <div className="container">
        {/* Main CTA */}
        <div className="text-center space-y-8 mb-16">
          <Badge variant="outline" className="px-4 py-2">
            <Star className="mr-2 h-4 w-4" />
            Ready to Transform Your Business?
          </Badge>
          
          <div className="space-y-4">
            <h2 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl">
              Start Building
              <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                {" "}AI Solutions{" "}
              </span>
              Today
            </h2>
            <p className="mx-auto max-w-[600px] text-lg text-muted-foreground">
              Join thousands of companies already using our platform to build, deploy, 
              and scale AI/ML solutions. No credit card required.
            </p>
          </div>

          {/* Primary CTA buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="group text-lg px-8 py-6">
              Start Free Trial
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6">
              Schedule Demo
            </Button>
          </div>

          {/* Trust indicators */}
          <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-muted-foreground">
            {guarantees.map((guarantee, index) => (
              <div key={index} className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>{guarantee}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Benefits grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-16">
          {benefits.map((benefit, index) => (
            <Card key={index} className="border-0 bg-background/50 backdrop-blur text-center">
              <CardContent className="p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 mx-auto mb-4">
                  <benefit.icon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold mb-2">{benefit.title}</h3>
                <p className="text-sm text-muted-foreground">{benefit.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-8 md:grid-cols-4 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="flex items-center justify-center mb-2">
                <stat.icon className="h-6 w-6 text-primary mr-2" />
                <div className="text-3xl font-bold text-primary">{stat.value}</div>
              </div>
              <div className="text-sm text-muted-foreground">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Newsletter signup */}
        <Card className="border-0 bg-background/50 backdrop-blur">
          <CardContent className="p-8 md:p-12">
            <div className="grid gap-8 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Clock className="h-5 w-5 text-primary" />
                  <Badge variant="secondary">Stay Updated</Badge>
                </div>
                <h3 className="text-2xl font-bold">
                  Get the latest AI/ML insights
                </h3>
                <p className="text-muted-foreground">
                  Subscribe to our newsletter for the latest updates on AI/ML trends, 
                  platform features, and industry insights. Join 25,000+ AI practitioners.
                </p>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    Weekly AI/ML insights and tutorials
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    Early access to new features
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    Exclusive webinars and events
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    No spam, unsubscribe anytime
                  </li>
                </ul>
              </div>
              
              <div className="space-y-4">
                <div className="flex space-x-2">
                  <Input
                    type="email"
                    placeholder="Enter your email address"
                    className="flex-1"
                  />
                  <Button type="submit">
                    Subscribe
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  By subscribing, you agree to our Privacy Policy and consent to receive updates from our company.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Final CTA */}
        <div className="mt-16 text-center space-y-6">
          <h3 className="text-2xl font-bold">
            Ready to revolutionize your AI/ML workflow?
          </h3>
          <p className="text-muted-foreground max-w-md mx-auto">
            Join the AI revolution today. Build, deploy, and scale AI solutions 
            faster than ever before.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="group">
              Get Started Now
              <Rocket className="ml-2 h-5 w-5 transition-transform group-hover:translate-y-[-2px]" />
            </Button>
            <Button size="lg" variant="outline">
              Talk to Sales
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
