"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  Play,
  Eye,
  MessageSquare,
  TrendingUp,
  Brain,
  ArrowRight,
  Clock,
  Users,
  CheckCircle
} from "lucide-react"

const demos = {
  "computer-vision": {
    title: "Computer Vision",
    icon: Eye,
    description: "Real-time object detection and image classification",
    duration: "3 min",
    difficulty: "Beginner",
    features: [
      "Upload images or use webcam",
      "Real-time object detection",
      "Custom model training",
      "Export to mobile apps"
    ],
    metrics: {
      accuracy: "99.2%",
      speed: "< 100ms",
      models: "50+"
    }
  },
  "nlp": {
    title: "Natural Language Processing",
    icon: MessageSquare,
    description: "Text analysis, sentiment detection, and language generation",
    duration: "4 min",
    difficulty: "Intermediate",
    features: [
      "Sentiment analysis",
      "Named entity recognition",
      "Text summarization",
      "Language translation"
    ],
    metrics: {
      accuracy: "97.8%",
      languages: "100+",
      models: "30+"
    }
  },
  "time-series": {
    title: "Time Series Forecasting",
    icon: TrendingUp,
    description: "Predict future trends and detect anomalies",
    duration: "5 min",
    difficulty: "Advanced",
    features: [
      "Automated forecasting",
      "Anomaly detection",
      "Seasonal patterns",
      "Multi-variate analysis"
    ],
    metrics: {
      accuracy: "95.5%",
      horizon: "365 days",
      algorithms: "15+"
    }
  },
  "automl": {
    title: "AutoML",
    icon: Brain,
    description: "Automated machine learning for any dataset",
    duration: "2 min",
    difficulty: "Beginner",
    features: [
      "Drag & drop datasets",
      "Automated preprocessing",
      "Model selection",
      "One-click deployment"
    ],
    metrics: {
      automation: "100%",
      time_saved: "90%",
      models: "200+"
    }
  }
}

export function DemoSection() {
  const [activeDemo, setActiveDemo] = useState("computer-vision")
  const [isPlaying, setIsPlaying] = useState(false)

  const currentDemo = demos[activeDemo as keyof typeof demos]

  return (
    <section className="py-24">
      <div className="container">
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="px-3 py-1">
            Interactive Demos
          </Badge>
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            See it in
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              {" "}action
            </span>
          </h2>
          <p className="mx-auto max-w-[700px] text-lg text-muted-foreground">
            Experience the power of our AI/ML platform with interactive demos. 
            No signup required - start exploring immediately.
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          {/* Demo selector */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold mb-4">Choose a Demo</h3>
            <div className="grid gap-3">
              {Object.entries(demos).map(([key, demo]) => (
                <Card 
                  key={key}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    activeDemo === key 
                      ? 'ring-2 ring-primary bg-primary/5' 
                      : 'hover:bg-muted/50'
                  }`}
                  onClick={() => setActiveDemo(key)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className={`flex h-10 w-10 items-center justify-center rounded-lg ${
                        activeDemo === key ? 'bg-primary text-primary-foreground' : 'bg-muted'
                      }`}>
                        <demo.icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">{demo.title}</h4>
                          <Badge variant="secondary" className="text-xs">
                            {demo.difficulty}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {demo.description}
                        </p>
                        <div className="flex items-center space-x-4 mt-2">
                          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            <span>{demo.duration}</span>
                          </div>
                          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                            <Users className="h-3 w-3" />
                            <span>Interactive</span>
                          </div>
                        </div>
                      </div>
                      {activeDemo === key && (
                        <CheckCircle className="h-5 w-5 text-primary" />
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Demo player */}
          <div className="space-y-6">
            <Card className="border-0 bg-background/50 backdrop-blur">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                      <currentDemo.icon className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle>{currentDemo.title}</CardTitle>
                      <CardDescription>{currentDemo.description}</CardDescription>
                    </div>
                  </div>
                  <Badge variant="outline">{currentDemo.difficulty}</Badge>
                </div>
              </CardHeader>
              <CardContent>
                {/* Demo video placeholder */}
                <div className="relative aspect-video rounded-lg bg-muted/50 flex items-center justify-center mb-6">
                  {!isPlaying ? (
                    <Button 
                      size="lg" 
                      className="group"
                      onClick={() => setIsPlaying(true)}
                    >
                      <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                      Play Demo
                    </Button>
                  ) : (
                    <div className="text-center">
                      <div className="animate-pulse text-muted-foreground">
                        Demo playing... ({currentDemo.duration})
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-4"
                        onClick={() => setIsPlaying(false)}
                      >
                        Reset
                      </Button>
                    </div>
                  )}
                </div>

                {/* Features */}
                <div className="space-y-3 mb-6">
                  <h4 className="font-medium">What you'll see:</h4>
                  <ul className="space-y-2">
                    {currentDemo.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm">
                        <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Metrics */}
                <div className="grid grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg">
                  {Object.entries(currentDemo.metrics).map(([key, value]) => (
                    <div key={key} className="text-center">
                      <div className="font-semibold text-primary">{value}</div>
                      <div className="text-xs text-muted-foreground capitalize">
                        {key.replace('_', ' ')}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* CTA */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button className="flex-1 group">
                Try This Demo Live
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
              <Button variant="outline" className="flex-1">
                View All Demos
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom stats */}
        <div className="mt-16 grid grid-cols-2 gap-8 md:grid-cols-4 text-center">
          <div>
            <div className="text-3xl font-bold text-primary">10K+</div>
            <div className="text-sm text-muted-foreground">Demo Sessions</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary">4.9/5</div>
            <div className="text-sm text-muted-foreground">User Rating</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary">15+</div>
            <div className="text-sm text-muted-foreground">Interactive Demos</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary">24/7</div>
            <div className="text-sm text-muted-foreground">Available</div>
          </div>
        </div>
      </div>
    </section>
  )
}
