import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Zap,
  Shield,
  Gauge,
  Globe,
  Brain,
  Database,
  BarChart3,
  Cpu,
  Lock,
  Layers,
  Workflow,
  Monitor
} from "lucide-react"

const features = [
  {
    icon: Brain,
    title: "18+ AI/ML Domains",
    description: "Comprehensive coverage from AutoML to Quantum Machine Learning",
    badge: "Complete",
    details: [
      "Computer Vision & Object Detection",
      "Natural Language Processing",
      "Time Series Forecasting",
      "Reinforcement Learning",
      "Generative AI & LLMs",
      "Graph Neural Networks"
    ]
  },
  {
    icon: Zap,
    title: "No-Code Interface",
    description: "Build, train, and deploy AI models without writing a single line of code",
    badge: "Easy",
    details: [
      "Drag-and-drop model building",
      "Visual workflow designer",
      "Automated preprocessing",
      "One-click deployment",
      "Real-time collaboration",
      "Template library"
    ]
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description: "Bank-grade security with compliance certifications",
    badge: "Secure",
    details: [
      "SOC2 Type II certified",
      "GDPR & HIPAA compliant",
      "End-to-end encryption",
      "Role-based access control",
      "Audit logging",
      "Zero-trust architecture"
    ]
  },
  {
    icon: Gauge,
    title: "High Performance",
    description: "Optimized for speed with GPU acceleration and auto-scaling",
    badge: "Fast",
    details: [
      "NVIDIA RAPIDS acceleration",
      "Kubernetes auto-scaling",
      "Sub-second inference",
      "Distributed training",
      "Edge deployment",
      "CDN optimization"
    ]
  },
  {
    icon: Database,
    title: "Multi-Modal Data",
    description: "Handle any data type with intelligent preprocessing",
    badge: "Flexible",
    details: [
      "Structured & unstructured data",
      "Images, text, audio, video",
      "Real-time streaming",
      "Data versioning",
      "Quality validation",
      "Automated cleaning"
    ]
  },
  {
    icon: Globe,
    title: "Multi-Cloud Ready",
    description: "Deploy anywhere with full cloud provider support",
    badge: "Scalable",
    details: [
      "AWS, GCP, Azure support",
      "Hybrid cloud deployment",
      "Edge computing",
      "Global load balancing",
      "Disaster recovery",
      "Cost optimization"
    ]
  },
  {
    icon: Workflow,
    title: "MLOps Automation",
    description: "Complete ML lifecycle management with CI/CD integration",
    badge: "Automated",
    details: [
      "Model versioning",
      "Experiment tracking",
      "Automated testing",
      "Deployment pipelines",
      "Monitoring & alerting",
      "Rollback capabilities"
    ]
  },
  {
    icon: Monitor,
    title: "Real-time Monitoring",
    description: "Comprehensive observability with advanced analytics",
    badge: "Insights",
    details: [
      "Model performance tracking",
      "Data drift detection",
      "Business metrics",
      "Custom dashboards",
      "Predictive alerts",
      "Root cause analysis"
    ]
  }
]

export function FeaturesSection() {
  return (
    <section className="py-24 bg-muted/30">
      <div className="container">
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="px-3 py-1">
            Features
          </Badge>
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            Everything you need for
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              {" "}AI success
            </span>
          </h2>
          <p className="mx-auto max-w-[700px] text-lg text-muted-foreground">
            From data ingestion to model deployment, our platform provides all the tools 
            and capabilities you need to build world-class AI applications.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {features.map((feature, index) => (
            <Card key={index} className="group relative overflow-hidden border-0 bg-background/50 backdrop-blur transition-all hover:shadow-lg hover:shadow-primary/5">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {feature.badge}
                  </Badge>
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
                <CardDescription className="text-sm">
                  {feature.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="space-y-2">
                  {feature.details.slice(0, 3).map((detail, detailIndex) => (
                    <li key={detailIndex} className="flex items-center text-sm text-muted-foreground">
                      <div className="mr-2 h-1.5 w-1.5 rounded-full bg-primary/60" />
                      {detail}
                    </li>
                  ))}
                  {feature.details.length > 3 && (
                    <li className="text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <div className="mr-2 h-1.5 w-1.5 rounded-full bg-primary/60" />
                        +{feature.details.length - 3} more features
                      </div>
                    </li>
                  )}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="mt-16 text-center">
          <p className="text-muted-foreground mb-4">
            Ready to experience the future of AI/ML?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90">
              Start Free Trial
            </button>
            <button className="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-3 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground">
              Schedule Demo
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}
