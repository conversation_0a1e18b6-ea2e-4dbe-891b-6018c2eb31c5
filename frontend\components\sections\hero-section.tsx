"use client"

import { useState, useEffect } from "react"
import <PERSON> from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { 
  ArrowRight,
  Play,
  Zap,
  Shield,
  Gauge,
  Users,
  CheckCircle,
  Sparkles,
  Brain,
  Database,
  BarChart3,
  Cpu
} from "lucide-react"

const stats = [
  { label: "AI Models", value: "18+", icon: Brain },
  { label: "Enterprise Clients", value: "500+", icon: Users },
  { label: "Data Processed", value: "10TB+", icon: Database },
  { label: "Accuracy Rate", value: "99.9%", icon: BarChart3 },
]

const features = [
  "No-code AI/ML platform",
  "18+ specialized AI domains",
  "Enterprise-grade security",
  "Real-time processing",
  "Auto-scaling infrastructure",
  "Multi-cloud deployment"
]

const techLogos = [
  { name: "AutoGluon", logo: "🚀" },
  { name: "<PERSON>y<PERSON><PERSON><PERSON>", logo: "🔥" },
  { name: "Ten<PERSON><PERSON><PERSON>", logo: "🧠" },
  { name: "RAPIDS", logo: "⚡" },
  { name: "Kubernet<PERSON>", logo: "☸️" },
  { name: "Next.js", logo: "▲" },
]

export function HeroSection() {
  const [currentFeature, setCurrentFeature] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length)
    }, 3000)
    return () => clearInterval(timer)
  }, [])

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />
      <div className="absolute left-0 right-0 top-0 -z-10 m-auto h-[310px] w-[310px] rounded-full bg-primary/20 opacity-20 blur-[100px]" />
      
      <div className="container relative">
        <div className="flex flex-col items-center justify-center space-y-8 py-24 text-center lg:py-32">
          {/* Announcement badge */}
          <Badge variant="secondary" className="px-4 py-2">
            <Sparkles className="mr-2 h-4 w-4" />
            New: GPT-4 Integration & Quantum ML Support
          </Badge>

          {/* Main heading */}
          <div className="space-y-4">
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl">
              The Future of
              <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                {" "}AI/ML{" "}
              </span>
              is Here
            </h1>
            <p className="mx-auto max-w-[700px] text-lg text-muted-foreground sm:text-xl">
              Production-ready, enterprise-grade no-code AI/ML platform with comprehensive 
              capabilities across all domains of artificial intelligence and machine learning.
            </p>
          </div>

          {/* Dynamic feature showcase */}
          <div className="flex h-8 items-center justify-center">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span className="text-sm font-medium transition-all duration-500">
                {features[currentFeature]}
              </span>
            </div>
          </div>

          {/* CTA buttons */}
          <div className="flex flex-col gap-4 sm:flex-row">
            <Button size="lg" className="group" asChild>
              <Link href="/signup">
                Get Started Free
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="group" asChild>
              <Link href="/demo">
                <Play className="mr-2 h-4 w-4" />
                Watch Demo
              </Link>
            </Button>
          </div>

          {/* Trust indicators */}
          <div className="flex flex-wrap items-center justify-center gap-8 pt-8">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Shield className="h-4 w-4" />
              <span>SOC2 Compliant</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Gauge className="h-4 w-4" />
              <span>99.9% Uptime</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Zap className="h-4 w-4" />
              <span>Sub-second Response</span>
            </div>
          </div>
        </div>

        {/* Stats section */}
        <div className="grid grid-cols-2 gap-4 py-12 md:grid-cols-4">
          {stats.map((stat, index) => (
            <Card key={index} className="border-0 bg-muted/50 backdrop-blur">
              <CardContent className="flex flex-col items-center space-y-2 p-6">
                <stat.icon className="h-8 w-8 text-primary" />
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Technology showcase */}
        <div className="py-12">
          <p className="text-center text-sm font-medium text-muted-foreground">
            Powered by industry-leading technologies
          </p>
          <div className="mt-8 flex flex-wrap items-center justify-center gap-8">
            {techLogos.map((tech, index) => (
              <div
                key={index}
                className="flex items-center space-x-2 rounded-lg bg-muted/50 px-4 py-2 backdrop-blur"
              >
                <span className="text-2xl">{tech.logo}</span>
                <span className="text-sm font-medium">{tech.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
