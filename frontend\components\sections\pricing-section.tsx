"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { 
  Check,
  X,
  Star,
  Zap,
  Shield,
  Crown,
  ArrowRight,
  Users,
  Building,
  Rocket
} from "lucide-react"

const plans = {
  starter: {
    name: "Starter",
    icon: Zap,
    description: "Perfect for individuals and small teams getting started with AI/ML",
    monthlyPrice: 29,
    yearlyPrice: 290,
    badge: "Most Popular",
    features: {
      included: [
        "5 AI/ML models",
        "10GB data storage",
        "Basic computer vision",
        "NLP sentiment analysis",
        "Time series forecasting",
        "Community support",
        "Basic templates",
        "API access (1K calls/month)"
      ],
      excluded: [
        "Advanced AI domains",
        "Custom model training",
        "Enterprise security",
        "Priority support"
      ]
    },
    limits: {
      models: "5",
      storage: "10GB",
      apiCalls: "1K/month",
      users: "3"
    }
  },
  professional: {
    name: "<PERSON>",
    icon: Users,
    description: "Advanced features for growing teams and businesses",
    monthlyPrice: 99,
    yearlyPrice: 990,
    badge: "Best Value",
    features: {
      included: [
        "50 AI/ML models",
        "100GB data storage",
        "All AI domains (18+)",
        "Custom model training",
        "Advanced computer vision",
        "Generative AI integration",
        "Reinforcement learning",
        "Graph neural networks",
        "Priority email support",
        "Advanced templates",
        "API access (50K calls/month)",
        "Team collaboration",
        "Model versioning",
        "A/B testing"
      ],
      excluded: [
        "Enterprise security features",
        "Dedicated support",
        "Custom integrations",
        "SLA guarantees"
      ]
    },
    limits: {
      models: "50",
      storage: "100GB",
      apiCalls: "50K/month",
      users: "10"
    }
  },
  enterprise: {
    name: "Enterprise",
    icon: Building,
    description: "Full-scale AI/ML platform for large organizations",
    monthlyPrice: 499,
    yearlyPrice: 4990,
    badge: "Enterprise",
    features: {
      included: [
        "Unlimited AI/ML models",
        "1TB+ data storage",
        "All AI domains + custom",
        "Quantum machine learning",
        "Federated learning",
        "Edge AI deployment",
        "Enterprise security (SOC2)",
        "GDPR & HIPAA compliance",
        "Dedicated support manager",
        "Custom integrations",
        "White-label options",
        "API access (unlimited)",
        "Advanced analytics",
        "Custom training",
        "99.9% SLA guarantee",
        "Multi-cloud deployment",
        "Advanced monitoring"
      ],
      excluded: []
    },
    limits: {
      models: "Unlimited",
      storage: "1TB+",
      apiCalls: "Unlimited",
      users: "Unlimited"
    }
  },
  custom: {
    name: "Custom",
    icon: Crown,
    description: "Tailored solutions for unique enterprise requirements",
    monthlyPrice: null,
    yearlyPrice: null,
    badge: "Contact Sales",
    features: {
      included: [
        "Everything in Enterprise",
        "Custom AI model development",
        "Dedicated infrastructure",
        "On-premise deployment",
        "Custom compliance requirements",
        "24/7 phone support",
        "Dedicated success team",
        "Custom SLA agreements",
        "Advanced security audits",
        "Custom integrations",
        "Training & consulting",
        "Priority feature requests"
      ],
      excluded: []
    },
    limits: {
      models: "Custom",
      storage: "Custom",
      apiCalls: "Custom",
      users: "Custom"
    }
  }
}

const addOns = [
  {
    name: "Additional Storage",
    description: "Extra data storage beyond plan limits",
    price: "$0.10/GB/month"
  },
  {
    name: "Premium Support",
    description: "24/7 phone and chat support",
    price: "$199/month"
  },
  {
    name: "Custom Model Training",
    description: "Professional model development service",
    price: "$2,500/model"
  },
  {
    name: "On-Premise Deployment",
    description: "Deploy on your own infrastructure",
    price: "Contact Sales"
  }
]

export function PricingSection() {
  const [isYearly, setIsYearly] = useState(false)

  const getPrice = (plan: any) => {
    if (!plan.monthlyPrice) return "Custom"
    const price = isYearly ? plan.yearlyPrice : plan.monthlyPrice
    return `$${price}`
  }

  const getSavings = (plan: any) => {
    if (!plan.monthlyPrice) return null
    const monthlyCost = plan.monthlyPrice * 12
    const savings = monthlyCost - plan.yearlyPrice
    const percentage = Math.round((savings / monthlyCost) * 100)
    return percentage
  }

  return (
    <section className="py-24">
      <div className="container">
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="px-3 py-1">
            Pricing Plans
          </Badge>
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            Choose your
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              {" "}AI journey
            </span>
          </h2>
          <p className="mx-auto max-w-[700px] text-lg text-muted-foreground">
            Flexible pricing plans designed to scale with your AI/ML needs. 
            Start free and upgrade as you grow.
          </p>
        </div>

        {/* Billing toggle */}
        <div className="flex items-center justify-center space-x-4 mb-12">
          <span className={`text-sm ${!isYearly ? 'font-medium' : 'text-muted-foreground'}`}>
            Monthly
          </span>
          <Switch
            checked={isYearly}
            onCheckedChange={setIsYearly}
          />
          <span className={`text-sm ${isYearly ? 'font-medium' : 'text-muted-foreground'}`}>
            Yearly
          </span>
          {isYearly && (
            <Badge variant="secondary" className="ml-2">
              Save up to 20%
            </Badge>
          )}
        </div>

        {/* Pricing cards */}
        <div className="grid gap-8 lg:grid-cols-4">
          {Object.entries(plans).map(([key, plan]) => (
            <Card 
              key={key} 
              className={`relative border-0 bg-background/50 backdrop-blur transition-all hover:shadow-lg ${
                plan.badge === "Best Value" ? 'ring-2 ring-primary shadow-lg scale-105' : ''
              }`}
            >
              {plan.badge && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge 
                    variant={plan.badge === "Best Value" ? "default" : "secondary"}
                    className="px-3 py-1"
                  >
                    {plan.badge}
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <div className="flex items-center justify-center mb-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                    <plan.icon className="h-6 w-6 text-primary" />
                  </div>
                </div>
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <CardDescription className="text-sm">
                  {plan.description}
                </CardDescription>
                
                <div className="mt-4">
                  <div className="text-3xl font-bold">
                    {getPrice(plan)}
                    {plan.monthlyPrice && (
                      <span className="text-lg font-normal text-muted-foreground">
                        /{isYearly ? 'year' : 'month'}
                      </span>
                    )}
                  </div>
                  {isYearly && getSavings(plan) && (
                    <div className="text-sm text-green-600 font-medium">
                      Save {getSavings(plan)}% annually
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Key limits */}
                <div className="grid grid-cols-2 gap-3 p-3 bg-muted/30 rounded-lg">
                  {Object.entries(plan.limits).map(([key, value]) => (
                    <div key={key} className="text-center">
                      <div className="font-semibold text-sm">{value}</div>
                      <div className="text-xs text-muted-foreground capitalize">
                        {key === 'apiCalls' ? 'API Calls' : key}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Features */}
                <div className="space-y-3">
                  <h4 className="font-medium text-sm">What's included:</h4>
                  <ul className="space-y-2">
                    {plan.features.included.slice(0, 8).map((feature, index) => (
                      <li key={index} className="flex items-center text-sm">
                        <Check className="mr-2 h-4 w-4 text-green-500 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                    {plan.features.included.length > 8 && (
                      <li className="text-sm text-muted-foreground">
                        +{plan.features.included.length - 8} more features
                      </li>
                    )}
                  </ul>
                </div>

                {/* CTA */}
                <Button 
                  className={`w-full ${
                    plan.badge === "Best Value" ? '' : 'variant-outline'
                  }`}
                  variant={plan.badge === "Best Value" ? "default" : "outline"}
                >
                  {plan.name === "Custom" ? "Contact Sales" : "Get Started"}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Add-ons */}
        <div className="mt-16">
          <h3 className="text-xl font-semibold text-center mb-8">Add-ons & Extensions</h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {addOns.map((addon, index) => (
              <Card key={index} className="border-0 bg-muted/30">
                <CardContent className="p-4">
                  <h4 className="font-medium mb-2">{addon.name}</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    {addon.description}
                  </p>
                  <div className="font-semibold text-primary">{addon.price}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* FAQ */}
        <div className="mt-16 text-center">
          <h3 className="text-xl font-semibold mb-4">Frequently Asked Questions</h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 text-left">
            <div className="p-4 bg-muted/30 rounded-lg">
              <h4 className="font-medium mb-2">Can I change plans anytime?</h4>
              <p className="text-sm text-muted-foreground">
                Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
              </p>
            </div>
            <div className="p-4 bg-muted/30 rounded-lg">
              <h4 className="font-medium mb-2">Is there a free trial?</h4>
              <p className="text-sm text-muted-foreground">
                Yes, all plans come with a 14-day free trial. No credit card required.
              </p>
            </div>
            <div className="p-4 bg-muted/30 rounded-lg">
              <h4 className="font-medium mb-2">What about data security?</h4>
              <p className="text-sm text-muted-foreground">
                All plans include enterprise-grade security with encryption at rest and in transit.
              </p>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="mt-16 text-center">
          <p className="text-muted-foreground mb-4">
            Need help choosing the right plan?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="outline">
              Schedule a Demo
            </Button>
            <Button>
              Contact Sales
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
