import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Code,
  Database,
  Cloud,
  Monitor,
  Layers,
  Cpu
} from "lucide-react"

const techStack = {
  frontend: {
    title: "Frontend",
    icon: Code,
    description: "Modern, responsive user interfaces",
    technologies: [
      { name: "Next.js 15", description: "React framework with App Router", version: "15.x", status: "Latest" },
      { name: "React 19", description: "UI library with concurrent features", version: "19.x", status: "Latest" },
      { name: "TypeScript", description: "Type-safe JavaScript", version: "5.x", status: "Stable" },
      { name: "Tailwind CSS 4", description: "Utility-first CSS framework", version: "4.x", status: "Latest" },
      { name: "shadcn/ui", description: "Accessible component library", version: "Latest", status: "Modern" },
      { name: "<PERSON>di<PERSON>", description: "Unstyled UI primitives", version: "Latest", status: "Accessible" }
    ]
  },
  backend: {
    title: "Backend",
    icon: Database,
    description: "High-performance API and data processing",
    technologies: [
      { name: "FastAPI", description: "Modern Python web framework", version: "Latest", status: "Fast" },
      { name: "Python 3.11+", description: "Latest Python features", version: "3.11+", status: "Modern" },
      { name: "PostgreSQL", description: "Advanced relational database", version: "15+", status: "Reliable" },
      { name: "MongoDB", description: "Document database", version: "6.x", status: "Flexible" },
      { name: "Redis", description: "In-memory data store", version: "7.x", status: "Fast" },
      { name: "Celery", description: "Distributed task queue", version: "Latest", status: "Scalable" }
    ]
  },
  aiml: {
    title: "AI/ML",
    icon: Cpu,
    description: "Cutting-edge machine learning frameworks",
    technologies: [
      { name: "AutoGluon", description: "Automated machine learning", version: "Latest", status: "AutoML" },
      { name: "PyTorch", description: "Deep learning framework", version: "2.x", status: "Popular" },
      { name: "TensorFlow", description: "ML platform", version: "2.x", status: "Enterprise" },
      { name: "NVIDIA RAPIDS", description: "GPU-accelerated computing", version: "Latest", status: "Fast" },
      { name: "Transformers", description: "NLP and generative AI", version: "4.x", status: "SOTA" },
      { name: "Qiskit", description: "Quantum computing", version: "Latest", status: "Quantum" }
    ]
  },
  infrastructure: {
    title: "Infrastructure",
    icon: Cloud,
    description: "Scalable cloud-native deployment",
    technologies: [
      { name: "Kubernetes", description: "Container orchestration", version: "1.28+", status: "Production" },
      { name: "Docker", description: "Containerization platform", version: "Latest", status: "Standard" },
      { name: "Terraform", description: "Infrastructure as Code", version: "1.x", status: "IaC" },
      { name: "Nginx", description: "Load balancer & reverse proxy", version: "Latest", status: "Reliable" },
      { name: "AWS/GCP/Azure", description: "Multi-cloud support", version: "Latest", status: "Global" },
      { name: "Helm", description: "Kubernetes package manager", version: "3.x", status: "K8s" }
    ]
  },
  monitoring: {
    title: "Monitoring",
    icon: Monitor,
    description: "Comprehensive observability and analytics",
    technologies: [
      { name: "Prometheus", description: "Metrics collection", version: "Latest", status: "Standard" },
      { name: "Grafana", description: "Visualization & dashboards", version: "Latest", status: "Visual" },
      { name: "ELK Stack", description: "Centralized logging", version: "8.x", status: "Complete" },
      { name: "Jaeger", description: "Distributed tracing", version: "Latest", status: "Tracing" },
      { name: "MLflow", description: "ML experiment tracking", version: "Latest", status: "MLOps" },
      { name: "Weights & Biases", description: "ML monitoring", version: "Latest", status: "Advanced" }
    ]
  },
  devops: {
    title: "DevOps",
    icon: Layers,
    description: "Automated CI/CD and deployment",
    technologies: [
      { name: "GitHub Actions", description: "CI/CD automation", version: "Latest", status: "Automated" },
      { name: "ArgoCD", description: "GitOps deployment", version: "Latest", status: "GitOps" },
      { name: "SonarQube", description: "Code quality analysis", version: "Latest", status: "Quality" },
      { name: "Trivy", description: "Security scanning", version: "Latest", status: "Secure" },
      { name: "Renovate", description: "Dependency updates", version: "Latest", status: "Updated" },
      { name: "Semantic Release", description: "Automated releases", version: "Latest", status: "Automated" }
    ]
  }
}

const statusColors = {
  "Latest": "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  "Modern": "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  "Fast": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  "Stable": "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  "Popular": "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  "Enterprise": "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300",
  "Production": "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
  "AutoML": "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
  "SOTA": "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300",
  "Quantum": "bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-300",
  "Standard": "bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-300",
  "IaC": "bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-300",
  "Global": "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300",
  "K8s": "bg-violet-100 text-violet-800 dark:bg-violet-900 dark:text-violet-300",
  "Visual": "bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-300",
  "Complete": "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300",
  "Tracing": "bg-lime-100 text-lime-800 dark:bg-lime-900 dark:text-lime-300",
  "MLOps": "bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-300",
  "Advanced": "bg-fuchsia-100 text-fuchsia-800 dark:bg-fuchsia-900 dark:text-fuchsia-300",
  "Automated": "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  "GitOps": "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  "Quality": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  "Secure": "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
  "Updated": "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  "Reliable": "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  "Flexible": "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300",
  "Scalable": "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
  "Accessible": "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300"
}

export function TechStackSection() {
  return (
    <section className="py-24 bg-muted/30">
      <div className="container">
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="px-3 py-1">
            Technology Stack
          </Badge>
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            Built with
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              {" "}cutting-edge tech
            </span>
          </h2>
          <p className="mx-auto max-w-[700px] text-lg text-muted-foreground">
            Our platform leverages the latest and most powerful technologies to deliver 
            unmatched performance, scalability, and reliability.
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {Object.entries(techStack).map(([key, stack]) => (
            <Card key={key} className="border-0 bg-background/50 backdrop-blur">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                    <stack.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{stack.title}</CardTitle>
                    <CardDescription className="text-sm">
                      {stack.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {stack.technologies.map((tech, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-sm">{tech.name}</span>
                          <Badge 
                            variant="secondary" 
                            className={`text-xs px-2 py-0.5 ${statusColors[tech.status as keyof typeof statusColors] || statusColors.Standard}`}
                          >
                            {tech.status}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {tech.description}
                        </p>
                      </div>
                      <span className="text-xs text-muted-foreground ml-2">
                        {tech.version}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Performance metrics */}
        <div className="mt-16 grid grid-cols-2 gap-8 md:grid-cols-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">50%</div>
            <div className="text-sm text-muted-foreground">Faster Builds</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">99.9%</div>
            <div className="text-sm text-muted-foreground">Uptime SLA</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">10x</div>
            <div className="text-sm text-muted-foreground">Performance Boost</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">24/7</div>
            <div className="text-sm text-muted-foreground">Monitoring</div>
          </div>
        </div>
      </div>
    </section>
  )
}
