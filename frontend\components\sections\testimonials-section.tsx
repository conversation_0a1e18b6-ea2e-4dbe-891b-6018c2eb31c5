"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Star,
  Quote,
  ChevronLeft,
  ChevronRight,
  Building,
  Users,
  TrendingUp
} from "lucide-react"

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Chief Data Officer",
    company: "TechCorp Inc.",
    industry: "Technology",
    avatar: "/avatars/sarah-chen.jpg",
    rating: 5,
    quote: "This platform transformed our AI capabilities overnight. We went from months of development to production-ready models in days. The no-code interface is incredibly intuitive, and the enterprise features give us the security and scalability we need.",
    metrics: {
      timeSaved: "80%",
      modelsDeployed: "50+",
      roi: "300%"
    },
    featured: true
  },
  {
    id: 2,
    name: "Dr. <PERSON>",
    role: "Head of AI Research",
    company: "MedTech Solutions",
    industry: "Healthcare",
    avatar: "/avatars/michael-rodriguez.jpg",
    rating: 5,
    quote: "The computer vision capabilities are outstanding. We're using it for medical image analysis and the accuracy is better than our previous custom solutions. The HIPAA compliance features were crucial for our deployment.",
    metrics: {
      accuracy: "99.2%",
      patientsHelped: "10K+",
      costReduction: "60%"
    },
    featured: false
  },
  {
    id: 3,
    name: "Emily Watson",
    role: "VP of Analytics",
    company: "RetailMax",
    industry: "Retail",
    avatar: "/avatars/emily-watson.jpg",
    rating: 5,
    quote: "The time series forecasting has revolutionized our inventory management. We've reduced stockouts by 40% and improved our demand prediction accuracy significantly. The ROI was immediate.",
    metrics: {
      stockoutReduction: "40%",
      accuracyImprovement: "35%",
      revenueIncrease: "25%"
    },
    featured: true
  },
  {
    id: 4,
    name: "James Park",
    role: "CTO",
    company: "FinanceFlow",
    industry: "Financial Services",
    avatar: "/avatars/james-park.jpg",
    rating: 5,
    quote: "The fraud detection models we built using this platform are incredibly sophisticated. The AutoML capabilities found patterns our data scientists missed. It's like having a team of ML experts at your fingertips.",
    metrics: {
      fraudDetection: "99.8%",
      falsePositives: "-70%",
      processingSpeed: "10x"
    },
    featured: false
  },
  {
    id: 5,
    name: "Dr. Lisa Thompson",
    role: "Research Director",
    company: "EduTech Institute",
    industry: "Education",
    avatar: "/avatars/lisa-thompson.jpg",
    rating: 5,
    quote: "We're using the NLP capabilities for automated essay grading and student feedback. The accuracy is remarkable, and it's freed up our teachers to focus on more meaningful interactions with students.",
    metrics: {
      gradingAccuracy: "96%",
      timeReduction: "75%",
      studentSatisfaction: "92%"
    },
    featured: false
  },
  {
    id: 6,
    name: "Robert Kim",
    role: "Head of Operations",
    company: "ManufacturingPro",
    industry: "Manufacturing",
    avatar: "/avatars/robert-kim.jpg",
    rating: 5,
    quote: "The predictive maintenance models have saved us millions in downtime costs. The platform's ability to handle our IoT sensor data and provide real-time insights is game-changing.",
    metrics: {
      downtimeReduction: "65%",
      costSavings: "$2M+",
      efficiency: "+45%"
    },
    featured: true
  }
]

const companyLogos = [
  { name: "TechCorp", logo: "🏢" },
  { name: "MedTech", logo: "🏥" },
  { name: "RetailMax", logo: "🛍️" },
  { name: "FinanceFlow", logo: "🏦" },
  { name: "EduTech", logo: "🎓" },
  { name: "ManufacturingPro", logo: "🏭" }
]

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  const featuredTestimonials = testimonials.filter(t => t.featured)
  const currentTestimonial = featuredTestimonials[currentIndex]

  useEffect(() => {
    if (!isAutoPlaying) return

    const timer = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % featuredTestimonials.length)
    }, 5000)

    return () => clearInterval(timer)
  }, [isAutoPlaying, featuredTestimonials.length])

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % featuredTestimonials.length)
    setIsAutoPlaying(false)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + featuredTestimonials.length) % featuredTestimonials.length)
    setIsAutoPlaying(false)
  }

  return (
    <section className="py-24 bg-muted/30">
      <div className="container">
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="px-3 py-1">
            Customer Success
          </Badge>
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            Trusted by
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              {" "}industry leaders
            </span>
          </h2>
          <p className="mx-auto max-w-[700px] text-lg text-muted-foreground">
            See how organizations across industries are transforming their operations 
            with our AI/ML platform.
          </p>
        </div>

        {/* Featured testimonial */}
        <div className="relative mb-16">
          <Card className="border-0 bg-background/50 backdrop-blur">
            <CardContent className="p-8 md:p-12">
              <div className="grid gap-8 lg:grid-cols-2 lg:gap-12">
                <div className="space-y-6">
                  <Quote className="h-8 w-8 text-primary/60" />
                  <blockquote className="text-lg md:text-xl leading-relaxed">
                    "{currentTestimonial.quote}"
                  </blockquote>
                  
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={currentTestimonial.avatar} alt={currentTestimonial.name} />
                      <AvatarFallback>
                        {currentTestimonial.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-semibold">{currentTestimonial.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {currentTestimonial.role} at {currentTestimonial.company}
                      </div>
                      <div className="flex items-center mt-1">
                        {Array.from({ length: currentTestimonial.rating }).map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  <div>
                    <h4 className="font-semibold mb-4">Impact Metrics</h4>
                    <div className="grid grid-cols-1 gap-4">
                      {Object.entries(currentTestimonial.metrics).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                          <span className="text-sm capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </span>
                          <span className="font-semibold text-primary">{value}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">{currentTestimonial.industry}</Badge>
                    <Badge variant="outline">Verified Customer</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Navigation */}
          <div className="flex items-center justify-center space-x-4 mt-6">
            <Button variant="outline" size="sm" onClick={prevTestimonial}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <div className="flex space-x-2">
              {featuredTestimonials.map((_, index) => (
                <button
                  key={index}
                  className={`h-2 w-2 rounded-full transition-colors ${
                    index === currentIndex ? 'bg-primary' : 'bg-muted-foreground/30'
                  }`}
                  onClick={() => {
                    setCurrentIndex(index)
                    setIsAutoPlaying(false)
                  }}
                />
              ))}
            </div>

            <Button variant="outline" size="sm" onClick={nextTestimonial}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* All testimonials grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-16">
          {testimonials.slice(0, 6).map((testimonial) => (
            <Card key={testimonial.id} className="border-0 bg-background/30 backdrop-blur">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                    <AvatarFallback>
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium text-sm">{testimonial.name}</div>
                    <div className="text-xs text-muted-foreground">{testimonial.company}</div>
                  </div>
                  <div className="flex">
                    {Array.from({ length: testimonial.rating }).map((_, i) => (
                      <Star key={i} className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                </div>
                <blockquote className="text-sm leading-relaxed">
                  "{testimonial.quote.slice(0, 120)}..."
                </blockquote>
                <Badge variant="secondary" className="mt-3 text-xs">
                  {testimonial.industry}
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Company logos */}
        <div className="text-center">
          <p className="text-sm text-muted-foreground mb-8">
            Trusted by companies across industries
          </p>
          <div className="flex flex-wrap items-center justify-center gap-8">
            {companyLogos.map((company, index) => (
              <div
                key={index}
                className="flex items-center space-x-2 opacity-60 hover:opacity-100 transition-opacity"
              >
                <span className="text-2xl">{company.logo}</span>
                <span className="text-sm font-medium">{company.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="mt-16 grid grid-cols-2 gap-8 md:grid-cols-4 text-center">
          <div>
            <div className="text-3xl font-bold text-primary">500+</div>
            <div className="text-sm text-muted-foreground">Enterprise Clients</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary">4.9/5</div>
            <div className="text-sm text-muted-foreground">Customer Rating</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary">99.9%</div>
            <div className="text-sm text-muted-foreground">Customer Satisfaction</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary">24/7</div>
            <div className="text-sm text-muted-foreground">Support Available</div>
          </div>
        </div>
      </div>
    </section>
  )
}
