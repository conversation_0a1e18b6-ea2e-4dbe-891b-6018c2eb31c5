# Backend Deployment Configuration
# Production-ready Kubernetes deployment with auto-scaling and health checks

apiVersion: apps/v1
kind: Deployment
metadata:
  name: aiml-platform-backend
  namespace: aiml-platform
  labels:
    app: aiml-platform-backend
    component: backend
    version: v2.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: aiml-platform-backend
  template:
    metadata:
      labels:
        app: aiml-platform-backend
        component: backend
        version: v2.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: aiml-platform-backend
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: backend
        image: ghcr.io/aiml-platform/backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        - name: DEBUG
          value: "false"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8000"
        - name: WORKERS
          value: "4"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: aiml-platform-secrets
              key: database-url
        - name: MONGODB_URL
          valueFrom:
            secretKeyRef:
              name: aiml-platform-secrets
              key: mongodb-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: aiml-platform-secrets
              key: redis-url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: aiml-platform-secrets
              key: secret-key
        - name: CELERY_BROKER_URL
          valueFrom:
            secretKeyRef:
              name: aiml-platform-secrets
              key: celery-broker-url
        - name: CELERY_RESULT_BACKEND
          valueFrom:
            secretKeyRef:
              name: aiml-platform-secrets
              key: celery-result-backend
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: aiml-platform-secrets
              key: openai-api-key
              optional: true
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: aiml-platform-secrets
              key: anthropic-api-key
              optional: true
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: aiml-platform-secrets
              key: aws-access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: aiml-platform-secrets
              key: aws-secret-access-key
        - name: AWS_S3_BUCKET
          valueFrom:
            configMapKeyRef:
              name: aiml-platform-config
              key: aws-s3-bucket
        - name: AWS_REGION
          valueFrom:
            configMapKeyRef:
              name: aiml-platform-config
              key: aws-region
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: ml-models
          mountPath: /app/models
        - name: ml-data
          mountPath: /app/data
        - name: logs
          mountPath: /app/logs
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: ml-models
        persistentVolumeClaim:
          claimName: ml-models-pvc
      - name: ml-data
        persistentVolumeClaim:
          claimName: ml-data-pvc
      - name: logs
        emptyDir: {}
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "node-type"
        operator: "Equal"
        value: "compute"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - aiml-platform-backend
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: aiml-platform-backend-service
  namespace: aiml-platform
  labels:
    app: aiml-platform-backend
    component: backend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: http
    protocol: TCP
    name: http
  selector:
    app: aiml-platform-backend

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: aiml-platform-backend-hpa
  namespace: aiml-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: aiml-platform-backend
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: aiml-platform-backend-pdb
  namespace: aiml-platform
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: aiml-platform-backend

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: aiml-platform-backend
  namespace: aiml-platform
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/aiml-platform-backend-role

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: aiml-platform-backend-netpol
  namespace: aiml-platform
spec:
  podSelector:
    matchLabels:
      app: aiml-platform-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: aiml-platform-frontend
    - podSelector:
        matchLabels:
          app: nginx-ingress
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 27017 # MongoDB
    - protocol: TCP
      port: 443   # HTTPS
    - protocol: TCP
      port: 53    # DNS
    - protocol: UDP
      port: 53    # DNS
