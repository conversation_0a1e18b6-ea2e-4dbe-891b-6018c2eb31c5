"""
No Code AI/ML Platform - Core Modules
====================================

This package contains all the specialized modules for the comprehensive
AI/ML/DL/RL/GenAI Data Science platform.

Modules:
- computer_vision: Complete CV pipeline with SOTA models
- nlp: Advanced NLP with transformers and LLMs
- time_series: Comprehensive time series analysis
- reinforcement_learning: Full RL environment and training
- generative_ai: GenAI tools and model integration
- graph_neural_networks: GNN implementations
- quantum_ml: Quantum machine learning
- federated_learning: Distributed ML
- mlops: Model lifecycle management
- advanced_analytics: Statistical and interpretability tools
- edge_ai: Model optimization and deployment
- audio_processing: Speech and audio analysis
"""

__version__ = "1.0.0"
__author__ = "AI Platform Team"

# Import all modules for easy access
from . import computer_vision
from . import nlp
from . import time_series
from . import reinforcement_learning
from . import generative_ai
from . import graph_neural_networks
from . import quantum_ml
from . import federated_learning
from . import mlops
from . import advanced_analytics
from . import edge_ai
from . import audio_processing

__all__ = [
    "computer_vision",
    "nlp", 
    "time_series",
    "reinforcement_learning",
    "generative_ai",
    "graph_neural_networks",
    "quantum_ml",
    "federated_learning",
    "mlops",
    "advanced_analytics",
    "edge_ai",
    "audio_processing"
]
