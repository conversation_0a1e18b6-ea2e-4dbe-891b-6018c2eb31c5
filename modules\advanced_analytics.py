"""
Advanced Analytics Module
=========================

Advanced analytics capabilities including:
- Model Interpretability (SHAP, LIME)
- Feature Importance Analysis
- Statistical Analysis
- A/B Testing
- Causal Inference
- Survival Analysis
- Bayesian Analysis
"""

import numpy as np
import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, List, Any, Optional
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Import libraries with fallbacks
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False

try:
    import lime
    from lime.lime_tabular import LimeTabularExplainer
    LIME_AVAILABLE = True
except ImportError:
    LIME_AVAILABLE = False

try:
    from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, mean_squared_error
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


class ModelExplainer:
    """Model interpretability and explanation."""
    
    def __init__(self):
        self.explainers = {}
        
    def create_shap_explainer(self, model, X_train: pd.DataFrame, model_type: str = "tree"):
        """Create SHAP explainer."""
        if not SHAP_AVAILABLE:
            st.error("SHAP not available")
            return None
            
        try:
            if model_type == "tree":
                explainer = shap.TreeExplainer(model)
            elif model_type == "linear":
                explainer = shap.LinearExplainer(model, X_train)
            else:
                explainer = shap.Explainer(model, X_train)
            
            self.explainers['shap'] = explainer
            return explainer
        except Exception as e:
            st.error(f"Error creating SHAP explainer: {str(e)}")
            return None
    
    def create_lime_explainer(self, X_train: pd.DataFrame, mode: str = "classification"):
        """Create LIME explainer."""
        if not LIME_AVAILABLE:
            st.error("LIME not available")
            return None
            
        try:
            explainer = LimeTabularExplainer(
                X_train.values,
                feature_names=X_train.columns,
                mode=mode,
                discretize_continuous=True
            )
            
            self.explainers['lime'] = explainer
            return explainer
        except Exception as e:
            st.error(f"Error creating LIME explainer: {str(e)}")
            return None
    
    def explain_prediction_shap(self, X_sample: pd.DataFrame):
        """Explain prediction using SHAP."""
        if 'shap' not in self.explainers:
            st.error("SHAP explainer not initialized")
            return None
            
        try:
            explainer = self.explainers['shap']
            shap_values = explainer.shap_values(X_sample)
            
            return shap_values
        except Exception as e:
            st.error(f"Error explaining with SHAP: {str(e)}")
            return None
    
    def explain_prediction_lime(self, model, X_sample: np.ndarray, instance_idx: int = 0):
        """Explain prediction using LIME."""
        if 'lime' not in self.explainers:
            st.error("LIME explainer not initialized")
            return None
            
        try:
            explainer = self.explainers['lime']
            explanation = explainer.explain_instance(
                X_sample[instance_idx], 
                model.predict_proba if hasattr(model, 'predict_proba') else model.predict,
                num_features=len(X_sample[instance_idx])
            )
            
            return explanation
        except Exception as e:
            st.error(f"Error explaining with LIME: {str(e)}")
            return None


class StatisticalAnalyzer:
    """Statistical analysis tools."""
    
    def __init__(self):
        pass
    
    def correlation_analysis(self, data: pd.DataFrame):
        """Perform correlation analysis."""
        try:
            # Calculate correlation matrix
            corr_matrix = data.corr()
            
            # Find highly correlated pairs
            high_corr_pairs = []
            for i in range(len(corr_matrix.columns)):
                for j in range(i+1, len(corr_matrix.columns)):
                    corr_val = corr_matrix.iloc[i, j]
                    if abs(corr_val) > 0.7:  # Threshold for high correlation
                        high_corr_pairs.append({
                            'feature1': corr_matrix.columns[i],
                            'feature2': corr_matrix.columns[j],
                            'correlation': corr_val
                        })
            
            return {
                'correlation_matrix': corr_matrix,
                'high_correlations': high_corr_pairs
            }
        except Exception as e:
            st.error(f"Error in correlation analysis: {str(e)}")
            return None
    
    def hypothesis_testing(self, group1: np.ndarray, group2: np.ndarray, test_type: str = "ttest"):
        """Perform hypothesis testing."""
        try:
            if test_type == "ttest":
                statistic, p_value = stats.ttest_ind(group1, group2)
                test_name = "Independent t-test"
            elif test_type == "mannwhitney":
                statistic, p_value = stats.mannwhitneyu(group1, group2)
                test_name = "Mann-Whitney U test"
            elif test_type == "ks":
                statistic, p_value = stats.ks_2samp(group1, group2)
                test_name = "Kolmogorov-Smirnov test"
            else:
                return None
            
            return {
                'test_name': test_name,
                'statistic': statistic,
                'p_value': p_value,
                'significant': p_value < 0.05
            }
        except Exception as e:
            st.error(f"Error in hypothesis testing: {str(e)}")
            return None
    
    def distribution_analysis(self, data: pd.Series):
        """Analyze data distribution."""
        try:
            # Basic statistics
            stats_dict = {
                'mean': data.mean(),
                'median': data.median(),
                'std': data.std(),
                'skewness': stats.skew(data.dropna()),
                'kurtosis': stats.kurtosis(data.dropna())
            }
            
            # Normality test
            shapiro_stat, shapiro_p = stats.shapiro(data.dropna().sample(min(5000, len(data))))
            
            stats_dict.update({
                'shapiro_statistic': shapiro_stat,
                'shapiro_p_value': shapiro_p,
                'is_normal': shapiro_p > 0.05
            })
            
            return stats_dict
        except Exception as e:
            st.error(f"Error in distribution analysis: {str(e)}")
            return None


class ABTester:
    """A/B testing framework."""
    
    def __init__(self):
        pass
    
    def design_ab_test(self, baseline_rate: float, minimum_effect: float, 
                      alpha: float = 0.05, power: float = 0.8):
        """Design A/B test and calculate sample size."""
        try:
            from statsmodels.stats.power import ttest_power
            from statsmodels.stats.proportion import proportions_ztest
            
            # Calculate effect size
            effect_size = minimum_effect / np.sqrt(baseline_rate * (1 - baseline_rate))
            
            # Calculate required sample size
            sample_size = ttest_power(effect_size, power, alpha, alternative='two-sided')
            
            return {
                'baseline_rate': baseline_rate,
                'minimum_effect': minimum_effect,
                'effect_size': effect_size,
                'alpha': alpha,
                'power': power,
                'required_sample_size': int(sample_size * 100)  # Rough estimate
            }
        except Exception as e:
            st.error(f"Error designing A/B test: {str(e)}")
            return None
    
    def analyze_ab_test(self, control_conversions: int, control_total: int,
                       treatment_conversions: int, treatment_total: int):
        """Analyze A/B test results."""
        try:
            # Calculate conversion rates
            control_rate = control_conversions / control_total
            treatment_rate = treatment_conversions / treatment_total
            
            # Calculate lift
            lift = (treatment_rate - control_rate) / control_rate * 100
            
            # Statistical test
            from statsmodels.stats.proportion import proportions_ztest
            
            counts = np.array([control_conversions, treatment_conversions])
            nobs = np.array([control_total, treatment_total])
            
            z_stat, p_value = proportions_ztest(counts, nobs)
            
            # Confidence interval for lift
            se_diff = np.sqrt(
                control_rate * (1 - control_rate) / control_total +
                treatment_rate * (1 - treatment_rate) / treatment_total
            )
            
            ci_lower = (treatment_rate - control_rate) - 1.96 * se_diff
            ci_upper = (treatment_rate - control_rate) + 1.96 * se_diff
            
            return {
                'control_rate': control_rate,
                'treatment_rate': treatment_rate,
                'lift_percent': lift,
                'z_statistic': z_stat,
                'p_value': p_value,
                'significant': p_value < 0.05,
                'confidence_interval': (ci_lower, ci_upper)
            }
        except Exception as e:
            st.error(f"Error analyzing A/B test: {str(e)}")
            return None


class CausalInference:
    """Causal inference tools."""
    
    def __init__(self):
        pass
    
    def propensity_score_matching(self, data: pd.DataFrame, treatment_col: str, 
                                 outcome_col: str, covariates: List[str]):
        """Perform propensity score matching."""
        try:
            from sklearn.linear_model import LogisticRegression
            from sklearn.neighbors import NearestNeighbors
            
            # Fit propensity score model
            X = data[covariates]
            y = data[treatment_col]
            
            ps_model = LogisticRegression()
            ps_model.fit(X, y)
            
            # Calculate propensity scores
            propensity_scores = ps_model.predict_proba(X)[:, 1]
            data_with_ps = data.copy()
            data_with_ps['propensity_score'] = propensity_scores
            
            # Matching (simplified 1:1 nearest neighbor)
            treated = data_with_ps[data_with_ps[treatment_col] == 1]
            control = data_with_ps[data_with_ps[treatment_col] == 0]
            
            # Find matches
            nn = NearestNeighbors(n_neighbors=1)
            nn.fit(control[['propensity_score']])
            
            distances, indices = nn.kneighbors(treated[['propensity_score']])
            
            matched_control = control.iloc[indices.flatten()]
            matched_treated = treated
            
            # Calculate treatment effect
            ate = matched_treated[outcome_col].mean() - matched_control[outcome_col].mean()
            
            return {
                'average_treatment_effect': ate,
                'matched_treated': matched_treated,
                'matched_control': matched_control,
                'propensity_scores': propensity_scores
            }
        except Exception as e:
            st.error(f"Error in propensity score matching: {str(e)}")
            return None


def render_advanced_analytics_page():
    """Render the advanced analytics page."""
    st.title("📊 Advanced Analytics")
    st.markdown("### Statistical Analysis & Model Interpretability")
    
    # Sidebar for analytics options
    analytics_task = st.sidebar.selectbox(
        "Select Analytics Task:",
        [
            "Model Interpretability",
            "Statistical Analysis", 
            "A/B Testing",
            "Causal Inference",
            "Feature Analysis"
        ]
    )
    
    if analytics_task == "Model Interpretability":
        st.markdown("#### Model Interpretability")
        
        # Generate sample data and model
        if st.button("Generate Sample Model"):
            if SKLEARN_AVAILABLE:
                # Create sample dataset
                np.random.seed(42)
                n_samples = 1000
                X = pd.DataFrame({
                    'feature_1': np.random.normal(0, 1, n_samples),
                    'feature_2': np.random.normal(0, 1, n_samples),
                    'feature_3': np.random.normal(0, 1, n_samples),
                    'feature_4': np.random.normal(0, 1, n_samples)
                })
                y = (X['feature_1'] + 0.5 * X['feature_2'] - 0.3 * X['feature_3'] + 
                     np.random.normal(0, 0.1, n_samples) > 0).astype(int)
                
                # Train model
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
                model = RandomForestClassifier(n_estimators=100, random_state=42)
                model.fit(X_train, y_train)
                
                # Store in session state
                st.session_state['interp_model'] = model
                st.session_state['interp_X_train'] = X_train
                st.session_state['interp_X_test'] = X_test
                st.session_state['interp_y_test'] = y_test
                
                accuracy = accuracy_score(y_test, model.predict(X_test))
                st.success(f"✅ Model trained! Accuracy: {accuracy:.3f}")
        
        if 'interp_model' in st.session_state:
            model = st.session_state['interp_model']
            X_train = st.session_state['interp_X_train']
            X_test = st.session_state['interp_X_test']
            
            explainer = ModelExplainer()
            
            method = st.selectbox("Explanation Method:", ["Feature Importance", "SHAP", "LIME"])
            
            if method == "Feature Importance":
                if hasattr(model, 'feature_importances_'):
                    importance_df = pd.DataFrame({
                        'feature': X_train.columns,
                        'importance': model.feature_importances_
                    }).sort_values('importance', ascending=False)
                    
                    fig = px.bar(importance_df, x='importance', y='feature', 
                               orientation='h', title="Feature Importance")
                    st.plotly_chart(fig, use_container_width=True)
            
            elif method == "SHAP":
                if SHAP_AVAILABLE:
                    if st.button("Generate SHAP Explanation"):
                        with st.spinner("Generating SHAP values..."):
                            shap_explainer = explainer.create_shap_explainer(model, X_train, "tree")
                            if shap_explainer:
                                shap_values = explainer.explain_prediction_shap(X_test.head(10))
                                if shap_values is not None:
                                    st.success("✅ SHAP explanation generated!")
                                    st.info("SHAP waterfall plot would be displayed here")
                else:
                    st.error("SHAP library not available")
            
            elif method == "LIME":
                if LIME_AVAILABLE:
                    if st.button("Generate LIME Explanation"):
                        with st.spinner("Generating LIME explanation..."):
                            lime_explainer = explainer.create_lime_explainer(X_train, "classification")
                            if lime_explainer:
                                explanation = explainer.explain_prediction_lime(
                                    model, X_test.values, 0
                                )
                                if explanation:
                                    st.success("✅ LIME explanation generated!")
                                    st.info("LIME explanation plot would be displayed here")
                else:
                    st.error("LIME library not available")
    
    elif analytics_task == "Statistical Analysis":
        st.markdown("#### Statistical Analysis")
        
        # File upload for analysis
        uploaded_file = st.file_uploader("Upload data for analysis", type=['csv'])
        
        if uploaded_file is not None:
            data = pd.read_csv(uploaded_file)
            st.dataframe(data.head())
            
            analyzer = StatisticalAnalyzer()
            
            analysis_type = st.selectbox(
                "Analysis Type:",
                ["Correlation Analysis", "Distribution Analysis", "Hypothesis Testing"]
            )
            
            if analysis_type == "Correlation Analysis":
                numeric_cols = data.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 1:
                    if st.button("Run Correlation Analysis"):
                        results = analyzer.correlation_analysis(data[numeric_cols])
                        
                        if results:
                            # Plot correlation heatmap
                            fig = px.imshow(results['correlation_matrix'], 
                                          title="Correlation Matrix",
                                          color_continuous_scale='RdBu_r')
                            st.plotly_chart(fig, use_container_width=True)
                            
                            # Show high correlations
                            if results['high_correlations']:
                                st.markdown("**High Correlations (|r| > 0.7):**")
                                high_corr_df = pd.DataFrame(results['high_correlations'])
                                st.dataframe(high_corr_df)
                else:
                    st.warning("Need at least 2 numeric columns for correlation analysis")
            
            elif analysis_type == "Distribution Analysis":
                numeric_cols = data.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    selected_col = st.selectbox("Select column:", numeric_cols)
                    
                    if st.button("Analyze Distribution"):
                        results = analyzer.distribution_analysis(data[selected_col])
                        
                        if results:
                            col1, col2 = st.columns(2)
                            
                            with col1:
                                st.markdown("**Distribution Statistics:**")
                                st.write(f"Mean: {results['mean']:.3f}")
                                st.write(f"Median: {results['median']:.3f}")
                                st.write(f"Std Dev: {results['std']:.3f}")
                                st.write(f"Skewness: {results['skewness']:.3f}")
                                st.write(f"Kurtosis: {results['kurtosis']:.3f}")
                            
                            with col2:
                                st.markdown("**Normality Test:**")
                                st.write(f"Shapiro-Wilk p-value: {results['shapiro_p_value']:.4f}")
                                normal_status = "Normal" if results['is_normal'] else "Not Normal"
                                st.write(f"Distribution: {normal_status}")
                            
                            # Plot histogram
                            fig = px.histogram(data, x=selected_col, 
                                             title=f"Distribution of {selected_col}")
                            st.plotly_chart(fig, use_container_width=True)
    
    elif analytics_task == "A/B Testing":
        st.markdown("#### A/B Testing")
        
        tab1, tab2 = st.tabs(["Test Design", "Results Analysis"])
        
        with tab1:
            st.markdown("**A/B Test Design**")
            
            col1, col2 = st.columns(2)
            with col1:
                baseline_rate = st.slider("Baseline Conversion Rate:", 0.01, 0.5, 0.1)
                minimum_effect = st.slider("Minimum Detectable Effect:", 0.001, 0.1, 0.02)
            
            with col2:
                alpha = st.slider("Significance Level (α):", 0.01, 0.1, 0.05)
                power = st.slider("Statistical Power:", 0.7, 0.95, 0.8)
            
            if st.button("Design Test"):
                tester = ABTester()
                design = tester.design_ab_test(baseline_rate, minimum_effect, alpha, power)
                
                if design:
                    st.markdown("**Test Design Results:**")
                    st.write(f"Required sample size per group: {design['required_sample_size']:,}")
                    st.write(f"Effect size: {design['effect_size']:.3f}")
                    st.write(f"Total participants needed: {design['required_sample_size'] * 2:,}")
        
        with tab2:
            st.markdown("**A/B Test Results Analysis**")
            
            col1, col2 = st.columns(2)
            with col1:
                st.markdown("**Control Group**")
                control_conversions = st.number_input("Control Conversions:", min_value=0, value=85)
                control_total = st.number_input("Control Total:", min_value=1, value=1000)
            
            with col2:
                st.markdown("**Treatment Group**")
                treatment_conversions = st.number_input("Treatment Conversions:", min_value=0, value=95)
                treatment_total = st.number_input("Treatment Total:", min_value=1, value=1000)
            
            if st.button("Analyze Results"):
                tester = ABTester()
                results = tester.analyze_ab_test(
                    control_conversions, control_total,
                    treatment_conversions, treatment_total
                )
                
                if results:
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Control Rate", f"{results['control_rate']:.3f}")
                    with col2:
                        st.metric("Treatment Rate", f"{results['treatment_rate']:.3f}")
                    with col3:
                        st.metric("Lift", f"{results['lift_percent']:.1f}%")
                    
                    significance = "Significant" if results['significant'] else "Not Significant"
                    st.write(f"**Result:** {significance} (p-value: {results['p_value']:.4f})")
    
    else:
        st.info(f"{analytics_task} implementation coming soon!")
    
    # Show analytics overview
    st.markdown("### 🚀 Advanced Analytics Capabilities")
    
    capabilities = {
        "Interpretability": ["SHAP", "LIME", "Feature Importance", "Partial Dependence"],
        "Statistical Tests": ["t-tests", "Chi-square", "ANOVA", "Non-parametric"],
        "Experimental": ["A/B Testing", "Multi-armed Bandits", "Causal Inference"],
        "Specialized": ["Survival Analysis", "Bayesian Methods", "Time Series"]
    }
    
    cols = st.columns(2)
    for i, (category, methods) in enumerate(capabilities.items()):
        with cols[i % 2]:
            st.markdown(f"**{category}**")
            for method in methods:
                st.markdown(f"• {method}")
