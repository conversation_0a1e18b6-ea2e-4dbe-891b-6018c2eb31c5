"""
Computer Vision Module - Refactored Architecture
==============================================

Modular computer vision capabilities with specialized components:
- Image Classification
- Object Detection  
- Image Segmentation
- Face Analysis
- OCR & Document Processing
- Image Enhancement
- Video Analysis
"""

from .classification import EnterpriseImageClassifier, ClassificationResult
from .detection import ObjectDetector, Detection, BoundingBox
from .segmentation import ImageSegmentation, SegmentationResult
from .face_analysis import FaceAnalysis
from .ocr import OCRProcessor
from .enhancement import ImageEnhancement
from .video import VideoAnalysis
from .core import ModelType, Framework

# Main computer vision interface
class ComputerVisionModule:
    """Unified computer vision module interface."""
    
    def __init__(self):
        self.classifier = EnterpriseImageClassifier()
        self.detector = ObjectDetector()
        self.segmentation = ImageSegmentation()
        self.face_analysis = FaceAnalysis()
        self.ocr = OCRProcessor()
        self.enhancement = ImageEnhancement()
        self.video = VideoAnalysis()
    
    async def initialize(self):
        """Initialize all CV components."""
        await self.classifier.initialize()
        await self.detector.initialize()
        await self.segmentation.initialize()
        await self.face_analysis.initialize()
        await self.ocr.initialize()
        await self.enhancement.initialize()
        await self.video.initialize()
    
    async def cleanup(self):
        """Cleanup all CV components."""
        await self.classifier.cleanup()
        await self.detector.cleanup()
        await self.segmentation.cleanup()
        await self.face_analysis.cleanup()
        await self.ocr.cleanup()
        await self.enhancement.cleanup()
        await self.video.cleanup()

# Streamlit interface function
def render_computer_vision_page():
    """Render the computer vision page in Streamlit."""
    from .ui import render_cv_interface
    render_cv_interface()

__all__ = [
    "ComputerVisionModule",
    "EnterpriseImageClassifier", 
    "ObjectDetector",
    "ImageSegmentation",
    "FaceAnalysis",
    "OCRProcessor", 
    "ImageEnhancement",
    "VideoAnalysis",
    "render_computer_vision_page"
]
