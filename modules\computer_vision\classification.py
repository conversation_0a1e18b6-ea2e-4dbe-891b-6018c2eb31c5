"""
Image Classification Module
==========================

Advanced image classification with multiple model support and enterprise features.
"""

import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Union
from PIL import Image
import torch
import torchvision.transforms as transforms
from transformers import AutoImageProcessor, AutoModelForImageClassification
import timm

from .core import ClassificationResult, CVConfig, ModelType, Framework, validate_image, normalize_image
from ..core.performance import performance_monitor, model_cache
from ..core.error_handling import handle_errors, error_context

class EnterpriseImageClassifier:
    """Enterprise-grade image classification with multiple model support."""
    
    def __init__(self, config: CVConfig = None):
        self.config = config or CVConfig()
        self.models = {}
        self.processors = {}
        self.device = self._get_device()
        
        # Supported models
        self.supported_models = {
            "resnet50": "microsoft/resnet-50",
            "vit_base": "google/vit-base-patch16-224",
            "efficientnet_b0": "google/efficientnet-b0",
            "swin_transformer": "microsoft/swin-base-patch4-window7-224",
            "convnext": "facebook/convnext-base-224",
            "deit": "facebook/deit-base-distilled-patch16-224",
            "beit": "microsoft/beit-base-patch16-224",
            "clip": "openai/clip-vit-base-patch32"
        }
    
    def _get_device(self) -> torch.device:
        """Get optimal device for inference."""
        if self.config.device == "auto":
            if torch.cuda.is_available() and self.config.enable_gpu:
                return torch.device("cuda")
            elif torch.backends.mps.is_available() and self.config.enable_gpu:
                return torch.device("mps")
            else:
                return torch.device("cpu")
        return torch.device(self.config.device)
    
    async def initialize(self):
        """Initialize the classifier with default models."""
        await self.load_model("resnet50")
    
    @performance_monitor
    @handle_errors(reraise=True)
    async def load_model(self, model_name: str, custom_model_path: str = None) -> bool:
        """Load a classification model."""
        async with error_context("load_classification_model", {"model_name": model_name}):
            if model_name in self.models:
                return True
            
            try:
                if custom_model_path:
                    # Load custom model
                    model = torch.load(custom_model_path, map_location=self.device)
                    processor = None
                elif model_name in self.supported_models:
                    # Load pre-trained model
                    model_path = self.supported_models[model_name]
                    
                    if "timm" in model_name:
                        # Use timm models
                        model = timm.create_model(model_path, pretrained=True)
                        processor = timm.data.resolve_data_config({}, model=model)
                    else:
                        # Use Hugging Face models
                        processor = AutoImageProcessor.from_pretrained(model_path)
                        model = AutoModelForImageClassification.from_pretrained(model_path)
                    
                    model = model.to(self.device)
                    model.eval()
                else:
                    raise ValueError(f"Unsupported model: {model_name}")
                
                self.models[model_name] = model
                self.processors[model_name] = processor
                
                return True
                
            except Exception as e:
                raise RuntimeError(f"Failed to load model {model_name}: {str(e)}")
    
    @performance_monitor
    @handle_errors(reraise=True)
    async def classify_image(
        self,
        image: Union[str, Image.Image, np.ndarray],
        model_name: str = "resnet50",
        top_k: int = 5,
        return_features: bool = False
    ) -> List[ClassificationResult]:
        """Classify a single image."""
        async with error_context("classify_image", {"model_name": model_name}):
            # Validate and normalize image
            if not validate_image(image):
                raise ValueError("Invalid image input")
            
            pil_image = normalize_image(image)
            
            # Ensure model is loaded
            if model_name not in self.models:
                await self.load_model(model_name)
            
            model = self.models[model_name]
            processor = self.processors[model_name]
            
            # Preprocess image
            if processor:
                inputs = processor(pil_image, return_tensors="pt").to(self.device)
            else:
                # Default preprocessing
                transform = transforms.Compose([
                    transforms.Resize((224, 224)),
                    transforms.ToTensor(),
                    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
                ])
                inputs = {"pixel_values": transform(pil_image).unsqueeze(0).to(self.device)}
            
            # Run inference
            with torch.no_grad():
                outputs = model(**inputs)
                
                if hasattr(outputs, 'logits'):
                    logits = outputs.logits
                else:
                    logits = outputs
                
                probabilities = torch.nn.functional.softmax(logits, dim=-1)
                top_k_probs, top_k_indices = torch.topk(probabilities, top_k)
            
            # Extract features if requested
            features = None
            if return_features and hasattr(outputs, 'last_hidden_state'):
                features = outputs.last_hidden_state.mean(dim=1).cpu().numpy()
            
            # Convert to results
            results = []
            for i in range(top_k):
                class_id = top_k_indices[0][i].item()
                confidence = top_k_probs[0][i].item()
                
                # Get class name (simplified - in production, use proper label mapping)
                class_name = f"class_{class_id}"
                if hasattr(model, 'config') and hasattr(model.config, 'id2label'):
                    class_name = model.config.id2label.get(class_id, class_name)
                
                result = ClassificationResult(
                    class_id=class_id,
                    class_name=class_name,
                    confidence=confidence,
                    features=features,
                    metadata={
                        "model_name": model_name,
                        "image_size": pil_image.size,
                        "device": str(self.device)
                    }
                )
                results.append(result)
            
            return results
    
    @performance_monitor
    @handle_errors(reraise=True)
    async def classify_batch(
        self,
        images: List[Union[str, Image.Image, np.ndarray]],
        model_name: str = "resnet50",
        top_k: int = 5,
        batch_size: int = None
    ) -> List[List[ClassificationResult]]:
        """Classify multiple images in batches."""
        async with error_context("classify_batch", {"model_name": model_name, "num_images": len(images)}):
            batch_size = batch_size or self.config.batch_size
            results = []
            
            for i in range(0, len(images), batch_size):
                batch = images[i:i + batch_size]
                batch_results = []
                
                # Process batch concurrently
                tasks = [
                    self.classify_image(image, model_name, top_k)
                    for image in batch
                ]
                
                batch_results = await asyncio.gather(*tasks)
                results.extend(batch_results)
            
            return results
    
    @performance_monitor
    @handle_errors(reraise=True)
    async def fine_tune_model(
        self,
        model_name: str,
        train_data: List[tuple],  # (image, label) pairs
        validation_data: List[tuple] = None,
        num_epochs: int = 10,
        learning_rate: float = 1e-4,
        save_path: str = None
    ) -> Dict[str, Any]:
        """Fine-tune a classification model on custom data."""
        async with error_context("fine_tune_model", {"model_name": model_name}):
            # Ensure model is loaded
            if model_name not in self.models:
                await self.load_model(model_name)
            
            model = self.models[model_name]
            
            # Prepare data loaders (simplified implementation)
            # In production, implement proper data loading and augmentation
            
            # Set up optimizer and loss function
            optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate)
            criterion = torch.nn.CrossEntropyLoss()
            
            # Training loop
            training_history = {
                "train_loss": [],
                "train_accuracy": [],
                "val_loss": [],
                "val_accuracy": []
            }
            
            model.train()
            for epoch in range(num_epochs):
                epoch_loss = 0.0
                correct_predictions = 0
                total_predictions = 0
                
                # Training phase (simplified)
                for image, label in train_data:
                    # Preprocess image and label
                    # Run forward pass, compute loss, backpropagate
                    pass
                
                # Validation phase
                if validation_data:
                    model.eval()
                    with torch.no_grad():
                        # Validation logic
                        pass
                    model.train()
                
                # Record metrics
                training_history["train_loss"].append(epoch_loss)
                # Add other metrics
            
            # Save model if path provided
            if save_path:
                torch.save(model.state_dict(), save_path)
            
            return {
                "status": "completed",
                "training_history": training_history,
                "final_accuracy": 0.95,  # Placeholder
                "model_path": save_path
            }
    
    async def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Get information about a loaded model."""
        if model_name not in self.models:
            return {"error": "Model not loaded"}
        
        model = self.models[model_name]
        
        # Count parameters
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        return {
            "model_name": model_name,
            "total_parameters": total_params,
            "trainable_parameters": trainable_params,
            "device": str(self.device),
            "model_type": str(type(model).__name__),
            "input_size": getattr(model, 'input_size', "Unknown"),
            "num_classes": getattr(model, 'num_classes', "Unknown")
        }
    
    async def cleanup(self):
        """Cleanup resources."""
        for model_name in list(self.models.keys()):
            del self.models[model_name]
            if model_name in self.processors:
                del self.processors[model_name]
        
        self.models.clear()
        self.processors.clear()
        
        # Clear GPU cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

# Utility functions for classification
async def classify_image_simple(
    image: Union[str, Image.Image, np.ndarray],
    model_name: str = "resnet50"
) -> ClassificationResult:
    """Simple image classification function."""
    classifier = EnterpriseImageClassifier()
    await classifier.initialize()
    
    results = await classifier.classify_image(image, model_name, top_k=1)
    await classifier.cleanup()
    
    return results[0] if results else None

async def compare_models(
    image: Union[str, Image.Image, np.ndarray],
    model_names: List[str] = None
) -> Dict[str, List[ClassificationResult]]:
    """Compare classification results across multiple models."""
    if not model_names:
        model_names = ["resnet50", "vit_base", "efficientnet_b0"]
    
    classifier = EnterpriseImageClassifier()
    await classifier.initialize()
    
    results = {}
    for model_name in model_names:
        try:
            model_results = await classifier.classify_image(image, model_name)
            results[model_name] = model_results
        except Exception as e:
            results[model_name] = [{"error": str(e)}]
    
    await classifier.cleanup()
    return results
