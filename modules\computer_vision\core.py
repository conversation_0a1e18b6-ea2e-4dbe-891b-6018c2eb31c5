"""
Computer Vision Core Components
==============================

Core data structures and utilities for computer vision operations.
"""

import numpy as np
from enum import Enum
from dataclasses import dataclass
from typing import List, Optional, Tuple, Any
from PIL import Image

class ModelType(Enum):
    """Computer vision model types."""
    CLASSIFICATION = "classification"
    OBJECT_DETECTION = "object_detection"
    SEGMENTATION = "segmentation"
    FACE_ANALYSIS = "face_analysis"
    OCR = "ocr"
    ENHANCEMENT = "enhancement"
    VIDEO_ANALYSIS = "video_analysis"
    DEPTH_ESTIMATION = "depth_estimation"

class Framework(Enum):
    """ML frameworks."""
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"
    ONNX = "onnx"
    OPENCV = "opencv"
    MEDIAPIPE = "mediapipe"
    ULTRALYTICS = "ultralytics"
    DETECTRON2 = "detectron2"

@dataclass
class BoundingBox:
    """Bounding box representation."""
    x1: float
    y1: float
    x2: float
    y2: float
    confidence: float = 0.0
    class_id: int = -1
    class_name: str = ""
    
    @property
    def width(self) -> float:
        return self.x2 - self.x1
    
    @property
    def height(self) -> float:
        return self.y2 - self.y1
    
    @property
    def area(self) -> float:
        return self.width * self.height
    
    @property
    def center(self) -> Tuple[float, float]:
        return ((self.x1 + self.x2) / 2, (self.y1 + self.y2) / 2)

@dataclass
class Detection:
    """Object detection result."""
    bbox: BoundingBox
    mask: Optional[np.ndarray] = None
    keypoints: Optional[List[Tuple[float, float]]] = None
    features: Optional[np.ndarray] = None
    metadata: Optional[dict] = None

@dataclass
class ClassificationResult:
    """Image classification result."""
    class_id: int
    class_name: str
    confidence: float
    features: Optional[np.ndarray] = None
    metadata: Optional[dict] = None

@dataclass
class SegmentationResult:
    """Image segmentation result."""
    mask: np.ndarray
    class_ids: List[int]
    class_names: List[str]
    confidences: List[float]
    metadata: Optional[dict] = None

# Custom exceptions
class CVError(Exception):
    """Base computer vision error."""
    pass

class ModelLoadError(CVError):
    """Model loading error."""
    pass

class InferenceError(CVError):
    """Inference error."""
    pass

class ValidationError(CVError):
    """Input validation error."""
    pass

# Utility functions
def validate_image(image: Any) -> bool:
    """Validate image input."""
    if isinstance(image, Image.Image):
        return True
    elif isinstance(image, np.ndarray):
        return len(image.shape) in [2, 3] and image.dtype == np.uint8
    elif isinstance(image, str):
        return image.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff'))
    return False

def normalize_image(image: Any) -> Image.Image:
    """Normalize image to PIL Image format."""
    if isinstance(image, Image.Image):
        return image.convert('RGB')
    elif isinstance(image, np.ndarray):
        if len(image.shape) == 2:
            image = np.stack([image] * 3, axis=-1)
        return Image.fromarray(image).convert('RGB')
    elif isinstance(image, str):
        return Image.open(image).convert('RGB')
    else:
        raise ValidationError(f"Unsupported image type: {type(image)}")

def resize_image(image: Image.Image, target_size: Tuple[int, int], maintain_aspect: bool = True) -> Image.Image:
    """Resize image with optional aspect ratio preservation."""
    if not maintain_aspect:
        return image.resize(target_size, Image.Resampling.LANCZOS)
    
    # Calculate new size maintaining aspect ratio
    width, height = image.size
    target_width, target_height = target_size
    
    ratio = min(target_width / width, target_height / height)
    new_width = int(width * ratio)
    new_height = int(height * ratio)
    
    # Resize and pad if necessary
    resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
    
    if new_width != target_width or new_height != target_height:
        # Create new image with target size and paste resized image
        new_image = Image.new('RGB', target_size, (0, 0, 0))
        paste_x = (target_width - new_width) // 2
        paste_y = (target_height - new_height) // 2
        new_image.paste(resized, (paste_x, paste_y))
        return new_image
    
    return resized

def calculate_iou(box1: BoundingBox, box2: BoundingBox) -> float:
    """Calculate Intersection over Union (IoU) between two bounding boxes."""
    # Calculate intersection
    x1 = max(box1.x1, box2.x1)
    y1 = max(box1.y1, box2.y1)
    x2 = min(box1.x2, box2.x2)
    y2 = min(box1.y2, box2.y2)
    
    if x2 <= x1 or y2 <= y1:
        return 0.0
    
    intersection = (x2 - x1) * (y2 - y1)
    union = box1.area + box2.area - intersection
    
    return intersection / union if union > 0 else 0.0

def non_max_suppression(detections: List[Detection], iou_threshold: float = 0.5) -> List[Detection]:
    """Apply Non-Maximum Suppression to remove overlapping detections."""
    if not detections:
        return []
    
    # Sort by confidence
    sorted_detections = sorted(detections, key=lambda d: d.bbox.confidence, reverse=True)
    
    keep = []
    while sorted_detections:
        # Take the detection with highest confidence
        current = sorted_detections.pop(0)
        keep.append(current)
        
        # Remove detections with high IoU
        sorted_detections = [
            det for det in sorted_detections
            if calculate_iou(current.bbox, det.bbox) < iou_threshold
        ]
    
    return keep

# Performance utilities
class ImageBatch:
    """Utility for batch processing images."""
    
    def __init__(self, batch_size: int = 32):
        self.batch_size = batch_size
        self.images = []
        self.metadata = []
    
    def add_image(self, image: Image.Image, metadata: dict = None):
        """Add image to batch."""
        self.images.append(image)
        self.metadata.append(metadata or {})
    
    def get_batches(self):
        """Get batches of images."""
        for i in range(0, len(self.images), self.batch_size):
            yield (
                self.images[i:i + self.batch_size],
                self.metadata[i:i + self.batch_size]
            )
    
    def clear(self):
        """Clear the batch."""
        self.images.clear()
        self.metadata.clear()
    
    def __len__(self):
        return len(self.images)

# Configuration classes
@dataclass
class CVConfig:
    """Computer vision configuration."""
    max_image_size: int = 2048
    batch_size: int = 32
    confidence_threshold: float = 0.5
    iou_threshold: float = 0.5
    device: str = "auto"  # auto, cpu, cuda
    enable_gpu: bool = True
    cache_models: bool = True
    max_cached_models: int = 10
