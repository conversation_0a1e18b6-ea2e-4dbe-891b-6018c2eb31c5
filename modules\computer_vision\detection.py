"""
Object Detection Module
======================

Advanced object detection with YOLO, DETR, and other SOTA models.
"""

import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Union, Tuple
from PIL import Image, ImageDraw
import torch
from ultralytics import YOLO
import cv2

from .core import Detection, BoundingBox, CVConfig, validate_image, normalize_image, non_max_suppression
from ..core.performance import performance_monitor, model_cache
from ..core.error_handling import handle_errors, error_context

class ObjectDetector:
    """Enterprise-grade object detection with multiple model support."""
    
    def __init__(self, config: CVConfig = None):
        self.config = config or CVConfig()
        self.models = {}
        self.device = self._get_device()
        
        # Supported models
        self.supported_models = {
            "yolov8n": "yolov8n.pt",
            "yolov8s": "yolov8s.pt", 
            "yolov8m": "yolov8m.pt",
            "yolov8l": "yolov8l.pt",
            "yolov8x": "yolov8x.pt",
            "yolov9": "yolov9c.pt",
            "yolov10": "yolov10n.pt"
        }
        
        # COCO class names
        self.coco_classes = [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
            'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
            'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
            'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
            'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
            'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
            'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
            'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
            'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
            'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
            'toothbrush'
        ]
    
    def _get_device(self) -> str:
        """Get optimal device for inference."""
        if self.config.device == "auto":
            return "cuda" if torch.cuda.is_available() and self.config.enable_gpu else "cpu"
        return self.config.device
    
    async def initialize(self):
        """Initialize the detector with default model."""
        await self.load_model("yolov8n")
    
    @performance_monitor
    @handle_errors(reraise=True)
    async def load_model(self, model_name: str, custom_model_path: str = None) -> bool:
        """Load an object detection model."""
        async with error_context("load_detection_model", {"model_name": model_name}):
            if model_name in self.models:
                return True
            
            try:
                if custom_model_path:
                    model = YOLO(custom_model_path)
                elif model_name in self.supported_models:
                    model_path = self.supported_models[model_name]
                    model = YOLO(model_path)
                else:
                    raise ValueError(f"Unsupported model: {model_name}")
                
                # Move to device
                model.to(self.device)
                self.models[model_name] = model
                
                return True
                
            except Exception as e:
                raise RuntimeError(f"Failed to load model {model_name}: {str(e)}")
    
    @performance_monitor
    @handle_errors(reraise=True)
    async def detect_objects(
        self,
        image: Union[str, Image.Image, np.ndarray],
        model_name: str = "yolov8n",
        confidence_threshold: float = None,
        iou_threshold: float = None,
        max_detections: int = 100,
        classes: List[int] = None
    ) -> List[Detection]:
        """Detect objects in an image."""
        async with error_context("detect_objects", {"model_name": model_name}):
            # Validate and normalize image
            if not validate_image(image):
                raise ValueError("Invalid image input")
            
            # Convert to format expected by YOLO
            if isinstance(image, Image.Image):
                cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            elif isinstance(image, str):
                cv_image = cv2.imread(image)
            else:
                cv_image = image
            
            # Ensure model is loaded
            if model_name not in self.models:
                await self.load_model(model_name)
            
            model = self.models[model_name]
            
            # Set thresholds
            conf_thresh = confidence_threshold or self.config.confidence_threshold
            iou_thresh = iou_threshold or self.config.iou_threshold
            
            # Run inference
            results = model(
                cv_image,
                conf=conf_thresh,
                iou=iou_thresh,
                classes=classes,
                max_det=max_detections,
                device=self.device
            )
            
            # Convert results to Detection objects
            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for i in range(len(boxes)):
                        # Extract box coordinates
                        x1, y1, x2, y2 = boxes.xyxy[i].cpu().numpy()
                        confidence = boxes.conf[i].cpu().numpy().item()
                        class_id = int(boxes.cls[i].cpu().numpy().item())
                        
                        # Get class name
                        class_name = self.coco_classes[class_id] if class_id < len(self.coco_classes) else f"class_{class_id}"
                        
                        # Create bounding box
                        bbox = BoundingBox(
                            x1=float(x1), y1=float(y1),
                            x2=float(x2), y2=float(y2),
                            confidence=confidence,
                            class_id=class_id,
                            class_name=class_name
                        )
                        
                        # Extract mask if available (for instance segmentation)
                        mask = None
                        if hasattr(result, 'masks') and result.masks is not None:
                            mask = result.masks.data[i].cpu().numpy()
                        
                        # Create detection
                        detection = Detection(
                            bbox=bbox,
                            mask=mask,
                            metadata={
                                "model_name": model_name,
                                "confidence_threshold": conf_thresh,
                                "iou_threshold": iou_thresh
                            }
                        )
                        detections.append(detection)
            
            return detections
    
    @performance_monitor
    @handle_errors(reraise=True)
    async def detect_batch(
        self,
        images: List[Union[str, Image.Image, np.ndarray]],
        model_name: str = "yolov8n",
        batch_size: int = None
    ) -> List[List[Detection]]:
        """Detect objects in multiple images."""
        async with error_context("detect_batch", {"model_name": model_name, "num_images": len(images)}):
            batch_size = batch_size or self.config.batch_size
            results = []
            
            for i in range(0, len(images), batch_size):
                batch = images[i:i + batch_size]
                batch_results = []
                
                # Process batch concurrently
                tasks = [
                    self.detect_objects(image, model_name)
                    for image in batch
                ]
                
                batch_results = await asyncio.gather(*tasks)
                results.extend(batch_results)
            
            return results
    
    @performance_monitor
    @handle_errors(reraise=True)
    async def track_objects(
        self,
        video_path: str,
        model_name: str = "yolov8n",
        output_path: str = None,
        tracker: str = "bytetrack"
    ) -> Dict[str, Any]:
        """Track objects in video."""
        async with error_context("track_objects", {"model_name": model_name}):
            # Ensure model is loaded
            if model_name not in self.models:
                await self.load_model(model_name)
            
            model = self.models[model_name]
            
            # Run tracking
            results = model.track(
                source=video_path,
                tracker=f"{tracker}.yaml",
                save=output_path is not None,
                project=output_path
            )
            
            # Process tracking results
            tracking_data = {
                "total_frames": 0,
                "unique_objects": set(),
                "tracks": []
            }
            
            for frame_idx, result in enumerate(results):
                tracking_data["total_frames"] += 1
                
                if result.boxes is not None and result.boxes.id is not None:
                    for i in range(len(result.boxes)):
                        track_id = int(result.boxes.id[i].cpu().numpy().item())
                        tracking_data["unique_objects"].add(track_id)
                        
                        # Store track information
                        x1, y1, x2, y2 = result.boxes.xyxy[i].cpu().numpy()
                        confidence = result.boxes.conf[i].cpu().numpy().item()
                        class_id = int(result.boxes.cls[i].cpu().numpy().item())
                        
                        track_info = {
                            "frame": frame_idx,
                            "track_id": track_id,
                            "bbox": [float(x1), float(y1), float(x2), float(y2)],
                            "confidence": confidence,
                            "class_id": class_id,
                            "class_name": self.coco_classes[class_id] if class_id < len(self.coco_classes) else f"class_{class_id}"
                        }
                        tracking_data["tracks"].append(track_info)
            
            tracking_data["unique_objects"] = len(tracking_data["unique_objects"])
            
            return tracking_data
    
    def visualize_detections(
        self,
        image: Union[str, Image.Image, np.ndarray],
        detections: List[Detection],
        show_confidence: bool = True,
        show_class: bool = True
    ) -> Image.Image:
        """Visualize detections on image."""
        # Convert to PIL Image
        if isinstance(image, str):
            pil_image = Image.open(image)
        elif isinstance(image, np.ndarray):
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        else:
            pil_image = image.copy()
        
        draw = ImageDraw.Draw(pil_image)
        
        for detection in detections:
            bbox = detection.bbox
            
            # Draw bounding box
            draw.rectangle(
                [bbox.x1, bbox.y1, bbox.x2, bbox.y2],
                outline="red",
                width=2
            )
            
            # Draw label
            label_parts = []
            if show_class:
                label_parts.append(bbox.class_name)
            if show_confidence:
                label_parts.append(f"{bbox.confidence:.2f}")
            
            if label_parts:
                label = " ".join(label_parts)
                draw.text((bbox.x1, bbox.y1 - 20), label, fill="red")
        
        return pil_image
    
    async def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Get information about a loaded model."""
        if model_name not in self.models:
            return {"error": "Model not loaded"}
        
        model = self.models[model_name]
        
        return {
            "model_name": model_name,
            "model_type": "Object Detection",
            "framework": "Ultralytics YOLO",
            "device": self.device,
            "input_size": getattr(model, 'imgsz', 640),
            "num_classes": len(self.coco_classes),
            "classes": self.coco_classes
        }
    
    async def cleanup(self):
        """Cleanup resources."""
        for model_name in list(self.models.keys()):
            del self.models[model_name]
        
        self.models.clear()
        
        # Clear GPU cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

# Utility functions
async def detect_objects_simple(
    image: Union[str, Image.Image, np.ndarray],
    model_name: str = "yolov8n"
) -> List[Detection]:
    """Simple object detection function."""
    detector = ObjectDetector()
    await detector.initialize()
    
    detections = await detector.detect_objects(image, model_name)
    await detector.cleanup()
    
    return detections

async def count_objects(
    image: Union[str, Image.Image, np.ndarray],
    target_classes: List[str] = None,
    model_name: str = "yolov8n"
) -> Dict[str, int]:
    """Count objects of specific classes in image."""
    detector = ObjectDetector()
    await detector.initialize()
    
    detections = await detector.detect_objects(image, model_name)
    await detector.cleanup()
    
    # Count objects
    counts = {}
    for detection in detections:
        class_name = detection.bbox.class_name
        if target_classes is None or class_name in target_classes:
            counts[class_name] = counts.get(class_name, 0) + 1
    
    return counts
