"""
Edge AI Module
==============

Edge AI and model optimization capabilities including:
- Model Quantization
- Model Pruning
- Knowledge Distillation
- ONNX Conversion
- TensorRT Optimization
- Mobile Deployment
- Hardware-specific Optimization
"""

import os
import tempfile
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go

# Import libraries with fallbacks
try:
    import torch
    import torch.nn as nn
    import torch.quantization as quantization
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import onnx
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False

try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False


class ModelOptimizer:
    """Model optimization for edge deployment."""
    
    def __init__(self):
        self.optimized_models = {}
        
    def quantize_pytorch_model(self, model: nn.Module, sample_data: torch.Tensor):
        """Quantize PyTorch model for edge deployment."""
        if not TORCH_AVAILABLE:
            st.error("PyTorch not available")
            return None
            
        try:
            # Prepare model for quantization
            model.eval()
            
            # Dynamic quantization (post-training)
            quantized_model = torch.quantization.quantize_dynamic(
                model, 
                {nn.Linear, nn.Conv2d}, 
                dtype=torch.qint8
            )
            
            # Calculate model sizes
            original_size = self._get_model_size(model)
            quantized_size = self._get_model_size(quantized_model)
            compression_ratio = original_size / quantized_size
            
            self.optimized_models['quantized_pytorch'] = {
                'model': quantized_model,
                'original_size_mb': original_size,
                'optimized_size_mb': quantized_size,
                'compression_ratio': compression_ratio,
                'optimization_type': 'quantization'
            }
            
            return quantized_model, compression_ratio
            
        except Exception as e:
            st.error(f"Error quantizing PyTorch model: {str(e)}")
            return None, None
    
    def prune_pytorch_model(self, model: nn.Module, pruning_ratio: float = 0.3):
        """Prune PyTorch model to reduce parameters."""
        if not TORCH_AVAILABLE:
            return None, None
            
        try:
            import torch.nn.utils.prune as prune
            
            # Apply structured pruning to linear layers
            parameters_to_prune = []
            for name, module in model.named_modules():
                if isinstance(module, nn.Linear):
                    parameters_to_prune.append((module, 'weight'))
            
            # Global magnitude pruning
            prune.global_unstructured(
                parameters_to_prune,
                pruning_method=prune.L1Unstructured,
                amount=pruning_ratio,
            )
            
            # Remove pruning reparameterization
            for module, param_name in parameters_to_prune:
                prune.remove(module, param_name)
            
            # Calculate sparsity
            total_params = sum(p.numel() for p in model.parameters())
            zero_params = sum((p == 0).sum().item() for p in model.parameters())
            sparsity = zero_params / total_params
            
            self.optimized_models['pruned_pytorch'] = {
                'model': model,
                'sparsity': sparsity,
                'total_params': total_params,
                'zero_params': zero_params,
                'optimization_type': 'pruning'
            }
            
            return model, sparsity
            
        except Exception as e:
            st.error(f"Error pruning PyTorch model: {str(e)}")
            return None, None
    
    def convert_to_onnx(self, model, sample_input: torch.Tensor, model_name: str = "model"):
        """Convert PyTorch model to ONNX format."""
        if not TORCH_AVAILABLE or not ONNX_AVAILABLE:
            st.error("PyTorch or ONNX not available")
            return None
            
        try:
            model.eval()
            
            # Create temporary file for ONNX model
            with tempfile.NamedTemporaryFile(suffix='.onnx', delete=False) as tmp_file:
                onnx_path = tmp_file.name
            
            # Export to ONNX
            torch.onnx.export(
                model,
                sample_input,
                onnx_path,
                export_params=True,
                opset_version=11,
                do_constant_folding=True,
                input_names=['input'],
                output_names=['output'],
                dynamic_axes={'input': {0: 'batch_size'},
                            'output': {0: 'batch_size'}}
            )
            
            # Verify ONNX model
            onnx_model = onnx.load(onnx_path)
            onnx.checker.check_model(onnx_model)
            
            # Create ONNX Runtime session
            ort_session = ort.InferenceSession(onnx_path)
            
            self.optimized_models['onnx'] = {
                'model_path': onnx_path,
                'ort_session': ort_session,
                'optimization_type': 'onnx_conversion'
            }
            
            return onnx_path, ort_session
            
        except Exception as e:
            st.error(f"Error converting to ONNX: {str(e)}")
            return None, None
    
    def optimize_tensorflow_model(self, model, optimization_type: str = "quantization"):
        """Optimize TensorFlow model."""
        if not TF_AVAILABLE:
            st.error("TensorFlow not available")
            return None
            
        try:
            if optimization_type == "quantization":
                # Post-training quantization
                converter = tf.lite.TFLiteConverter.from_keras_model(model)
                converter.optimizations = [tf.lite.Optimize.DEFAULT]
                tflite_model = converter.convert()
                
                # Save quantized model
                with tempfile.NamedTemporaryFile(suffix='.tflite', delete=False) as tmp_file:
                    tmp_file.write(tflite_model)
                    tflite_path = tmp_file.name
                
                self.optimized_models['tflite_quantized'] = {
                    'model_path': tflite_path,
                    'model_data': tflite_model,
                    'optimization_type': 'tflite_quantization'
                }
                
                return tflite_path
                
            elif optimization_type == "pruning":
                # TensorFlow Model Optimization Toolkit pruning
                st.info("TensorFlow pruning would be implemented with tfmot library")
                return None
                
        except Exception as e:
            st.error(f"Error optimizing TensorFlow model: {str(e)}")
            return None
    
    def _get_model_size(self, model):
        """Calculate model size in MB."""
        try:
            param_size = 0
            buffer_size = 0
            
            for param in model.parameters():
                param_size += param.nelement() * param.element_size()
            
            for buffer in model.buffers():
                buffer_size += buffer.nelement() * buffer.element_size()
            
            size_mb = (param_size + buffer_size) / 1024 / 1024
            return size_mb
        except:
            return 0


class PerformanceBenchmark:
    """Benchmark model performance on different hardware."""
    
    def __init__(self):
        self.benchmark_results = {}
    
    def benchmark_inference_speed(self, model, sample_input, num_runs: int = 100):
        """Benchmark model inference speed."""
        try:
            import time
            
            # Warm up
            for _ in range(10):
                _ = model(sample_input)
            
            # Benchmark
            start_time = time.time()
            for _ in range(num_runs):
                _ = model(sample_input)
            end_time = time.time()
            
            avg_inference_time = (end_time - start_time) / num_runs * 1000  # ms
            throughput = 1000 / avg_inference_time  # inferences per second
            
            return {
                'avg_inference_time_ms': avg_inference_time,
                'throughput_fps': throughput,
                'num_runs': num_runs
            }
            
        except Exception as e:
            st.error(f"Error benchmarking inference speed: {str(e)}")
            return None
    
    def benchmark_onnx_model(self, ort_session, sample_input_np: np.ndarray, num_runs: int = 100):
        """Benchmark ONNX model performance."""
        try:
            import time
            
            input_name = ort_session.get_inputs()[0].name
            
            # Warm up
            for _ in range(10):
                _ = ort_session.run(None, {input_name: sample_input_np})
            
            # Benchmark
            start_time = time.time()
            for _ in range(num_runs):
                _ = ort_session.run(None, {input_name: sample_input_np})
            end_time = time.time()
            
            avg_inference_time = (end_time - start_time) / num_runs * 1000  # ms
            throughput = 1000 / avg_inference_time  # inferences per second
            
            return {
                'avg_inference_time_ms': avg_inference_time,
                'throughput_fps': throughput,
                'num_runs': num_runs,
                'framework': 'onnx'
            }
            
        except Exception as e:
            st.error(f"Error benchmarking ONNX model: {str(e)}")
            return None


class HardwareProfiler:
    """Profile hardware capabilities for edge deployment."""
    
    def __init__(self):
        pass
    
    def get_system_info(self):
        """Get system hardware information."""
        try:
            import psutil
            import platform
            
            # CPU info
            cpu_info = {
                'cpu_count': psutil.cpu_count(),
                'cpu_freq': psutil.cpu_freq().current if psutil.cpu_freq() else None,
                'cpu_percent': psutil.cpu_percent(interval=1)
            }
            
            # Memory info
            memory = psutil.virtual_memory()
            memory_info = {
                'total_gb': memory.total / (1024**3),
                'available_gb': memory.available / (1024**3),
                'percent_used': memory.percent
            }
            
            # System info
            system_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'architecture': platform.architecture()[0]
            }
            
            # GPU info (if available)
            gpu_info = self._get_gpu_info()
            
            return {
                'cpu': cpu_info,
                'memory': memory_info,
                'system': system_info,
                'gpu': gpu_info
            }
            
        except Exception as e:
            st.error(f"Error getting system info: {str(e)}")
            return None
    
    def _get_gpu_info(self):
        """Get GPU information."""
        gpu_info = {'available': False}
        
        try:
            if TORCH_AVAILABLE and torch.cuda.is_available():
                gpu_info.update({
                    'available': True,
                    'device_count': torch.cuda.device_count(),
                    'current_device': torch.cuda.current_device(),
                    'device_name': torch.cuda.get_device_name(),
                    'memory_allocated': torch.cuda.memory_allocated() / (1024**3),
                    'memory_cached': torch.cuda.memory_reserved() / (1024**3)
                })
        except:
            pass
        
        return gpu_info


def render_edge_ai_page():
    """Render the Edge AI page."""
    st.title("📱 Edge AI")
    st.markdown("### Model Optimization for Edge Deployment")
    
    # Sidebar for Edge AI options
    edge_task = st.sidebar.selectbox(
        "Select Edge AI Task:",
        [
            "Model Optimization",
            "Performance Benchmarking",
            "Hardware Profiling",
            "Deployment Formats",
            "Mobile Optimization"
        ]
    )
    
    if edge_task == "Model Optimization":
        st.markdown("#### Model Optimization")
        
        optimizer = ModelOptimizer()
        
        # Create sample model for demonstration
        if st.button("Create Sample Model"):
            if TORCH_AVAILABLE:
                # Simple neural network
                model = nn.Sequential(
                    nn.Linear(784, 256),
                    nn.ReLU(),
                    nn.Linear(256, 128),
                    nn.ReLU(),
                    nn.Linear(128, 10)
                )
                
                sample_input = torch.randn(1, 784)
                
                st.session_state['edge_model'] = model
                st.session_state['edge_sample_input'] = sample_input
                
                original_size = optimizer._get_model_size(model)
                st.success(f"✅ Sample model created! Size: {original_size:.2f} MB")
            else:
                st.error("PyTorch not available")
        
        if 'edge_model' in st.session_state:
            model = st.session_state['edge_model']
            sample_input = st.session_state['edge_sample_input']
            
            optimization_type = st.selectbox(
                "Optimization Type:",
                ["Quantization", "Pruning", "ONNX Conversion"]
            )
            
            if optimization_type == "Quantization":
                if st.button("Apply Quantization"):
                    with st.spinner("Quantizing model..."):
                        quantized_model, compression_ratio = optimizer.quantize_pytorch_model(
                            model, sample_input
                        )
                        
                        if quantized_model is not None:
                            st.success(f"✅ Model quantized! Compression ratio: {compression_ratio:.2f}x")
                            
                            # Show size comparison
                            if 'quantized_pytorch' in optimizer.optimized_models:
                                opt_info = optimizer.optimized_models['quantized_pytorch']
                                
                                col1, col2, col3 = st.columns(3)
                                with col1:
                                    st.metric("Original Size", f"{opt_info['original_size_mb']:.2f} MB")
                                with col2:
                                    st.metric("Quantized Size", f"{opt_info['optimized_size_mb']:.2f} MB")
                                with col3:
                                    st.metric("Compression", f"{opt_info['compression_ratio']:.2f}x")
            
            elif optimization_type == "Pruning":
                pruning_ratio = st.slider("Pruning Ratio:", 0.1, 0.9, 0.3)
                
                if st.button("Apply Pruning"):
                    with st.spinner("Pruning model..."):
                        pruned_model, sparsity = optimizer.prune_pytorch_model(
                            model.copy() if hasattr(model, 'copy') else model, pruning_ratio
                        )
                        
                        if pruned_model is not None:
                            st.success(f"✅ Model pruned! Sparsity: {sparsity:.2%}")
                            
                            # Show pruning statistics
                            if 'pruned_pytorch' in optimizer.optimized_models:
                                prune_info = optimizer.optimized_models['pruned_pytorch']
                                
                                col1, col2 = st.columns(2)
                                with col1:
                                    st.metric("Total Parameters", f"{prune_info['total_params']:,}")
                                with col2:
                                    st.metric("Zero Parameters", f"{prune_info['zero_params']:,}")
            
            elif optimization_type == "ONNX Conversion":
                if st.button("Convert to ONNX"):
                    with st.spinner("Converting to ONNX..."):
                        onnx_path, ort_session = optimizer.convert_to_onnx(
                            model, sample_input
                        )
                        
                        if onnx_path is not None:
                            st.success("✅ Model converted to ONNX!")
                            st.info(f"ONNX model saved temporarily")
    
    elif edge_task == "Performance Benchmarking":
        st.markdown("#### Performance Benchmarking")
        
        if 'edge_model' in st.session_state:
            model = st.session_state['edge_model']
            sample_input = st.session_state['edge_sample_input']
            
            benchmarker = PerformanceBenchmark()
            
            num_runs = st.slider("Number of benchmark runs:", 10, 1000, 100)
            
            if st.button("Benchmark Original Model"):
                with st.spinner("Benchmarking..."):
                    results = benchmarker.benchmark_inference_speed(
                        model, sample_input, num_runs
                    )
                    
                    if results:
                        col1, col2 = st.columns(2)
                        with col1:
                            st.metric("Avg Inference Time", f"{results['avg_inference_time_ms']:.2f} ms")
                        with col2:
                            st.metric("Throughput", f"{results['throughput_fps']:.1f} FPS")
            
            # Benchmark optimized models if available
            optimizer = ModelOptimizer()
            if hasattr(st.session_state, 'model_optimizer'):
                optimizer = st.session_state['model_optimizer']
            
            if 'onnx' in optimizer.optimized_models:
                if st.button("Benchmark ONNX Model"):
                    ort_session = optimizer.optimized_models['onnx']['ort_session']
                    sample_input_np = sample_input.numpy()
                    
                    with st.spinner("Benchmarking ONNX..."):
                        results = benchmarker.benchmark_onnx_model(
                            ort_session, sample_input_np, num_runs
                        )
                        
                        if results:
                            col1, col2 = st.columns(2)
                            with col1:
                                st.metric("ONNX Inference Time", f"{results['avg_inference_time_ms']:.2f} ms")
                            with col2:
                                st.metric("ONNX Throughput", f"{results['throughput_fps']:.1f} FPS")
        else:
            st.warning("⚠️ Please create a sample model first in Model Optimization")
    
    elif edge_task == "Hardware Profiling":
        st.markdown("#### Hardware Profiling")
        
        profiler = HardwareProfiler()
        
        if st.button("Profile System Hardware"):
            with st.spinner("Profiling hardware..."):
                system_info = profiler.get_system_info()
                
                if system_info:
                    # CPU Information
                    st.markdown("**CPU Information**")
                    cpu_info = system_info['cpu']
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("CPU Cores", cpu_info['cpu_count'])
                    with col2:
                        if cpu_info['cpu_freq']:
                            st.metric("CPU Frequency", f"{cpu_info['cpu_freq']:.0f} MHz")
                    with col3:
                        st.metric("CPU Usage", f"{cpu_info['cpu_percent']:.1f}%")
                    
                    # Memory Information
                    st.markdown("**Memory Information**")
                    memory_info = system_info['memory']
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Total Memory", f"{memory_info['total_gb']:.1f} GB")
                    with col2:
                        st.metric("Available Memory", f"{memory_info['available_gb']:.1f} GB")
                    with col3:
                        st.metric("Memory Usage", f"{memory_info['percent_used']:.1f}%")
                    
                    # GPU Information
                    st.markdown("**GPU Information**")
                    gpu_info = system_info['gpu']
                    if gpu_info['available']:
                        col1, col2 = st.columns(2)
                        with col1:
                            st.write(f"**Device:** {gpu_info['device_name']}")
                            st.write(f"**Device Count:** {gpu_info['device_count']}")
                        with col2:
                            st.write(f"**Memory Allocated:** {gpu_info['memory_allocated']:.2f} GB")
                            st.write(f"**Memory Cached:** {gpu_info['memory_cached']:.2f} GB")
                    else:
                        st.write("No GPU available")
                    
                    # System Information
                    st.markdown("**System Information**")
                    system = system_info['system']
                    st.write(f"**Platform:** {system['platform']}")
                    st.write(f"**Architecture:** {system['architecture']}")
    
    else:
        st.info(f"{edge_task} implementation coming soon!")
    
    # Show Edge AI overview
    st.markdown("### 🚀 Edge AI Capabilities")
    
    capabilities = {
        "Optimization": ["Quantization", "Pruning", "Knowledge Distillation", "Compression"],
        "Formats": ["ONNX", "TensorRT", "TFLite", "CoreML", "OpenVINO"],
        "Hardware": ["CPU", "GPU", "NPU", "FPGA", "Mobile", "IoT"],
        "Deployment": ["Mobile Apps", "Edge Devices", "Embedded Systems", "Real-time"]
    }
    
    cols = st.columns(2)
    for i, (category, methods) in enumerate(capabilities.items()):
        with cols[i % 2]:
            st.markdown(f"**{category}**")
            for method in methods:
                st.markdown(f"• {method}")
