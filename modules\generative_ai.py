"""
Generative AI Module
===================

Comprehensive generative AI capabilities including:
- Text Generation (GPT, T5, BART)
- Image Generation (Stable Diffusion, DALL-E)
- Code Generation
- Audio Generation
- Video Generation
- Multimodal Generation
- Fine-tuning & Custom Models
"""

import os
import io
import base64
from typing import Any, Dict, List, Optional, Tuple, Union
import numpy as np
import pandas as pd
import streamlit as st
from PIL import Image
import requests
import json

# Import libraries with fallbacks
try:
    from transformers import (
        AutoTokenizer, AutoModelForCausalLM, AutoModelForSeq2SeqLM,
        pipeline, GPT2LMHeadModel, GPT2Tokenizer, T5ForConditionalGeneration, T5Tokenizer
    )
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    st.warning("Transformers library not available")

try:
    from diffusers import StableDiffusionPipeline, DiffusionPipeline
    import torch
    DIFFUSERS_AVAILABLE = True
except ImportError:
    DIFFUSERS_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False


class TextGenerator:
    """Advanced text generation using various models."""
    
    def __init__(self):
        self.models = {}
        self.tokenizers = {}
        
    def load_gpt2_model(self, model_name: str = "gpt2"):
        """Load GPT-2 model for text generation."""
        if not TRANSFORMERS_AVAILABLE:
            st.error("Transformers library not available")
            return None
            
        try:
            tokenizer = GPT2Tokenizer.from_pretrained(model_name)
            model = GPT2LMHeadModel.from_pretrained(model_name)
            
            # Add padding token
            tokenizer.pad_token = tokenizer.eos_token
            
            self.tokenizers[model_name] = tokenizer
            self.models[model_name] = model
            
            return model
        except Exception as e:
            st.error(f"Error loading GPT-2 model: {str(e)}")
            return None
    
    def generate_text(self, prompt: str, model_name: str = "gpt2", 
                     max_length: int = 100, temperature: float = 0.7,
                     num_return_sequences: int = 1):
        """Generate text using loaded model."""
        if model_name not in self.models:
            self.load_gpt2_model(model_name)
        
        model = self.models.get(model_name)
        tokenizer = self.tokenizers.get(model_name)
        
        if model is None or tokenizer is None:
            return None
            
        try:
            # Encode input
            input_ids = tokenizer.encode(prompt, return_tensors='pt')
            
            # Generate
            with torch.no_grad():
                outputs = model.generate(
                    input_ids,
                    max_length=max_length,
                    temperature=temperature,
                    num_return_sequences=num_return_sequences,
                    pad_token_id=tokenizer.eos_token_id,
                    do_sample=True
                )
            
            # Decode outputs
            generated_texts = []
            for output in outputs:
                text = tokenizer.decode(output, skip_special_tokens=True)
                generated_texts.append(text)
            
            return generated_texts
            
        except Exception as e:
            st.error(f"Error generating text: {str(e)}")
            return None
    
    def generate_with_pipeline(self, prompt: str, task: str = "text-generation",
                              model_name: str = "gpt2", max_length: int = 100):
        """Generate text using Hugging Face pipeline."""
        if not TRANSFORMERS_AVAILABLE:
            return None
            
        try:
            generator = pipeline(task, model=model_name)
            result = generator(prompt, max_length=max_length, num_return_sequences=1)
            return result[0]['generated_text']
        except Exception as e:
            st.error(f"Error in pipeline generation: {str(e)}")
            return None


class CodeGenerator:
    """Code generation using specialized models."""
    
    def __init__(self):
        self.models = {}
    
    def generate_code(self, prompt: str, language: str = "python", 
                     model_name: str = "microsoft/CodeGPT-small-py"):
        """Generate code using CodeGPT or similar models."""
        if not TRANSFORMERS_AVAILABLE:
            st.error("Transformers library not available")
            return None
            
        try:
            generator = pipeline("text-generation", model=model_name)
            
            # Format prompt for code generation
            formatted_prompt = f"# {prompt}\n"
            
            result = generator(
                formatted_prompt,
                max_length=200,
                temperature=0.2,
                num_return_sequences=1
            )
            
            return result[0]['generated_text']
            
        except Exception as e:
            st.error(f"Error generating code: {str(e)}")
            return None
    
    def explain_code(self, code: str):
        """Explain code functionality."""
        if not TRANSFORMERS_AVAILABLE:
            return None
            
        try:
            # Use a text generation model to explain code
            prompt = f"Explain this code:\n\n{code}\n\nExplanation:"
            
            generator = pipeline("text-generation", model="gpt2")
            result = generator(prompt, max_length=300, temperature=0.3)
            
            explanation = result[0]['generated_text'].split("Explanation:")[-1].strip()
            return explanation
            
        except Exception as e:
            st.error(f"Error explaining code: {str(e)}")
            return None


class ImageGenerator:
    """Image generation using Stable Diffusion and other models."""
    
    def __init__(self):
        self.pipelines = {}
        
    def load_stable_diffusion(self, model_id: str = "runwayml/stable-diffusion-v1-5"):
        """Load Stable Diffusion pipeline."""
        if not DIFFUSERS_AVAILABLE:
            st.error("Diffusers library not available")
            return None
            
        try:
            pipe = StableDiffusionPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
            )
            
            if torch.cuda.is_available():
                pipe = pipe.to("cuda")
            
            self.pipelines[model_id] = pipe
            return pipe
            
        except Exception as e:
            st.error(f"Error loading Stable Diffusion: {str(e)}")
            return None
    
    def generate_image(self, prompt: str, model_id: str = "runwayml/stable-diffusion-v1-5",
                      num_inference_steps: int = 50, guidance_scale: float = 7.5,
                      width: int = 512, height: int = 512):
        """Generate image from text prompt."""
        if model_id not in self.pipelines:
            self.load_stable_diffusion(model_id)
        
        pipe = self.pipelines.get(model_id)
        if pipe is None:
            return None
            
        try:
            image = pipe(
                prompt,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                width=width,
                height=height
            ).images[0]
            
            return image
            
        except Exception as e:
            st.error(f"Error generating image: {str(e)}")
            return None


class MultimodalGenerator:
    """Multimodal generation combining text, image, and other modalities."""
    
    def __init__(self):
        pass
    
    def image_to_text(self, image: Image.Image):
        """Generate text description from image."""
        if not TRANSFORMERS_AVAILABLE:
            return None
            
        try:
            # Use BLIP or similar model for image captioning
            captioner = pipeline("image-to-text")
            result = captioner(image)
            return result[0]['generated_text']
            
        except Exception as e:
            st.error(f"Error in image-to-text: {str(e)}")
            return None
    
    def text_to_speech(self, text: str):
        """Convert text to speech."""
        # Placeholder for TTS implementation
        st.info("Text-to-speech would be implemented with models like Tacotron2, FastSpeech2")
        return None
    
    def speech_to_text(self, audio_file):
        """Convert speech to text."""
        # Placeholder for STT implementation
        st.info("Speech-to-text would be implemented with models like Whisper")
        return None


class LLMIntegration:
    """Integration with external LLM APIs."""
    
    def __init__(self):
        self.api_keys = {}
    
    def set_openai_key(self, api_key: str):
        """Set OpenAI API key."""
        self.api_keys['openai'] = api_key
        if OPENAI_AVAILABLE:
            openai.api_key = api_key
    
    def set_anthropic_key(self, api_key: str):
        """Set Anthropic API key."""
        self.api_keys['anthropic'] = api_key
    
    def generate_with_openai(self, prompt: str, model: str = "gpt-3.5-turbo",
                            max_tokens: int = 150, temperature: float = 0.7):
        """Generate text using OpenAI API."""
        if not OPENAI_AVAILABLE or 'openai' not in self.api_keys:
            st.error("OpenAI API not available or key not set")
            return None
            
        try:
            response = openai.ChatCompletion.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            st.error(f"Error with OpenAI API: {str(e)}")
            return None
    
    def generate_with_anthropic(self, prompt: str, model: str = "claude-3-sonnet-20240229",
                               max_tokens: int = 150):
        """Generate text using Anthropic API."""
        if not ANTHROPIC_AVAILABLE or 'anthropic' not in self.api_keys:
            st.error("Anthropic API not available or key not set")
            return None
            
        try:
            client = anthropic.Anthropic(api_key=self.api_keys['anthropic'])
            
            response = client.messages.create(
                model=model,
                max_tokens=max_tokens,
                messages=[{"role": "user", "content": prompt}]
            )
            
            return response.content[0].text
            
        except Exception as e:
            st.error(f"Error with Anthropic API: {str(e)}")
            return None


def render_generative_ai_page():
    """Render the generative AI page."""
    st.title("🎨 Generative AI")
    st.markdown("### Advanced Generative AI Capabilities")
    
    # Sidebar for GenAI options
    genai_task = st.sidebar.selectbox(
        "Select GenAI Task:",
        [
            "Text Generation",
            "Code Generation",
            "Image Generation",
            "Multimodal Generation",
            "LLM Integration",
            "Fine-tuning",
            "Custom Models"
        ]
    )
    
    if genai_task == "Text Generation":
        st.markdown("#### Text Generation")
        
        text_gen = TextGenerator()
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            prompt = st.text_area("Enter your prompt:", height=100)
            
        with col2:
            model_choice = st.selectbox(
                "Model:",
                ["gpt2", "gpt2-medium", "gpt2-large", "distilgpt2"]
            )
            
            max_length = st.slider("Max Length:", 50, 500, 100)
            temperature = st.slider("Temperature:", 0.1, 2.0, 0.7)
            num_sequences = st.slider("Number of Sequences:", 1, 5, 1)
        
        if st.button("Generate Text") and prompt:
            with st.spinner("Generating text..."):
                results = text_gen.generate_text(
                    prompt, model_choice, max_length, temperature, num_sequences
                )
                
                if results:
                    st.markdown("#### Generated Text(s):")
                    for i, text in enumerate(results):
                        st.markdown(f"**Sequence {i+1}:**")
                        st.write(text)
                        st.markdown("---")
    
    elif genai_task == "Code Generation":
        st.markdown("#### Code Generation")
        
        code_gen = CodeGenerator()
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            task_description = st.text_area(
                "Describe the code you need:",
                height=100,
                placeholder="e.g., Create a function to calculate fibonacci numbers"
            )
        
        with col2:
            language = st.selectbox(
                "Programming Language:",
                ["python", "javascript", "java", "cpp", "go"]
            )
        
        if st.button("Generate Code") and task_description:
            with st.spinner("Generating code..."):
                code = code_gen.generate_code(task_description, language)
                
                if code:
                    st.markdown("#### Generated Code:")
                    st.code(code, language=language)
                    
                    # Option to explain code
                    if st.button("Explain Code"):
                        explanation = code_gen.explain_code(code)
                        if explanation:
                            st.markdown("#### Code Explanation:")
                            st.write(explanation)
    
    elif genai_task == "Image Generation":
        st.markdown("#### Image Generation")
        
        if DIFFUSERS_AVAILABLE:
            img_gen = ImageGenerator()
            
            col1, col2 = st.columns([2, 1])
            
            with col1:
                prompt = st.text_area(
                    "Enter image description:",
                    height=100,
                    placeholder="e.g., A beautiful sunset over mountains, digital art"
                )
            
            with col2:
                model_choice = st.selectbox(
                    "Model:",
                    ["runwayml/stable-diffusion-v1-5", "stabilityai/stable-diffusion-2-1"]
                )
                
                steps = st.slider("Inference Steps:", 10, 100, 50)
                guidance = st.slider("Guidance Scale:", 1.0, 20.0, 7.5)
                
                col2a, col2b = st.columns(2)
                with col2a:
                    width = st.selectbox("Width:", [512, 768, 1024], index=0)
                with col2b:
                    height = st.selectbox("Height:", [512, 768, 1024], index=0)
            
            if st.button("Generate Image") and prompt:
                with st.spinner("Generating image... This may take a while."):
                    image = img_gen.generate_image(
                        prompt, model_choice, steps, guidance, width, height
                    )
                    
                    if image:
                        st.markdown("#### Generated Image:")
                        st.image(image, use_column_width=True)
                        
                        # Option to download
                        buf = io.BytesIO()
                        image.save(buf, format='PNG')
                        st.download_button(
                            label="Download Image",
                            data=buf.getvalue(),
                            file_name="generated_image.png",
                            mime="image/png"
                        )
        else:
            st.error("Diffusers library not available for image generation")
    
    elif genai_task == "LLM Integration":
        st.markdown("#### LLM API Integration")
        
        llm_integration = LLMIntegration()
        
        api_provider = st.selectbox(
            "API Provider:",
            ["OpenAI", "Anthropic", "Local Model"]
        )
        
        if api_provider in ["OpenAI", "Anthropic"]:
            api_key = st.text_input(
                f"Enter {api_provider} API Key:",
                type="password",
                help=f"Your {api_provider} API key"
            )
            
            if api_key:
                if api_provider == "OpenAI":
                    llm_integration.set_openai_key(api_key)
                    model_choice = st.selectbox(
                        "Model:",
                        ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]
                    )
                else:
                    llm_integration.set_anthropic_key(api_key)
                    model_choice = st.selectbox(
                        "Model:",
                        ["claude-3-sonnet-20240229", "claude-3-opus-20240229"]
                    )
                
                prompt = st.text_area("Enter your prompt:", height=150)
                
                col1, col2 = st.columns(2)
                with col1:
                    max_tokens = st.slider("Max Tokens:", 50, 1000, 150)
                with col2:
                    temperature = st.slider("Temperature:", 0.0, 2.0, 0.7)
                
                if st.button("Generate Response") and prompt:
                    with st.spinner(f"Generating with {api_provider}..."):
                        if api_provider == "OpenAI":
                            response = llm_integration.generate_with_openai(
                                prompt, model_choice, max_tokens, temperature
                            )
                        else:
                            response = llm_integration.generate_with_anthropic(
                                prompt, model_choice, max_tokens
                            )
                        
                        if response:
                            st.markdown("#### Response:")
                            st.write(response)
        else:
            st.info("Local model integration would be implemented here")
    
    else:
        st.info(f"{genai_task} implementation coming soon!")
    
    # Show capabilities overview
    if genai_task not in ["Text Generation", "Code Generation", "Image Generation", "LLM Integration"]:
        st.markdown("### 🚀 Available Capabilities")
        
        capabilities = {
            "Text": ["GPT-2/3/4", "T5", "BART", "Custom LLMs"],
            "Code": ["CodeGPT", "Codex", "CodeT5", "Custom"],
            "Image": ["Stable Diffusion", "DALL-E", "Midjourney", "Custom"],
            "Audio": ["Tacotron2", "WaveNet", "Whisper", "Custom"],
            "Video": ["Video Diffusion", "Make-A-Video", "Custom"],
            "Multimodal": ["CLIP", "BLIP", "GPT-4V", "Custom"]
        }
        
        cols = st.columns(3)
        for i, (category, methods) in enumerate(capabilities.items()):
            with cols[i % 3]:
                st.markdown(f"**{category}**")
                for method in methods:
                    st.markdown(f"• {method}")
