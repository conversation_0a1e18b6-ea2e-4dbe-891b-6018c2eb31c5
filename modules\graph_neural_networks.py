"""
Graph Neural Networks Module
============================

Comprehensive GNN capabilities including:
- Graph Convolutional Networks (GCN)
- Graph Attention Networks (GAT)
- GraphSAGE
- Graph Transformer
- Node Classification
- Link Prediction
- Graph Classification
- Knowledge Graphs
"""

import numpy as np
import pandas as pd
import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import networkx as nx
from typing import Dict, List, Optional, Tuple, Any

# Import libraries with fallbacks
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import torch_geometric
    from torch_geometric.data import Data, DataLoader
    from torch_geometric.nn import GCNConv, GATConv, SAGEConv, global_mean_pool
    from torch_geometric.utils import to_networkx
    TORCH_GEOMETRIC_AVAILABLE = True
except ImportError:
    TORCH_GEOMETRIC_AVAILABLE = False

try:
    import dgl
    DGL_AVAILABLE = True
except ImportError:
    DGL_AVAILABLE = False


class GraphDataProcessor:
    """Graph data processing and visualization."""
    
    def __init__(self):
        self.graph = None
        self.node_features = None
        self.edge_features = None
    
    def create_graph_from_edges(self, edge_list: List[Tuple], node_features: Optional[Dict] = None):
        """Create graph from edge list."""
        try:
            G = nx.Graph()
            G.add_edges_from(edge_list)
            
            if node_features:
                for node, features in node_features.items():
                    G.nodes[node].update(features)
            
            self.graph = G
            return G
        except Exception as e:
            st.error(f"Error creating graph: {str(e)}")
            return None
    
    def load_graph_from_dataframe(self, df: pd.DataFrame, source_col: str, target_col: str):
        """Load graph from DataFrame."""
        try:
            edge_list = list(zip(df[source_col], df[target_col]))
            return self.create_graph_from_edges(edge_list)
        except Exception as e:
            st.error(f"Error loading graph from DataFrame: {str(e)}")
            return None
    
    def visualize_graph(self, layout: str = "spring"):
        """Visualize graph using plotly."""
        if self.graph is None:
            st.error("No graph loaded")
            return None
        
        try:
            # Get layout positions
            if layout == "spring":
                pos = nx.spring_layout(self.graph)
            elif layout == "circular":
                pos = nx.circular_layout(self.graph)
            elif layout == "random":
                pos = nx.random_layout(self.graph)
            else:
                pos = nx.spring_layout(self.graph)
            
            # Extract edges
            edge_x = []
            edge_y = []
            for edge in self.graph.edges():
                x0, y0 = pos[edge[0]]
                x1, y1 = pos[edge[1]]
                edge_x.extend([x0, x1, None])
                edge_y.extend([y0, y1, None])
            
            # Extract nodes
            node_x = []
            node_y = []
            node_text = []
            for node in self.graph.nodes():
                x, y = pos[node]
                node_x.append(x)
                node_y.append(y)
                node_text.append(str(node))
            
            # Create plotly figure
            fig = go.Figure()
            
            # Add edges
            fig.add_trace(go.Scatter(
                x=edge_x, y=edge_y,
                line=dict(width=0.5, color='#888'),
                hoverinfo='none',
                mode='lines'
            ))
            
            # Add nodes
            fig.add_trace(go.Scatter(
                x=node_x, y=node_y,
                mode='markers+text',
                hoverinfo='text',
                text=node_text,
                textposition="middle center",
                marker=dict(
                    size=20,
                    color='lightblue',
                    line=dict(width=2, color='black')
                )
            ))
            
            fig.update_layout(
                title="Graph Visualization",
                showlegend=False,
                hovermode='closest',
                margin=dict(b=20,l=5,r=5,t=40),
                annotations=[ dict(
                    text="Graph Network",
                    showarrow=False,
                    xref="paper", yref="paper",
                    x=0.005, y=-0.002,
                    xanchor='left', yanchor='bottom',
                    font=dict(color='black', size=12)
                )],
                xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                yaxis=dict(showgrid=False, zeroline=False, showticklabels=False)
            )
            
            return fig
        except Exception as e:
            st.error(f"Error visualizing graph: {str(e)}")
            return None
    
    def get_graph_statistics(self):
        """Get basic graph statistics."""
        if self.graph is None:
            return None
        
        try:
            stats = {
                'num_nodes': self.graph.number_of_nodes(),
                'num_edges': self.graph.number_of_edges(),
                'density': nx.density(self.graph),
                'avg_clustering': nx.average_clustering(self.graph),
                'is_connected': nx.is_connected(self.graph)
            }
            
            if nx.is_connected(self.graph):
                stats['diameter'] = nx.diameter(self.graph)
                stats['avg_path_length'] = nx.average_shortest_path_length(self.graph)
            
            return stats
        except Exception as e:
            st.error(f"Error computing graph statistics: {str(e)}")
            return None


class GCNModel(nn.Module):
    """Graph Convolutional Network model."""
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int, num_layers: int = 2):
        super(GCNModel, self).__init__()
        
        if not TORCH_GEOMETRIC_AVAILABLE:
            raise ImportError("PyTorch Geometric not available")
        
        self.convs = nn.ModuleList()
        self.convs.append(GCNConv(input_dim, hidden_dim))
        
        for _ in range(num_layers - 2):
            self.convs.append(GCNConv(hidden_dim, hidden_dim))
        
        self.convs.append(GCNConv(hidden_dim, output_dim))
        self.dropout = nn.Dropout(0.5)
    
    def forward(self, x, edge_index):
        for i, conv in enumerate(self.convs[:-1]):
            x = conv(x, edge_index)
            x = F.relu(x)
            x = self.dropout(x)
        
        x = self.convs[-1](x, edge_index)
        return F.log_softmax(x, dim=1)


class GATModel(nn.Module):
    """Graph Attention Network model."""
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int, heads: int = 8):
        super(GATModel, self).__init__()
        
        if not TORCH_GEOMETRIC_AVAILABLE:
            raise ImportError("PyTorch Geometric not available")
        
        self.conv1 = GATConv(input_dim, hidden_dim, heads=heads, dropout=0.6)
        self.conv2 = GATConv(hidden_dim * heads, output_dim, heads=1, concat=False, dropout=0.6)
        self.dropout = nn.Dropout(0.6)
    
    def forward(self, x, edge_index):
        x = self.dropout(x)
        x = F.elu(self.conv1(x, edge_index))
        x = self.dropout(x)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


class GraphSAGEModel(nn.Module):
    """GraphSAGE model."""
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int):
        super(GraphSAGEModel, self).__init__()
        
        if not TORCH_GEOMETRIC_AVAILABLE:
            raise ImportError("PyTorch Geometric not available")
        
        self.conv1 = SAGEConv(input_dim, hidden_dim)
        self.conv2 = SAGEConv(hidden_dim, output_dim)
        self.dropout = nn.Dropout(0.5)
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = self.dropout(x)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


class GNNTrainer:
    """GNN model trainer."""
    
    def __init__(self):
        self.model = None
        self.optimizer = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu') if TORCH_AVAILABLE else None
    
    def create_model(self, model_type: str, input_dim: int, hidden_dim: int, output_dim: int):
        """Create GNN model."""
        if not TORCH_GEOMETRIC_AVAILABLE:
            st.error("PyTorch Geometric not available")
            return None
        
        try:
            if model_type == "GCN":
                self.model = GCNModel(input_dim, hidden_dim, output_dim)
            elif model_type == "GAT":
                self.model = GATModel(input_dim, hidden_dim, output_dim)
            elif model_type == "GraphSAGE":
                self.model = GraphSAGEModel(input_dim, hidden_dim, output_dim)
            else:
                st.error(f"Unknown model type: {model_type}")
                return None
            
            if self.device:
                self.model = self.model.to(self.device)
            
            self.optimizer = torch.optim.Adam(self.model.parameters(), lr=0.01, weight_decay=5e-4)
            
            return self.model
        except Exception as e:
            st.error(f"Error creating model: {str(e)}")
            return None
    
    def train_node_classification(self, data, epochs: int = 200):
        """Train model for node classification."""
        if self.model is None:
            st.error("No model created")
            return None
        
        try:
            self.model.train()
            losses = []
            
            for epoch in range(epochs):
                self.optimizer.zero_grad()
                out = self.model(data.x, data.edge_index)
                loss = F.nll_loss(out[data.train_mask], data.y[data.train_mask])
                loss.backward()
                self.optimizer.step()
                losses.append(loss.item())
                
                if epoch % 50 == 0:
                    st.write(f'Epoch {epoch}, Loss: {loss.item():.4f}')
            
            return losses
        except Exception as e:
            st.error(f"Error training model: {str(e)}")
            return None
    
    def evaluate_model(self, data):
        """Evaluate trained model."""
        if self.model is None:
            return None
        
        try:
            self.model.eval()
            with torch.no_grad():
                pred = self.model(data.x, data.edge_index).argmax(dim=1)
                correct = (pred[data.test_mask] == data.y[data.test_mask]).float().sum()
                accuracy = correct / data.test_mask.sum()
            
            return accuracy.item()
        except Exception as e:
            st.error(f"Error evaluating model: {str(e)}")
            return None


def render_graph_neural_networks_page():
    """Render the GNN page."""
    st.title("🕸️ Graph Neural Networks")
    st.markdown("### Advanced Graph Learning")
    
    # Sidebar for GNN options
    gnn_task = st.sidebar.selectbox(
        "Select GNN Task:",
        [
            "Graph Data Upload",
            "Graph Visualization",
            "Node Classification",
            "Link Prediction",
            "Graph Classification",
            "Knowledge Graphs"
        ]
    )
    
    processor = GraphDataProcessor()
    
    if gnn_task == "Graph Data Upload":
        st.markdown("#### Upload Graph Data")
        
        upload_method = st.radio(
            "Upload Method:",
            ["Edge List CSV", "Adjacency Matrix", "Manual Input"]
        )
        
        if upload_method == "Edge List CSV":
            uploaded_file = st.file_uploader("Upload edge list CSV", type=['csv'])
            
            if uploaded_file is not None:
                df = pd.read_csv(uploaded_file)
                st.dataframe(df.head())
                
                source_col = st.selectbox("Source column:", df.columns)
                target_col = st.selectbox("Target column:", df.columns)
                
                if st.button("Create Graph"):
                    graph = processor.load_graph_from_dataframe(df, source_col, target_col)
                    if graph:
                        st.success("✅ Graph created successfully!")
                        st.session_state['graph'] = graph
                        st.session_state['graph_processor'] = processor
        
        elif upload_method == "Manual Input":
            st.markdown("#### Manual Edge Input")
            edge_input = st.text_area(
                "Enter edges (one per line, format: node1,node2):",
                height=150,
                placeholder="A,B\nB,C\nC,D\nA,D"
            )
            
            if st.button("Create Graph") and edge_input:
                try:
                    edges = []
                    for line in edge_input.strip().split('\n'):
                        if ',' in line:
                            source, target = line.strip().split(',')
                            edges.append((source.strip(), target.strip()))
                    
                    graph = processor.create_graph_from_edges(edges)
                    if graph:
                        st.success("✅ Graph created successfully!")
                        st.session_state['graph'] = graph
                        st.session_state['graph_processor'] = processor
                except Exception as e:
                    st.error(f"Error parsing edges: {str(e)}")
    
    elif gnn_task == "Graph Visualization":
        if 'graph' in st.session_state:
            processor = st.session_state['graph_processor']
            
            st.markdown("#### Graph Visualization")
            
            layout = st.selectbox(
                "Layout Algorithm:",
                ["spring", "circular", "random"]
            )
            
            if st.button("Visualize Graph"):
                fig = processor.visualize_graph(layout)
                if fig:
                    st.plotly_chart(fig, use_container_width=True)
            
            # Show graph statistics
            if st.button("Show Graph Statistics"):
                stats = processor.get_graph_statistics()
                if stats:
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Nodes", stats['num_nodes'])
                        st.metric("Edges", stats['num_edges'])
                    
                    with col2:
                        st.metric("Density", f"{stats['density']:.3f}")
                        st.metric("Avg Clustering", f"{stats['avg_clustering']:.3f}")
                    
                    with col3:
                        st.metric("Connected", "Yes" if stats['is_connected'] else "No")
                        if 'diameter' in stats:
                            st.metric("Diameter", stats['diameter'])
        else:
            st.warning("⚠️ Please upload graph data first!")
    
    elif gnn_task == "Node Classification":
        if TORCH_GEOMETRIC_AVAILABLE:
            st.markdown("#### Node Classification with GNNs")
            
            model_type = st.selectbox("Model Type:", ["GCN", "GAT", "GraphSAGE"])
            
            col1, col2 = st.columns(2)
            with col1:
                hidden_dim = st.number_input("Hidden Dimension:", min_value=8, value=64)
                epochs = st.number_input("Training Epochs:", min_value=10, value=200)
            
            with col2:
                learning_rate = st.number_input("Learning Rate:", value=0.01, format="%.3f")
            
            if st.button("Train Model"):
                st.info("Node classification training would be implemented with real graph data")
        else:
            st.error("PyTorch Geometric not available for GNN training")
    
    else:
        st.info(f"{gnn_task} implementation coming soon!")
    
    # Show capabilities overview
    if 'graph' not in st.session_state:
        st.markdown("### 🚀 Available Capabilities")
        
        capabilities = {
            "Models": ["GCN", "GAT", "GraphSAGE", "Graph Transformer"],
            "Tasks": ["Node Classification", "Link Prediction", "Graph Classification"],
            "Applications": ["Social Networks", "Knowledge Graphs", "Molecular Analysis"],
            "Advanced": ["Graph Generation", "Graph Attention", "Heterogeneous Graphs"]
        }
        
        cols = st.columns(2)
        for i, (category, methods) in enumerate(capabilities.items()):
            with cols[i % 2]:
                st.markdown(f"**{category}**")
                for method in methods:
                    st.markdown(f"• {method}")
