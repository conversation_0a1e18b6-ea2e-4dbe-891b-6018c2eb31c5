"""
MLOps Module
============

MLOps capabilities including:
- Model Versioning & Registry
- Experiment Tracking
- Model Deployment & Serving
- Monitoring & Observability
- CI/CD for ML
- Data Drift Detection
- Model Performance Tracking
"""

import os
import json
import pickle
from datetime import datetime
from typing import Dict, List, Any, Optional
import numpy as np
import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go

# Import libraries with fallbacks
try:
    import mlflow
    import mlflow.sklearn
    import mlflow.pytorch
    MLFLOW_AVAILABLE = True
except ImportError:
    MLFLOW_AVAILABLE = False

try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    WANDB_AVAILABLE = False


class ModelRegistry:
    """Model registry for versioning and management."""
    
    def __init__(self):
        self.models = {}
        self.model_metadata = {}
    
    def register_model(self, model_name: str, model_object: Any, 
                      metadata: Dict[str, Any]):
        """Register a model with metadata."""
        try:
            model_id = f"{model_name}_v{len(self.models.get(model_name, [])) + 1}"
            
            if model_name not in self.models:
                self.models[model_name] = []
                self.model_metadata[model_name] = []
            
            # Store model and metadata
            self.models[model_name].append({
                'id': model_id,
                'model': model_object,
                'timestamp': datetime.now(),
                'status': 'registered'
            })
            
            self.model_metadata[model_name].append({
                'id': model_id,
                'metadata': metadata,
                'timestamp': datetime.now()
            })
            
            return model_id
        except Exception as e:
            st.error(f"Error registering model: {str(e)}")
            return None
    
    def get_model(self, model_name: str, version: Optional[str] = None):
        """Get model by name and version."""
        try:
            if model_name not in self.models:
                return None
            
            if version is None:
                # Return latest version
                return self.models[model_name][-1]
            else:
                # Find specific version
                for model in self.models[model_name]:
                    if model['id'] == version:
                        return model
                return None
        except Exception as e:
            st.error(f"Error retrieving model: {str(e)}")
            return None
    
    def list_models(self):
        """List all registered models."""
        model_list = []
        for model_name, versions in self.models.items():
            for version in versions:
                model_list.append({
                    'name': model_name,
                    'version': version['id'],
                    'timestamp': version['timestamp'],
                    'status': version['status']
                })
        return model_list
    
    def promote_model(self, model_name: str, version: str, stage: str):
        """Promote model to different stage (staging, production)."""
        try:
            for model in self.models.get(model_name, []):
                if model['id'] == version:
                    model['status'] = stage
                    return True
            return False
        except Exception as e:
            st.error(f"Error promoting model: {str(e)}")
            return False


class ExperimentTracker:
    """Experiment tracking and logging."""
    
    def __init__(self):
        self.experiments = {}
        self.current_experiment = None
    
    def start_experiment(self, experiment_name: str, config: Dict[str, Any]):
        """Start a new experiment."""
        try:
            experiment_id = f"exp_{len(self.experiments) + 1}_{experiment_name}"
            
            self.experiments[experiment_id] = {
                'name': experiment_name,
                'config': config,
                'metrics': {},
                'artifacts': {},
                'start_time': datetime.now(),
                'status': 'running'
            }
            
            self.current_experiment = experiment_id
            return experiment_id
        except Exception as e:
            st.error(f"Error starting experiment: {str(e)}")
            return None
    
    def log_metric(self, metric_name: str, value: float, step: Optional[int] = None):
        """Log a metric value."""
        try:
            if self.current_experiment is None:
                st.warning("No active experiment. Start an experiment first.")
                return
            
            exp = self.experiments[self.current_experiment]
            
            if metric_name not in exp['metrics']:
                exp['metrics'][metric_name] = []
            
            exp['metrics'][metric_name].append({
                'value': value,
                'step': step or len(exp['metrics'][metric_name]),
                'timestamp': datetime.now()
            })
            
        except Exception as e:
            st.error(f"Error logging metric: {str(e)}")
    
    def log_parameter(self, param_name: str, value: Any):
        """Log a parameter value."""
        try:
            if self.current_experiment is None:
                return
            
            exp = self.experiments[self.current_experiment]
            exp['config'][param_name] = value
            
        except Exception as e:
            st.error(f"Error logging parameter: {str(e)}")
    
    def end_experiment(self):
        """End current experiment."""
        try:
            if self.current_experiment is None:
                return
            
            exp = self.experiments[self.current_experiment]
            exp['end_time'] = datetime.now()
            exp['status'] = 'completed'
            
            self.current_experiment = None
            
        except Exception as e:
            st.error(f"Error ending experiment: {str(e)}")
    
    def get_experiment_history(self):
        """Get experiment history."""
        history = []
        for exp_id, exp_data in self.experiments.items():
            history.append({
                'id': exp_id,
                'name': exp_data['name'],
                'status': exp_data['status'],
                'start_time': exp_data['start_time'],
                'config': exp_data['config'],
                'metrics': exp_data['metrics']
            })
        return history


class ModelMonitor:
    """Model monitoring and drift detection."""
    
    def __init__(self):
        self.monitoring_data = {}
    
    def log_prediction(self, model_name: str, input_data: Dict, 
                      prediction: Any, actual: Optional[Any] = None):
        """Log model prediction for monitoring."""
        try:
            if model_name not in self.monitoring_data:
                self.monitoring_data[model_name] = []
            
            log_entry = {
                'timestamp': datetime.now(),
                'input': input_data,
                'prediction': prediction,
                'actual': actual
            }
            
            self.monitoring_data[model_name].append(log_entry)
            
        except Exception as e:
            st.error(f"Error logging prediction: {str(e)}")
    
    def detect_data_drift(self, model_name: str, reference_data: pd.DataFrame, 
                         current_data: pd.DataFrame):
        """Detect data drift using statistical tests."""
        try:
            from scipy import stats
            
            drift_results = {}
            
            for column in reference_data.columns:
                if column in current_data.columns:
                    # Kolmogorov-Smirnov test
                    ks_stat, p_value = stats.ks_2samp(
                        reference_data[column].dropna(),
                        current_data[column].dropna()
                    )
                    
                    drift_results[column] = {
                        'ks_statistic': ks_stat,
                        'p_value': p_value,
                        'drift_detected': p_value < 0.05
                    }
            
            return drift_results
            
        except Exception as e:
            st.error(f"Error detecting data drift: {str(e)}")
            return None
    
    def calculate_model_performance(self, model_name: str):
        """Calculate model performance metrics."""
        try:
            if model_name not in self.monitoring_data:
                return None
            
            data = self.monitoring_data[model_name]
            
            # Filter entries with actual values
            labeled_data = [entry for entry in data if entry['actual'] is not None]
            
            if not labeled_data:
                return None
            
            predictions = [entry['prediction'] for entry in labeled_data]
            actuals = [entry['actual'] for entry in labeled_data]
            
            # Calculate metrics (simplified)
            from sklearn.metrics import accuracy_score, mean_squared_error
            
            try:
                # Classification metrics
                accuracy = accuracy_score(actuals, predictions)
                return {'accuracy': accuracy, 'type': 'classification'}
            except:
                # Regression metrics
                mse = mean_squared_error(actuals, predictions)
                return {'mse': mse, 'type': 'regression'}
                
        except Exception as e:
            st.error(f"Error calculating performance: {str(e)}")
            return None


def render_mlops_page():
    """Render the MLOps page."""
    st.title("🔧 MLOps")
    st.markdown("### Machine Learning Operations")
    
    # Sidebar for MLOps options
    mlops_task = st.sidebar.selectbox(
        "Select MLOps Task:",
        [
            "Model Registry",
            "Experiment Tracking",
            "Model Monitoring",
            "Deployment",
            "CI/CD Pipeline"
        ]
    )
    
    if mlops_task == "Model Registry":
        st.markdown("#### Model Registry")
        
        # Initialize registry if not exists
        if 'model_registry' not in st.session_state:
            st.session_state['model_registry'] = ModelRegistry()
        
        registry = st.session_state['model_registry']
        
        tab1, tab2, tab3 = st.tabs(["Register Model", "View Models", "Promote Model"])
        
        with tab1:
            st.markdown("**Register New Model**")
            
            model_name = st.text_input("Model Name:")
            model_type = st.selectbox("Model Type:", ["sklearn", "pytorch", "tensorflow", "custom"])
            
            col1, col2 = st.columns(2)
            with col1:
                accuracy = st.number_input("Accuracy:", 0.0, 1.0, 0.85)
                precision = st.number_input("Precision:", 0.0, 1.0, 0.80)
            with col2:
                recall = st.number_input("Recall:", 0.0, 1.0, 0.75)
                f1_score = st.number_input("F1 Score:", 0.0, 1.0, 0.77)
            
            description = st.text_area("Model Description:")
            
            if st.button("Register Model") and model_name:
                # Create dummy model object
                dummy_model = {"type": model_type, "trained": True}
                
                metadata = {
                    "accuracy": accuracy,
                    "precision": precision,
                    "recall": recall,
                    "f1_score": f1_score,
                    "description": description,
                    "model_type": model_type
                }
                
                model_id = registry.register_model(model_name, dummy_model, metadata)
                if model_id:
                    st.success(f"✅ Model registered with ID: {model_id}")
        
        with tab2:
            st.markdown("**Registered Models**")
            
            models = registry.list_models()
            if models:
                df = pd.DataFrame(models)
                st.dataframe(df)
            else:
                st.info("No models registered yet.")
        
        with tab3:
            st.markdown("**Promote Model**")
            
            models = registry.list_models()
            if models:
                model_options = [f"{m['name']} ({m['version']})" for m in models]
                selected_model = st.selectbox("Select Model:", model_options)
                
                stage = st.selectbox("Promote to Stage:", ["staging", "production", "archived"])
                
                if st.button("Promote Model") and selected_model:
                    # Parse model name and version
                    model_name = selected_model.split(" (")[0]
                    version = selected_model.split(" (")[1].rstrip(")")
                    
                    success = registry.promote_model(model_name, version, stage)
                    if success:
                        st.success(f"✅ Model promoted to {stage}")
            else:
                st.info("No models available for promotion.")
    
    elif mlops_task == "Experiment Tracking":
        st.markdown("#### Experiment Tracking")
        
        # Initialize tracker if not exists
        if 'experiment_tracker' not in st.session_state:
            st.session_state['experiment_tracker'] = ExperimentTracker()
        
        tracker = st.session_state['experiment_tracker']
        
        tab1, tab2 = st.tabs(["Run Experiment", "View History"])
        
        with tab1:
            st.markdown("**Start New Experiment**")
            
            exp_name = st.text_input("Experiment Name:")
            
            # Configuration
            st.markdown("**Configuration**")
            col1, col2 = st.columns(2)
            with col1:
                learning_rate = st.number_input("Learning Rate:", value=0.001, format="%.4f")
                batch_size = st.number_input("Batch Size:", value=32)
            with col2:
                epochs = st.number_input("Epochs:", value=10)
                optimizer = st.selectbox("Optimizer:", ["adam", "sgd", "rmsprop"])
            
            if st.button("Start Experiment") and exp_name:
                config = {
                    "learning_rate": learning_rate,
                    "batch_size": batch_size,
                    "epochs": epochs,
                    "optimizer": optimizer
                }
                
                exp_id = tracker.start_experiment(exp_name, config)
                if exp_id:
                    st.success(f"✅ Experiment started: {exp_id}")
                    
                    # Simulate training and logging metrics
                    if st.button("Simulate Training"):
                        progress_bar = st.progress(0)
                        
                        for epoch in range(epochs):
                            # Simulate metrics
                            loss = 1.0 - (epoch / epochs) * 0.8 + np.random.normal(0, 0.05)
                            accuracy = (epoch / epochs) * 0.9 + np.random.normal(0, 0.02)
                            
                            tracker.log_metric("loss", loss, epoch)
                            tracker.log_metric("accuracy", accuracy, epoch)
                            
                            progress_bar.progress((epoch + 1) / epochs)
                        
                        tracker.end_experiment()
                        st.success("✅ Training completed and logged!")
        
        with tab2:
            st.markdown("**Experiment History**")
            
            history = tracker.get_experiment_history()
            if history:
                for exp in history:
                    with st.expander(f"{exp['name']} ({exp['id']})"):
                        st.write(f"**Status:** {exp['status']}")
                        st.write(f"**Start Time:** {exp['start_time']}")
                        st.write(f"**Config:** {exp['config']}")
                        
                        # Plot metrics if available
                        if exp['metrics']:
                            for metric_name, metric_data in exp['metrics'].items():
                                values = [m['value'] for m in metric_data]
                                steps = [m['step'] for m in metric_data]
                                
                                fig = go.Figure()
                                fig.add_trace(go.Scatter(
                                    x=steps, y=values,
                                    mode='lines+markers',
                                    name=metric_name
                                ))
                                fig.update_layout(
                                    title=f"{metric_name.title()} Over Time",
                                    xaxis_title="Step",
                                    yaxis_title=metric_name.title()
                                )
                                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No experiments recorded yet.")
    
    elif mlops_task == "Model Monitoring":
        st.markdown("#### Model Monitoring")
        
        # Initialize monitor if not exists
        if 'model_monitor' not in st.session_state:
            st.session_state['model_monitor'] = ModelMonitor()
        
        monitor = st.session_state['model_monitor']
        
        tab1, tab2 = st.tabs(["Log Predictions", "Drift Detection"])
        
        with tab1:
            st.markdown("**Log Model Predictions**")
            
            model_name = st.text_input("Model Name:")
            
            # Simulate logging predictions
            if st.button("Simulate Predictions") and model_name:
                # Generate dummy predictions
                for i in range(10):
                    input_data = {"feature_1": np.random.random(), "feature_2": np.random.random()}
                    prediction = np.random.choice([0, 1])
                    actual = np.random.choice([0, 1])
                    
                    monitor.log_prediction(model_name, input_data, prediction, actual)
                
                st.success("✅ Predictions logged!")
                
                # Show performance
                performance = monitor.calculate_model_performance(model_name)
                if performance:
                    if performance['type'] == 'classification':
                        st.metric("Model Accuracy", f"{performance['accuracy']:.3f}")
                    else:
                        st.metric("Model MSE", f"{performance['mse']:.3f}")
        
        with tab2:
            st.markdown("**Data Drift Detection**")
            
            if st.button("Simulate Drift Detection"):
                # Generate reference and current data
                reference_data = pd.DataFrame({
                    'feature_1': np.random.normal(0, 1, 1000),
                    'feature_2': np.random.normal(0, 1, 1000)
                })
                
                # Current data with slight drift
                current_data = pd.DataFrame({
                    'feature_1': np.random.normal(0.2, 1.1, 1000),
                    'feature_2': np.random.normal(-0.1, 0.9, 1000)
                })
                
                drift_results = monitor.detect_data_drift("test_model", reference_data, current_data)
                
                if drift_results:
                    st.markdown("**Drift Detection Results:**")
                    for feature, result in drift_results.items():
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.write(f"**{feature}**")
                        with col2:
                            st.write(f"P-value: {result['p_value']:.4f}")
                        with col3:
                            drift_status = "🚨 Drift Detected" if result['drift_detected'] else "✅ No Drift"
                            st.write(drift_status)
    
    else:
        st.info(f"{mlops_task} implementation coming soon!")
    
    # Show MLOps overview
    st.markdown("### 🚀 MLOps Capabilities")
    
    capabilities = {
        "Model Management": ["Versioning", "Registry", "Promotion", "Rollback"],
        "Monitoring": ["Performance", "Drift Detection", "Alerts", "Logging"],
        "Deployment": ["Batch", "Real-time", "A/B Testing", "Canary"],
        "Automation": ["CI/CD", "Auto-scaling", "Auto-retraining", "Testing"]
    }
    
    cols = st.columns(2)
    for i, (category, methods) in enumerate(capabilities.items()):
        with cols[i % 2]:
            st.markdown(f"**{category}**")
            for method in methods:
                st.markdown(f"• {method}")
