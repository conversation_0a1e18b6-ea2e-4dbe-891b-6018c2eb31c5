"""
Natural Language Processing Module
=================================

Comprehensive NLP capabilities including:
- Text Classification & Sentiment Analysis
- Named Entity Recognition (NER)
- Text Summarization
- Question Answering
- Language Translation
- Text Generation
- Topic Modeling
- Information Extraction
- Conversational AI
"""

import os
import re
from typing import Any, Dict, List, Optional, Tuple, Union
import numpy as np
import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from collections import Counter

# Import libraries with fallbacks
try:
    from transformers import (
        AutoTokenizer, AutoModel, AutoModelForSequenceClassification,
        AutoModelForQuestionAnswering, AutoModelForSummarization,
        pipeline, BertTokenizer, BertModel
    )
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    st.warning("Transformers library not available")

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

try:
    import nltk
    from nltk.sentiment import SentimentIntensityAnalyzer
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize, sent_tokenize
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

try:
    from textblob import TextBlob
    TEXTBLOB_AVAILABLE = True
except ImportError:
    TEXTBLOB_AVAILABLE = False

try:
    import gensim
    from gensim import corpora
    from gensim.models import LdaModel, Word2Vec
    GENSIM_AVAILABLE = True
except ImportError:
    GENSIM_AVAILABLE = False


class TextClassifier:
    """Advanced text classification using transformer models."""

    def __init__(self):
        self.models = {}
        self.tokenizers = {}

    def load_pretrained_classifier(self, model_name: str = "distilbert-base-uncased-finetuned-sst-2-english"):
        """Load pretrained classification model."""
        if not TRANSFORMERS_AVAILABLE:
            st.error("Transformers library not available")
            return None

        try:
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForSequenceClassification.from_pretrained(model_name)

            self.tokenizers[model_name] = tokenizer
            self.models[model_name] = model

            return model
        except Exception as e:
            st.error(f"Error loading model {model_name}: {str(e)}")
            return None

    def classify_text(self, text: str, model_name: str = "distilbert-base-uncased-finetuned-sst-2-english"):
        """Classify text using pretrained model."""
        if model_name not in self.models:
            self.load_pretrained_classifier(model_name)

        try:
            classifier = pipeline("text-classification",
                                model=self.models[model_name],
                                tokenizer=self.tokenizers[model_name])

            result = classifier(text)
            return result
        except Exception as e:
            st.error(f"Error during classification: {str(e)}")
            return None

    def batch_classify(self, texts: List[str], model_name: str = "distilbert-base-uncased-finetuned-sst-2-english"):
        """Classify multiple texts."""
        results = []
        for text in texts:
            result = self.classify_text(text, model_name)
            if result:
                results.append(result[0])
        return results


class SentimentAnalyzer:
    """Multi-method sentiment analysis."""

    def __init__(self):
        self.sia = None
        self._load_nltk_analyzer()

    def _load_nltk_analyzer(self):
        """Load NLTK sentiment analyzer."""
        if NLTK_AVAILABLE:
            try:
                nltk.download('vader_lexicon', quiet=True)
                self.sia = SentimentIntensityAnalyzer()
            except Exception as e:
                st.warning(f"Could not load NLTK sentiment analyzer: {str(e)}")

    def analyze_sentiment_nltk(self, text: str):
        """Analyze sentiment using NLTK VADER."""
        if self.sia is None:
            return None

        try:
            scores = self.sia.polarity_scores(text)
            return {
                'compound': scores['compound'],
                'positive': scores['pos'],
                'negative': scores['neg'],
                'neutral': scores['neu'],
                'sentiment': 'positive' if scores['compound'] > 0.05 else 'negative' if scores['compound'] < -0.05 else 'neutral'
            }
        except Exception as e:
            st.error(f"Error in NLTK sentiment analysis: {str(e)}")
            return None

    def analyze_sentiment_textblob(self, text: str):
        """Analyze sentiment using TextBlob."""
        if not TEXTBLOB_AVAILABLE:
            return None

        try:
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity
            subjectivity = blob.sentiment.subjectivity

            return {
                'polarity': polarity,
                'subjectivity': subjectivity,
                'sentiment': 'positive' if polarity > 0.1 else 'negative' if polarity < -0.1 else 'neutral'
            }
        except Exception as e:
            st.error(f"Error in TextBlob sentiment analysis: {str(e)}")
            return None

    def analyze_sentiment_transformer(self, text: str):
        """Analyze sentiment using transformer model."""
        if not TRANSFORMERS_AVAILABLE:
            return None

        try:
            classifier = pipeline("sentiment-analysis")
            result = classifier(text)
            return result[0]
        except Exception as e:
            st.error(f"Error in transformer sentiment analysis: {str(e)}")
            return None


class NamedEntityRecognizer:
    """Named Entity Recognition using multiple approaches."""

    def __init__(self):
        self.spacy_nlp = None
        self._load_spacy_model()

    def _load_spacy_model(self):
        """Load spaCy model."""
        if SPACY_AVAILABLE:
            try:
                self.spacy_nlp = spacy.load("en_core_web_sm")
            except OSError:
                st.warning("spaCy English model not found. Install with: python -m spacy download en_core_web_sm")

    def extract_entities_spacy(self, text: str):
        """Extract entities using spaCy."""
        if self.spacy_nlp is None:
            return None

        try:
            doc = self.spacy_nlp(text)
            entities = []
            for ent in doc.ents:
                entities.append({
                    'text': ent.text,
                    'label': ent.label_,
                    'start': ent.start_char,
                    'end': ent.end_char,
                    'description': spacy.explain(ent.label_)
                })
            return entities
        except Exception as e:
            st.error(f"Error in spaCy NER: {str(e)}")
            return None

    def extract_entities_transformer(self, text: str):
        """Extract entities using transformer model."""
        if not TRANSFORMERS_AVAILABLE:
            return None

        try:
            ner_pipeline = pipeline("ner", aggregation_strategy="simple")
            entities = ner_pipeline(text)
            return entities
        except Exception as e:
            st.error(f"Error in transformer NER: {str(e)}")
            return None


class TextSummarizer:
    """Text summarization using various methods."""

    def __init__(self):
        pass

    def summarize_extractive(self, text: str, num_sentences: int = 3):
        """Extractive summarization using sentence ranking."""
        if not NLTK_AVAILABLE:
            st.error("NLTK not available for extractive summarization")
            return None

        try:
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)

            sentences = sent_tokenize(text)
            if len(sentences) <= num_sentences:
                return text

            # Simple frequency-based ranking
            words = word_tokenize(text.lower())
            stop_words = set(stopwords.words('english'))
            words = [word for word in words if word.isalnum() and word not in stop_words]

            word_freq = Counter(words)

            sentence_scores = {}
            for sentence in sentences:
                sentence_words = word_tokenize(sentence.lower())
                score = 0
                word_count = 0
                for word in sentence_words:
                    if word in word_freq:
                        score += word_freq[word]
                        word_count += 1
                if word_count > 0:
                    sentence_scores[sentence] = score / word_count

            # Get top sentences
            top_sentences = sorted(sentence_scores.items(), key=lambda x: x[1], reverse=True)[:num_sentences]
            summary = ' '.join([sent[0] for sent in top_sentences])

            return summary
        except Exception as e:
            st.error(f"Error in extractive summarization: {str(e)}")
            return None

    def summarize_abstractive(self, text: str, max_length: int = 150):
        """Abstractive summarization using transformer model."""
        if not TRANSFORMERS_AVAILABLE:
            return None

        try:
            summarizer = pipeline("summarization")
            summary = summarizer(text, max_length=max_length, min_length=30, do_sample=False)
            return summary[0]['summary_text']
        except Exception as e:
            st.error(f"Error in abstractive summarization: {str(e)}")
            return None


class QuestionAnswering:
    """Question answering system."""

    def __init__(self):
        self.qa_pipeline = None
        self._load_qa_model()

    def _load_qa_model(self):
        """Load QA model."""
        if TRANSFORMERS_AVAILABLE:
            try:
                self.qa_pipeline = pipeline("question-answering")
            except Exception as e:
                st.warning(f"Could not load QA model: {str(e)}")

    def answer_question(self, context: str, question: str):
        """Answer question based on context."""
        if self.qa_pipeline is None:
            return None

        try:
            result = self.qa_pipeline(question=question, context=context)
            return result
        except Exception as e:
            st.error(f"Error in question answering: {str(e)}")
            return None


class TopicModeling:
    """Topic modeling using LDA and other methods."""

    def __init__(self):
        pass

    def lda_topic_modeling(self, texts: List[str], num_topics: int = 5):
        """Perform LDA topic modeling."""
        if not GENSIM_AVAILABLE or not NLTK_AVAILABLE:
            st.error("Gensim or NLTK not available for topic modeling")
            return None

        try:
            nltk.download('stopwords', quiet=True)
            nltk.download('punkt', quiet=True)

            # Preprocess texts
            stop_words = set(stopwords.words('english'))
            processed_texts = []

            for text in texts:
                words = word_tokenize(text.lower())
                words = [word for word in words if word.isalnum() and word not in stop_words and len(word) > 2]
                processed_texts.append(words)

            # Create dictionary and corpus
            dictionary = corpora.Dictionary(processed_texts)
            corpus = [dictionary.doc2bow(text) for text in processed_texts]

            # Train LDA model
            lda_model = LdaModel(corpus=corpus, id2word=dictionary, num_topics=num_topics,
                               random_state=42, passes=10, alpha='auto', per_word_topics=True)

            # Extract topics
            topics = []
            for idx, topic in lda_model.print_topics(-1):
                topics.append({
                    'topic_id': idx,
                    'words': topic
                })

            return topics, lda_model
        except Exception as e:
            st.error(f"Error in topic modeling: {str(e)}")
            return None, None


def render_nlp_page():
    """Render the NLP page."""
    st.title("📝 Natural Language Processing")
    st.markdown("### Advanced NLP Capabilities")

    # Sidebar for NLP options
    nlp_task = st.sidebar.selectbox(
        "Select NLP Task:",
        [
            "Text Classification",
            "Sentiment Analysis",
            "Named Entity Recognition",
            "Text Summarization",
            "Question Answering",
            "Topic Modeling",
            "Language Translation",
            "Text Generation"
        ]
    )

    # Text input
    text_input_method = st.radio(
        "Input method:",
        ["Text Area", "File Upload"]
    )

    text_data = None

    if text_input_method == "Text Area":
        text_data = st.text_area(
            "Enter your text:",
            height=200,
            placeholder="Type or paste your text here..."
        )
    else:
        uploaded_file = st.file_uploader(
            "Upload text file",
            type=['txt', 'csv'],
            help="Supported formats: TXT, CSV"
        )

        if uploaded_file is not None:
            if uploaded_file.name.endswith('.txt'):
                text_data = str(uploaded_file.read(), "utf-8")
            elif uploaded_file.name.endswith('.csv'):
                df = pd.read_csv(uploaded_file)
                text_column = st.selectbox("Select text column:", df.columns)
                text_data = df[text_column].tolist()

    if text_data:
        col1, col2 = st.columns([1, 1])

        with col1:
            st.markdown("#### Input Text")
            if isinstance(text_data, str):
                st.text_area("", text_data, height=300, disabled=True)
            else:
                st.dataframe(pd.DataFrame({'texts': text_data[:10]}))  # Show first 10

        with col2:
            st.markdown("#### Analysis Results")

            if nlp_task == "Sentiment Analysis":
                analyzer = SentimentAnalyzer()

                method = st.selectbox(
                    "Analysis Method:",
                    ["NLTK VADER", "TextBlob", "Transformer"]
                )

                if st.button("Analyze Sentiment"):
                    with st.spinner("Analyzing sentiment..."):
                        if isinstance(text_data, str):
                            if method == "NLTK VADER":
                                result = analyzer.analyze_sentiment_nltk(text_data)
                            elif method == "TextBlob":
                                result = analyzer.analyze_sentiment_textblob(text_data)
                            else:
                                result = analyzer.analyze_sentiment_transformer(text_data)

                            if result:
                                st.json(result)
                        else:
                            # Batch processing
                            results = []
                            for text in text_data[:50]:  # Limit to 50 for demo
                                if method == "NLTK VADER":
                                    result = analyzer.analyze_sentiment_nltk(text)
                                elif method == "TextBlob":
                                    result = analyzer.analyze_sentiment_textblob(text)
                                else:
                                    result = analyzer.analyze_sentiment_transformer(text)

                                if result:
                                    results.append(result)

                            if results:
                                df_results = pd.DataFrame(results)
                                st.dataframe(df_results)

                                # Visualization
                                if 'sentiment' in df_results.columns:
                                    sentiment_counts = df_results['sentiment'].value_counts()
                                    fig = px.pie(values=sentiment_counts.values,
                                               names=sentiment_counts.index,
                                               title="Sentiment Distribution")
                                    st.plotly_chart(fig, use_container_width=True)

            elif nlp_task == "Named Entity Recognition":
                ner = NamedEntityRecognizer()

                method = st.selectbox(
                    "NER Method:",
                    ["spaCy", "Transformer"]
                )

                if st.button("Extract Entities") and isinstance(text_data, str):
                    with st.spinner("Extracting entities..."):
                        if method == "spaCy":
                            entities = ner.extract_entities_spacy(text_data)
                        else:
                            entities = ner.extract_entities_transformer(text_data)

                        if entities:
                            st.markdown("**Found Entities:**")
                            df_entities = pd.DataFrame(entities)
                            st.dataframe(df_entities)

                            # Entity type distribution
                            if 'label' in df_entities.columns:
                                entity_counts = df_entities['label'].value_counts()
                                fig = px.bar(x=entity_counts.index, y=entity_counts.values,
                                           title="Entity Types Distribution")
                                st.plotly_chart(fig, use_container_width=True)

            elif nlp_task == "Text Summarization":
                summarizer = TextSummarizer()

                method = st.selectbox(
                    "Summarization Method:",
                    ["Extractive", "Abstractive"]
                )

                if method == "Extractive":
                    num_sentences = st.slider("Number of sentences:", 1, 10, 3)
                else:
                    max_length = st.slider("Max length:", 50, 300, 150)

                if st.button("Summarize") and isinstance(text_data, str):
                    with st.spinner("Generating summary..."):
                        if method == "Extractive":
                            summary = summarizer.summarize_extractive(text_data, num_sentences)
                        else:
                            summary = summarizer.summarize_abstractive(text_data, max_length)

                        if summary:
                            st.markdown("**Summary:**")
                            st.write(summary)

            elif nlp_task == "Question Answering":
                qa = QuestionAnswering()

                question = st.text_input("Enter your question:")

                if st.button("Answer Question") and question and isinstance(text_data, str):
                    with st.spinner("Finding answer..."):
                        answer = qa.answer_question(text_data, question)

                        if answer:
                            st.markdown("**Answer:**")
                            st.write(answer['answer'])
                            st.markdown(f"**Confidence:** {answer['score']:.3f}")

            elif nlp_task == "Topic Modeling":
                topic_modeler = TopicModeling()

                if isinstance(text_data, list):
                    num_topics = st.slider("Number of topics:", 2, 20, 5)

                    if st.button("Extract Topics"):
                        with st.spinner("Modeling topics..."):
                            topics, model = topic_modeler.lda_topic_modeling(text_data, num_topics)

                            if topics:
                                st.markdown("**Discovered Topics:**")
                                for topic in topics:
                                    st.write(f"**Topic {topic['topic_id']}:** {topic['words']}")
                else:
                    st.warning("Topic modeling requires multiple texts. Please upload a CSV file.")

            elif nlp_task == "Language Translation":
                if TRANSFORMERS_AVAILABLE:
                    source_lang = st.selectbox("Source Language:", ["en", "es", "fr", "de", "it", "pt"])
                    target_lang = st.selectbox("Target Language:", ["en", "es", "fr", "de", "it", "pt"])

                    if st.button("Translate") and isinstance(text_data, str):
                        with st.spinner("Translating..."):
                            try:
                                translator = pipeline("translation",
                                                    model=f"Helsinki-NLP/opus-mt-{source_lang}-{target_lang}")
                                result = translator(text_data)
                                st.markdown("**Translation:**")
                                st.write(result[0]['translation_text'])
                            except Exception as e:
                                st.error(f"Translation error: {str(e)}")
                else:
                    st.error("Transformers library required for translation")

            elif nlp_task == "Text Generation":
                if TRANSFORMERS_AVAILABLE:
                    prompt = st.text_input("Enter prompt for text generation:")
                    max_length = st.slider("Max length:", 50, 500, 100)

                    if st.button("Generate Text") and prompt:
                        with st.spinner("Generating text..."):
                            try:
                                generator = pipeline("text-generation", model="gpt2")
                                result = generator(prompt, max_length=max_length, num_return_sequences=1)
                                st.markdown("**Generated Text:**")
                                st.write(result[0]['generated_text'])
                            except Exception as e:
                                st.error(f"Generation error: {str(e)}")
                else:
                    st.error("Transformers library required for text generation")

            else:
                st.info(f"{nlp_task} implementation coming soon!")

    else:
        st.info("👆 Enter text or upload a file to start NLP analysis")

        # Show capabilities overview
        st.markdown("### 🚀 Available Capabilities")

        capabilities = {
            "Classification": ["BERT", "RoBERTa", "DistilBERT", "Custom Models"],
            "Sentiment": ["VADER", "TextBlob", "Transformers", "Ensemble"],
            "NER": ["spaCy", "BERT-NER", "BiLSTM-CRF", "Custom"],
            "Summarization": ["Extractive", "Abstractive", "BART", "T5"],
            "QA": ["BERT-QA", "RoBERTa-QA", "DistilBERT", "Custom"],
            "Translation": ["Helsinki-NLP", "mBART", "T5", "Custom"],
            "Generation": ["GPT-2", "GPT-3", "T5", "BART", "Custom LLMs"]
        }

        cols = st.columns(3)
        for i, (category, methods) in enumerate(capabilities.items()):
            with cols[i % 3]:
                st.markdown(f"**{category}**")
                for method in methods:
                    st.markdown(f"• {method}")
