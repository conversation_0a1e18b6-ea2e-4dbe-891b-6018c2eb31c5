"""
Time Series Analysis Module
===========================

Comprehensive time series analysis capabilities including:
- Forecasting (ARIMA, Prophet, Neural Networks)
- Anomaly Detection
- Trend Analysis & Decomposition
- Seasonality Analysis
- Change Point Detection
- Multi-variate Time Series
- Real-time Streaming Analysis
"""

import warnings
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import pandas as pd
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# Suppress warnings
warnings.filterwarnings('ignore')

# Import libraries with fallbacks
try:
    from statsmodels.tsa.arima.model import ARIMA
    from statsmodels.tsa.seasonal import seasonal_decompose
    from statsmodels.tsa.stattools import adfuller, kpss
    from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False
    st.warning("Statsmodels not available for classical time series analysis")

try:
    from prophet import Prophet
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False

try:
    import pmdarima as pm
    from pmdarima import auto_arima
    PMDARIMA_AVAILABLE = True
except ImportError:
    PMDARIMA_AVAILABLE = False

try:
    from sklearn.ensemble import IsolationForest
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import mean_absolute_error, mean_squared_error
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    from autogluon.timeseries import TimeSeriesPredictor, TimeSeriesDataFrame
    AUTOGLUON_TS_AVAILABLE = True
except ImportError:
    AUTOGLUON_TS_AVAILABLE = False


class TimeSeriesAnalyzer:
    """Comprehensive time series analysis and preprocessing."""
    
    def __init__(self):
        self.data = None
        self.date_column = None
        self.value_columns = []
        
    def load_data(self, df: pd.DataFrame, date_column: str, value_columns: List[str]):
        """Load and prepare time series data."""
        try:
            self.data = df.copy()
            self.date_column = date_column
            self.value_columns = value_columns
            
            # Convert date column to datetime
            self.data[date_column] = pd.to_datetime(self.data[date_column])
            self.data = self.data.sort_values(date_column)
            self.data.set_index(date_column, inplace=True)
            
            return True
        except Exception as e:
            st.error(f"Error loading time series data: {str(e)}")
            return False
    
    def check_stationarity(self, series: pd.Series):
        """Check stationarity using ADF and KPSS tests."""
        if not STATSMODELS_AVAILABLE:
            return None
            
        try:
            # ADF Test
            adf_result = adfuller(series.dropna())
            adf_statistic = adf_result[0]
            adf_pvalue = adf_result[1]
            adf_critical = adf_result[4]
            
            # KPSS Test
            kpss_result = kpss(series.dropna())
            kpss_statistic = kpss_result[0]
            kpss_pvalue = kpss_result[1]
            kpss_critical = kpss_result[3]
            
            return {
                'adf_statistic': adf_statistic,
                'adf_pvalue': adf_pvalue,
                'adf_critical_values': adf_critical,
                'adf_stationary': adf_pvalue < 0.05,
                'kpss_statistic': kpss_statistic,
                'kpss_pvalue': kpss_pvalue,
                'kpss_critical_values': kpss_critical,
                'kpss_stationary': kpss_pvalue > 0.05
            }
        except Exception as e:
            st.error(f"Error in stationarity test: {str(e)}")
            return None
    
    def decompose_series(self, series: pd.Series, model: str = 'additive', period: int = None):
        """Decompose time series into trend, seasonal, and residual components."""
        if not STATSMODELS_AVAILABLE:
            return None
            
        try:
            if period is None:
                # Try to infer period
                period = min(len(series) // 2, 365)  # Default to yearly for daily data
            
            decomposition = seasonal_decompose(series.dropna(), model=model, period=period)
            
            return {
                'trend': decomposition.trend,
                'seasonal': decomposition.seasonal,
                'residual': decomposition.resid,
                'observed': decomposition.observed
            }
        except Exception as e:
            st.error(f"Error in decomposition: {str(e)}")
            return None
    
    def detect_outliers(self, series: pd.Series, method: str = 'isolation_forest'):
        """Detect outliers in time series."""
        if not SKLEARN_AVAILABLE:
            return None
            
        try:
            if method == 'isolation_forest':
                # Reshape for sklearn
                X = series.values.reshape(-1, 1)
                
                # Fit Isolation Forest
                iso_forest = IsolationForest(contamination=0.1, random_state=42)
                outliers = iso_forest.fit_predict(X)
                
                # -1 indicates outlier
                outlier_indices = series.index[outliers == -1]
                
                return {
                    'outlier_indices': outlier_indices,
                    'outlier_values': series.loc[outlier_indices],
                    'normal_indices': series.index[outliers == 1]
                }
            
            elif method == 'statistical':
                # Statistical method using IQR
                Q1 = series.quantile(0.25)
                Q3 = series.quantile(0.75)
                IQR = Q3 - Q1
                
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = series[(series < lower_bound) | (series > upper_bound)]
                
                return {
                    'outlier_indices': outliers.index,
                    'outlier_values': outliers,
                    'bounds': {'lower': lower_bound, 'upper': upper_bound}
                }
                
        except Exception as e:
            st.error(f"Error in outlier detection: {str(e)}")
            return None


class TimeSeriesForecaster:
    """Time series forecasting using various methods."""
    
    def __init__(self):
        self.models = {}
        
    def arima_forecast(self, series: pd.Series, steps: int = 30, order: Tuple = None):
        """ARIMA forecasting."""
        if not STATSMODELS_AVAILABLE:
            st.error("Statsmodels not available for ARIMA")
            return None
            
        try:
            if order is None and PMDARIMA_AVAILABLE:
                # Auto ARIMA
                auto_model = auto_arima(series.dropna(), seasonal=False, stepwise=True, 
                                      suppress_warnings=True, error_action='ignore')
                order = auto_model.order
            elif order is None:
                order = (1, 1, 1)  # Default order
            
            # Fit ARIMA model
            model = ARIMA(series.dropna(), order=order)
            fitted_model = model.fit()
            
            # Forecast
            forecast = fitted_model.forecast(steps=steps)
            conf_int = fitted_model.get_forecast(steps=steps).conf_int()
            
            # Create forecast index
            last_date = series.index[-1]
            forecast_index = pd.date_range(start=last_date + pd.Timedelta(days=1), 
                                         periods=steps, freq='D')
            
            return {
                'forecast': pd.Series(forecast, index=forecast_index),
                'confidence_intervals': conf_int,
                'model': fitted_model,
                'order': order,
                'aic': fitted_model.aic,
                'bic': fitted_model.bic
            }
            
        except Exception as e:
            st.error(f"Error in ARIMA forecasting: {str(e)}")
            return None
    
    def prophet_forecast(self, series: pd.Series, steps: int = 30):
        """Prophet forecasting."""
        if not PROPHET_AVAILABLE:
            st.error("Prophet not available")
            return None
            
        try:
            # Prepare data for Prophet
            df = pd.DataFrame({
                'ds': series.index,
                'y': series.values
            })
            
            # Initialize and fit Prophet model
            model = Prophet(daily_seasonality=True, yearly_seasonality=True)
            model.fit(df)
            
            # Create future dataframe
            future = model.make_future_dataframe(periods=steps)
            forecast = model.predict(future)
            
            return {
                'forecast': forecast,
                'model': model,
                'components': model.predict(future)
            }
            
        except Exception as e:
            st.error(f"Error in Prophet forecasting: {str(e)}")
            return None
    
    def autogluon_forecast(self, df: pd.DataFrame, target_column: str, steps: int = 30):
        """AutoGluon time series forecasting."""
        if not AUTOGLUON_TS_AVAILABLE:
            st.error("AutoGluon TimeSeries not available")
            return None
            
        try:
            # Prepare data for AutoGluon
            ts_df = TimeSeriesDataFrame.from_data_frame(
                df, 
                id_column=None,
                timestamp_column=df.index.name or 'timestamp'
            )
            
            # Initialize predictor
            predictor = TimeSeriesPredictor(
                prediction_length=steps,
                target=target_column,
                eval_metric='MAPE'
            )
            
            # Fit model
            predictor.fit(ts_df)
            
            # Forecast
            predictions = predictor.predict(ts_df)
            
            return {
                'forecast': predictions,
                'model': predictor,
                'leaderboard': predictor.leaderboard()
            }
            
        except Exception as e:
            st.error(f"Error in AutoGluon forecasting: {str(e)}")
            return None


class AnomalyDetector:
    """Time series anomaly detection."""
    
    def __init__(self):
        pass
    
    def statistical_anomaly_detection(self, series: pd.Series, window: int = 30, threshold: float = 3.0):
        """Statistical anomaly detection using rolling statistics."""
        try:
            # Calculate rolling mean and std
            rolling_mean = series.rolling(window=window).mean()
            rolling_std = series.rolling(window=window).std()
            
            # Calculate z-scores
            z_scores = (series - rolling_mean) / rolling_std
            
            # Identify anomalies
            anomalies = series[abs(z_scores) > threshold]
            
            return {
                'anomalies': anomalies,
                'z_scores': z_scores,
                'rolling_mean': rolling_mean,
                'rolling_std': rolling_std,
                'threshold': threshold
            }
            
        except Exception as e:
            st.error(f"Error in statistical anomaly detection: {str(e)}")
            return None
    
    def isolation_forest_anomaly_detection(self, series: pd.Series, contamination: float = 0.1):
        """Isolation Forest anomaly detection."""
        if not SKLEARN_AVAILABLE:
            return None
            
        try:
            # Prepare features (value, rolling mean, rolling std)
            features = pd.DataFrame({
                'value': series,
                'rolling_mean_7': series.rolling(7).mean(),
                'rolling_std_7': series.rolling(7).std(),
                'rolling_mean_30': series.rolling(30).mean(),
                'rolling_std_30': series.rolling(30).std()
            }).dropna()
            
            # Fit Isolation Forest
            iso_forest = IsolationForest(contamination=contamination, random_state=42)
            anomaly_labels = iso_forest.fit_predict(features)
            
            # Get anomalies
            anomaly_indices = features.index[anomaly_labels == -1]
            anomalies = series.loc[anomaly_indices]
            
            return {
                'anomalies': anomalies,
                'anomaly_indices': anomaly_indices,
                'anomaly_scores': iso_forest.decision_function(features)
            }
            
        except Exception as e:
            st.error(f"Error in Isolation Forest anomaly detection: {str(e)}")
            return None


def render_time_series_page():
    """Render the time series analysis page."""
    st.title("📈 Time Series Analysis")
    st.markdown("### Advanced Time Series Analytics")
    
    # Sidebar for time series options
    ts_task = st.sidebar.selectbox(
        "Select Time Series Task:",
        [
            "Data Upload & Exploration",
            "Stationarity Analysis",
            "Decomposition",
            "Forecasting",
            "Anomaly Detection",
            "Trend Analysis",
            "Seasonality Analysis"
        ]
    )
    
    # Initialize analyzer
    analyzer = TimeSeriesAnalyzer()
    forecaster = TimeSeriesForecaster()
    anomaly_detector = AnomalyDetector()
    
    if ts_task == "Data Upload & Exploration":
        st.markdown("#### Upload Time Series Data")
        
        uploaded_file = st.file_uploader(
            "Upload CSV file",
            type=['csv'],
            help="CSV file with date and numeric columns"
        )
        
        if uploaded_file is not None:
            df = pd.read_csv(uploaded_file)
            st.dataframe(df.head())
            
            # Column selection
            date_column = st.selectbox("Select date column:", df.columns)
            value_columns = st.multiselect("Select value columns:", 
                                         [col for col in df.columns if col != date_column])
            
            if date_column and value_columns:
                if analyzer.load_data(df, date_column, value_columns):
                    st.success("✅ Time series data loaded successfully!")
                    
                    # Basic visualization
                    fig = go.Figure()
                    for col in value_columns:
                        fig.add_trace(go.Scatter(
                            x=analyzer.data.index,
                            y=analyzer.data[col],
                            mode='lines',
                            name=col
                        ))
                    
                    fig.update_layout(
                        title="Time Series Data",
                        xaxis_title="Date",
                        yaxis_title="Value"
                    )
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # Store data in session state for other tasks
                    st.session_state['ts_data'] = analyzer.data
                    st.session_state['ts_value_columns'] = value_columns
    
    elif 'ts_data' in st.session_state:
        data = st.session_state['ts_data']
        value_columns = st.session_state['ts_value_columns']
        
        selected_column = st.selectbox("Select column for analysis:", value_columns)
        series = data[selected_column]
        
        if ts_task == "Stationarity Analysis":
            st.markdown("#### Stationarity Analysis")
            
            if st.button("Run Stationarity Tests"):
                with st.spinner("Running stationarity tests..."):
                    results = analyzer.check_stationarity(series)
                    
                    if results:
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.markdown("**ADF Test Results**")
                            st.write(f"Statistic: {results['adf_statistic']:.4f}")
                            st.write(f"P-value: {results['adf_pvalue']:.4f}")
                            st.write(f"Stationary: {results['adf_stationary']}")
                        
                        with col2:
                            st.markdown("**KPSS Test Results**")
                            st.write(f"Statistic: {results['kpss_statistic']:.4f}")
                            st.write(f"P-value: {results['kpss_pvalue']:.4f}")
                            st.write(f"Stationary: {results['kpss_stationary']}")
        
        elif ts_task == "Forecasting":
            st.markdown("#### Time Series Forecasting")
            
            method = st.selectbox("Forecasting Method:", ["ARIMA", "Prophet", "AutoGluon"])
            steps = st.slider("Forecast Steps:", 1, 365, 30)
            
            if st.button("Generate Forecast"):
                with st.spinner(f"Generating {method} forecast..."):
                    if method == "ARIMA":
                        result = forecaster.arima_forecast(series, steps)
                    elif method == "Prophet":
                        result = forecaster.prophet_forecast(series, steps)
                    else:
                        result = forecaster.autogluon_forecast(data, selected_column, steps)
                    
                    if result:
                        st.success("✅ Forecast generated successfully!")
                        
                        # Visualization would go here
                        st.info("Forecast visualization implementation")
        
        elif ts_task == "Anomaly Detection":
            st.markdown("#### Anomaly Detection")
            
            method = st.selectbox("Detection Method:", ["Statistical", "Isolation Forest"])
            
            if method == "Statistical":
                window = st.slider("Rolling Window:", 5, 100, 30)
                threshold = st.slider("Z-Score Threshold:", 1.0, 5.0, 3.0)
                
                if st.button("Detect Anomalies"):
                    result = anomaly_detector.statistical_anomaly_detection(series, window, threshold)
            else:
                contamination = st.slider("Contamination Rate:", 0.01, 0.3, 0.1)
                
                if st.button("Detect Anomalies"):
                    result = anomaly_detector.isolation_forest_anomaly_detection(series, contamination)
            
            # Visualization would be implemented here
    
    else:
        st.info("👆 Please upload time series data first in 'Data Upload & Exploration'")
        
        # Show capabilities overview
        st.markdown("### 🚀 Available Capabilities")
        
        capabilities = {
            "Forecasting": ["ARIMA", "Prophet", "AutoGluon", "Neural Networks"],
            "Anomaly Detection": ["Statistical", "Isolation Forest", "LSTM", "Ensemble"],
            "Analysis": ["Stationarity", "Decomposition", "Trend", "Seasonality"],
            "Advanced": ["Multi-variate", "Change Points", "Regime Detection", "Streaming"]
        }
        
        cols = st.columns(2)
        for i, (category, methods) in enumerate(capabilities.items()):
            with cols[i % 2]:
                st.markdown(f"**{category}**")
                for method in methods:
                    st.markdown(f"• {method}")
