# Prometheus Configuration
# Comprehensive monitoring setup for the AI/ML Platform

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'aiml-platform'
    environment: 'production'

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    kubernetes_sd_configs:
      - role: endpoints
    relabel_configs:
      - source_labels: [__meta_kubernetes_endpoints_name]
        action: keep
        regex: node-exporter
      - source_labels: [__meta_kubernetes_endpoint_port_name]
        action: keep
        regex: metrics

  # Kubernetes API Server
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
      - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;kubernetes;https

  # Kubernetes Nodes
  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
      - role: node
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - target_label: __address__
        replacement: kubernetes.default.svc:443
      - source_labels: [__meta_kubernetes_node_name]
        regex: (.+)
        target_label: __metrics_path__
        replacement: /api/v1/nodes/${1}/proxy/metrics

  # Kubernetes Pods
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_pod_name]
        action: replace
        target_label: kubernetes_pod_name

  # AI/ML Platform Backend
  - job_name: 'aiml-platform-backend'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - aiml-platform
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: aiml-platform-backend-service
      - source_labels: [__meta_kubernetes_endpoint_port_name]
        action: keep
        regex: http
    metrics_path: /metrics
    scrape_interval: 10s

  # AI/ML Platform Frontend
  - job_name: 'aiml-platform-frontend'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - aiml-platform
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: aiml-platform-frontend-service
      - source_labels: [__meta_kubernetes_endpoint_port_name]
        action: keep
        regex: http
    metrics_path: /metrics
    scrape_interval: 30s

  # PostgreSQL
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # MongoDB
  - job_name: 'mongodb-exporter'
    static_configs:
      - targets: ['mongodb-exporter:9216']
    scrape_interval: 30s

  # Nginx Ingress Controller
  - job_name: 'nginx-ingress'
    kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
            - ingress-nginx
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
        action: keep
        regex: ingress-nginx
      - source_labels: [__meta_kubernetes_pod_container_port_number]
        action: keep
        regex: "10254"
    metrics_path: /metrics

  # Celery Workers
  - job_name: 'celery-exporter'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - aiml-platform
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: celery-exporter-service
    scrape_interval: 30s

  # GPU Metrics (if available)
  - job_name: 'nvidia-dcgm-exporter'
    kubernetes_sd_configs:
      - role: endpoints
    relabel_configs:
      - source_labels: [__meta_kubernetes_endpoints_name]
        action: keep
        regex: nvidia-dcgm-exporter
    scrape_interval: 30s

  # Elasticsearch
  - job_name: 'elasticsearch-exporter'
    static_configs:
      - targets: ['elasticsearch-exporter:9114']
    scrape_interval: 30s

  # Kafka (if used for streaming)
  - job_name: 'kafka-exporter'
    static_configs:
      - targets: ['kafka-exporter:9308']
    scrape_interval: 30s

  # Custom ML Model Metrics
  - job_name: 'ml-model-metrics'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - aiml-platform
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape_ml_metrics]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
    scrape_interval: 60s

  # Blackbox Exporter for endpoint monitoring
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://aiml-platform.com/health
        - https://api.aiml-platform.com/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

# Recording rules for performance optimization
recording_rules:
  - name: aiml_platform_rules
    rules:
      - record: aiml:request_rate_5m
        expr: rate(http_requests_total[5m])
      
      - record: aiml:error_rate_5m
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])
      
      - record: aiml:response_time_95p
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))
      
      - record: aiml:cpu_usage_avg
        expr: avg(rate(container_cpu_usage_seconds_total[5m])) by (pod, namespace)
      
      - record: aiml:memory_usage_percent
        expr: (container_memory_working_set_bytes / container_spec_memory_limit_bytes) * 100
      
      - record: aiml:model_inference_rate
        expr: rate(ml_model_predictions_total[5m])
      
      - record: aiml:model_accuracy_avg
        expr: avg(ml_model_accuracy) by (model_name, version)

# Alerting rules
alerting_rules:
  - name: aiml_platform_alerts
    rules:
      - alert: HighErrorRate
        expr: aiml:error_rate_5m > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.instance }}"
      
      - alert: HighResponseTime
        expr: aiml:response_time_95p > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.instance }}"
      
      - alert: HighCPUUsage
        expr: aiml:cpu_usage_avg > 0.8
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value | humanizePercentage }} for {{ $labels.pod }}"
      
      - alert: HighMemoryUsage
        expr: aiml:memory_usage_percent > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% for {{ $labels.pod }}"
      
      - alert: ModelAccuracyDrop
        expr: aiml:model_accuracy_avg < 0.8
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Model accuracy drop detected"
          description: "Model {{ $labels.model_name }} accuracy dropped to {{ $value | humanizePercentage }}"
