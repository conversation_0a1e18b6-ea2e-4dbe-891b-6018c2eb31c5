groups:
  - name: ml_model_performance
    rules:
      # Recording rules for ML model metrics
      - record: ml:model_inference_rate_5m
        expr: rate(ml_model_predictions_total[5m])
        labels:
          metric_type: "performance"

      - record: ml:model_accuracy_avg_1h
        expr: avg_over_time(ml_model_accuracy[1h])
        labels:
          metric_type: "quality"

      - record: ml:model_latency_p95_5m
        expr: histogram_quantile(0.95, rate(ml_model_inference_duration_seconds_bucket[5m]))
        labels:
          metric_type: "performance"

      - record: ml:model_error_rate_5m
        expr: rate(ml_model_errors_total[5m]) / rate(ml_model_predictions_total[5m])
        labels:
          metric_type: "reliability"

      - record: ml:model_memory_usage_avg
        expr: avg(ml_model_memory_usage_bytes) by (model_name, version)
        labels:
          metric_type: "resource"

      - record: ml:model_gpu_utilization_avg
        expr: avg(ml_model_gpu_utilization_percent) by (model_name, version)
        labels:
          metric_type: "resource"

      - record: ml:model_throughput_5m
        expr: rate(ml_model_predictions_total[5m]) * 60
        labels:
          metric_type: "performance"

      - record: ml:model_data_drift_score
        expr: ml_model_data_drift_score
        labels:
          metric_type: "quality"

  - name: ml_model_alerts
    rules:
      # Critical Alerts
      - alert: ModelAccuracyDrop
        expr: ml:model_accuracy_avg_1h < 0.7
        for: 10m
        labels:
          severity: critical
          team: ml-ops
          component: model
        annotations:
          summary: "Critical model accuracy drop detected"
          description: "Model {{ $labels.model_name }} (v{{ $labels.version }}) accuracy dropped to {{ $value | humanizePercentage }}. This is below the critical threshold of 70%."
          runbook_url: "https://docs.aiml-platform.com/runbooks/model-accuracy-drop"
          dashboard_url: "https://grafana.aiml-platform.com/d/model-performance/{{ $labels.model_name }}"

      - alert: ModelHighErrorRate
        expr: ml:model_error_rate_5m > 0.1
        for: 5m
        labels:
          severity: critical
          team: ml-ops
          component: model
        annotations:
          summary: "High model error rate detected"
          description: "Model {{ $labels.model_name }} error rate is {{ $value | humanizePercentage }}. This exceeds the critical threshold of 10%."
          runbook_url: "https://docs.aiml-platform.com/runbooks/model-error-rate"

      - alert: ModelInferenceDown
        expr: up{job="ml-model-metrics"} == 0
        for: 2m
        labels:
          severity: critical
          team: ml-ops
          component: infrastructure
        annotations:
          summary: "Model inference service is down"
          description: "Model inference service {{ $labels.instance }} has been down for more than 2 minutes."
          runbook_url: "https://docs.aiml-platform.com/runbooks/service-down"

      # Warning Alerts
      - alert: ModelAccuracyWarning
        expr: ml:model_accuracy_avg_1h < 0.85 and ml:model_accuracy_avg_1h >= 0.7
        for: 15m
        labels:
          severity: warning
          team: ml-ops
          component: model
        annotations:
          summary: "Model accuracy below warning threshold"
          description: "Model {{ $labels.model_name }} accuracy is {{ $value | humanizePercentage }}, below the warning threshold of 85%."
          runbook_url: "https://docs.aiml-platform.com/runbooks/model-accuracy-warning"

      - alert: ModelHighLatency
        expr: ml:model_latency_p95_5m > 2.0
        for: 10m
        labels:
          severity: warning
          team: ml-ops
          component: performance
        annotations:
          summary: "High model inference latency"
          description: "Model {{ $labels.model_name }} 95th percentile latency is {{ $value }}s, exceeding the 2s threshold."
          runbook_url: "https://docs.aiml-platform.com/runbooks/high-latency"

      - alert: ModelLowThroughput
        expr: ml:model_throughput_5m < 10
        for: 15m
        labels:
          severity: warning
          team: ml-ops
          component: performance
        annotations:
          summary: "Low model throughput detected"
          description: "Model {{ $labels.model_name }} throughput is {{ $value }} predictions/minute, below expected threshold."

      - alert: ModelHighMemoryUsage
        expr: ml:model_memory_usage_avg > 8e9  # 8GB
        for: 10m
        labels:
          severity: warning
          team: ml-ops
          component: resource
        annotations:
          summary: "High model memory usage"
          description: "Model {{ $labels.model_name }} is using {{ $value | humanizeBytes }} of memory."

      - alert: ModelDataDrift
        expr: ml:model_data_drift_score > 0.3
        for: 30m
        labels:
          severity: warning
          team: ml-ops
          component: quality
        annotations:
          summary: "Data drift detected in model"
          description: "Model {{ $labels.model_name }} shows data drift score of {{ $value }}, indicating potential model degradation."
          runbook_url: "https://docs.aiml-platform.com/runbooks/data-drift"

      # Info Alerts
      - alert: ModelRetrainingRecommended
        expr: ml:model_accuracy_avg_1h < 0.9 and ml:model_data_drift_score > 0.2
        for: 1h
        labels:
          severity: info
          team: ml-ops
          component: maintenance
        annotations:
          summary: "Model retraining recommended"
          description: "Model {{ $labels.model_name }} shows signs of degradation. Consider retraining."

      - alert: ModelVersionMismatch
        expr: count by (model_name) (count by (model_name, version) (ml_model_predictions_total)) > 1
        for: 30m
        labels:
          severity: info
          team: ml-ops
          component: deployment
        annotations:
          summary: "Multiple model versions detected"
          description: "Multiple versions of model {{ $labels.model_name }} are serving traffic simultaneously."

  - name: ml_infrastructure_alerts
    rules:
      # GPU Alerts
      - alert: GPUHighUtilization
        expr: ml:model_gpu_utilization_avg > 90
        for: 15m
        labels:
          severity: warning
          team: infrastructure
          component: gpu
        annotations:
          summary: "High GPU utilization"
          description: "GPU utilization for model {{ $labels.model_name }} is {{ $value }}%."

      - alert: GPUMemoryHigh
        expr: nvidia_ml_py_memory_used_bytes / nvidia_ml_py_memory_total_bytes > 0.9
        for: 10m
        labels:
          severity: warning
          team: infrastructure
          component: gpu
        annotations:
          summary: "High GPU memory usage"
          description: "GPU memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

      # Model Registry Alerts
      - alert: ModelRegistryDown
        expr: up{job="model-registry"} == 0
        for: 5m
        labels:
          severity: critical
          team: ml-ops
          component: registry
        annotations:
          summary: "Model registry is down"
          description: "Model registry service is unavailable."

      # Training Pipeline Alerts
      - alert: TrainingJobFailed
        expr: increase(ml_training_jobs_failed_total[1h]) > 0
        labels:
          severity: warning
          team: ml-ops
          component: training
        annotations:
          summary: "Training job failed"
          description: "{{ $value }} training jobs have failed in the last hour."

      - alert: TrainingJobStuck
        expr: ml_training_job_duration_seconds > 14400  # 4 hours
        labels:
          severity: warning
          team: ml-ops
          component: training
        annotations:
          summary: "Training job running too long"
          description: "Training job {{ $labels.job_id }} has been running for {{ $value | humanizeDuration }}."

  - name: ml_business_metrics
    rules:
      # Business Impact Alerts
      - alert: ModelPredictionVolumeDropped
        expr: (ml:model_throughput_5m / ml:model_throughput_5m offset 1d) < 0.5
        for: 30m
        labels:
          severity: warning
          team: business
          component: usage
        annotations:
          summary: "Significant drop in model usage"
          description: "Model {{ $labels.model_name }} prediction volume dropped by {{ (1 - $value) | humanizePercentage }} compared to yesterday."

      - alert: ModelCostThresholdExceeded
        expr: ml_model_cost_per_hour > 100
        for: 1h
        labels:
          severity: info
          team: finance
          component: cost
        annotations:
          summary: "Model cost threshold exceeded"
          description: "Model {{ $labels.model_name }} is costing ${{ $value }}/hour, exceeding the $100 threshold."

      # SLA Alerts
      - alert: ModelSLABreach
        expr: ml:model_latency_p95_5m > 1.0 or ml:model_error_rate_5m > 0.01
        for: 5m
        labels:
          severity: critical
          team: sre
          component: sla
        annotations:
          summary: "Model SLA breach detected"
          description: "Model {{ $labels.model_name }} is breaching SLA requirements."
          runbook_url: "https://docs.aiml-platform.com/runbooks/sla-breach"
