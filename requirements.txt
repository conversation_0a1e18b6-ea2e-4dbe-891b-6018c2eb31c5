# Core Framework
streamlit>=1.28.0
pandas>=2.0.0
numpy>=1.24.0
plotly>=5.15.0

# AutoML & Core ML
autogluon.tabular>=0.8.0
autogluon.multimodal>=0.8.0
autogluon.timeseries>=0.8.0
autogluon.vision>=0.8.0
autogluon.text>=0.8.0

# Deep Learning Frameworks
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
tensorflow>=2.13.0
tensorflow-probability>=0.21.0
keras>=2.13.0
jax>=0.4.0
jaxlib>=0.4.0
flax>=0.7.0

# Computer Vision
opencv-python>=4.8.0
albumentations>=1.3.0
timm>=0.9.0
ultralytics>=8.0.0
detectron2 @ git+https://github.com/facebookresearch/detectron2.git
segment-anything @ git+https://github.com/facebookresearch/segment-anything.git
Pillow>=10.0.0
imageio>=2.31.0
scikit-image>=0.21.0

# Natural Language Processing
transformers>=4.30.0
datasets>=2.14.0
tokenizers>=0.13.0
sentence-transformers>=2.2.0
spacy>=3.6.0
nltk>=3.8.0
gensim>=4.3.0
textblob>=0.17.0
langchain>=0.0.200
langchain-community>=0.0.20
openai>=0.27.0
anthropic>=0.3.0
huggingface-hub>=0.16.0

# Time Series
prophet>=1.1.0
statsmodels>=0.14.0
pmdarima>=2.0.0
sktime>=0.21.0
tsfresh>=0.20.0
tslearn>=0.6.0
neuralprophet>=0.6.0
darts>=0.24.0

# Reinforcement Learning
stable-baselines3>=2.0.0
ray[rllib]>=2.5.0
gymnasium>=0.29.0
gym>=0.26.0
pettingzoo>=1.24.0
sb3-contrib>=2.0.0
tensorboard>=2.13.0

# Graph Neural Networks
torch-geometric>=2.3.0
dgl>=1.1.0
networkx>=3.1.0
graph-tool>=2.45.0
stellargraph>=1.2.0

# Generative AI
diffusers>=0.18.0
accelerate>=0.20.0
xformers>=0.0.20
controlnet-aux>=0.0.6
compel>=2.0.0
invisible-watermark>=0.2.0

# MLOps & Experiment Tracking
mlflow>=2.5.0
wandb>=0.15.0
neptune-client>=1.3.0
tensorboard>=2.13.0
optuna>=3.2.0
hyperopt>=0.2.7
ray[tune]>=2.5.0

# Data Processing & Analytics
cudf-cu11>=23.06.0; platform_system=="Linux"
cuml-cu11>=23.06.0; platform_system=="Linux"
cupy-cuda11x>=12.0.0; platform_system=="Linux"
dask>=2023.6.0
polars>=0.18.0
modin[ray]>=0.22.0
vaex>=4.16.0
datashader>=0.15.0

# Advanced Analytics
shap>=0.42.0
lime>=0.2.0
eli5>=0.13.0
yellowbrick>=1.5.0
feature-engine>=1.6.0
imbalanced-learn>=0.11.0
category-encoders>=2.6.0

# Visualization & UI
matplotlib>=3.7.0
seaborn>=0.12.0
bokeh>=3.2.0
altair>=5.0.0
streamlit-aggrid>=0.3.4
streamlit-option-menu>=0.3.6
streamlit-plotly-events>=0.0.6
ydata-profiling>=4.3.0
streamlit-pandas-profiling>=0.1.3

# Model Deployment & Serving
onnx>=1.14.0
onnxruntime>=1.15.0
skl2onnx>=1.15.0
tf2onnx>=1.15.0
torch2trt>=0.4.0
tensorrt>=8.6.0; platform_system=="Linux"
triton-client>=2.34.0

# Database & Storage
sqlalchemy>=2.0.0
pymongo>=4.4.0
redis>=4.6.0
boto3>=1.28.0
azure-storage-blob>=12.17.0
google-cloud-storage>=2.10.0
psycopg2-binary>=2.9.0
mysql-connector-python>=8.1.0

# Quantum Computing
qiskit>=0.43.0
cirq>=1.2.0
pennylane>=0.31.0
tensorflow-quantum>=0.7.0

# Federated Learning
flower>=1.4.0
syft>=0.8.0

# Edge AI & Optimization
onnxruntime-gpu>=1.15.0
openvino>=2023.0.0
tflite-runtime>=2.13.0
tensorrt>=8.6.0; platform_system=="Linux"

# Audio Processing
librosa>=0.10.0
soundfile>=0.12.0
audioread>=3.0.0
pydub>=0.25.0
speechrecognition>=3.10.0

# Scientific Computing
scipy>=1.11.0
sympy>=1.12.0
numba>=0.57.0
cython>=3.0.0

# Utilities
tqdm>=4.65.0
joblib>=1.3.0
cloudpickle>=2.2.0
psutil>=5.9.0
requests>=2.31.0
aiohttp>=3.8.0
fastapi>=0.100.0
uvicorn>=0.23.0
pydantic>=2.0.0
typer>=0.9.0
rich>=13.4.0
loguru>=0.7.0