#!/bin/bash

# Enterprise AI/ML Platform - Production Deployment Script
# Comprehensive deployment automation with health checks and rollback

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-staging}"
VERSION="${2:-latest}"
NAMESPACE="aiml-platform-${ENVIRONMENT}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
error_exit() {
    log_error "$1"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check required tools
    local tools=("kubectl" "helm" "docker" "terraform" "aws")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            error_exit "$tool is not installed or not in PATH"
        fi
    done
    
    # Check environment variables
    local required_vars=("AWS_ACCESS_KEY_ID" "AWS_SECRET_ACCESS_KEY")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            error_exit "Environment variable $var is not set"
        fi
    done
    
    log_success "Prerequisites check passed"
}

# Validate environment
validate_environment() {
    log_info "Validating environment: $ENVIRONMENT"
    
    case "$ENVIRONMENT" in
        "development"|"staging"|"production")
            log_success "Environment $ENVIRONMENT is valid"
            ;;
        *)
            error_exit "Invalid environment: $ENVIRONMENT. Must be development, staging, or production"
            ;;
    esac
}

# Setup AWS credentials and context
setup_aws_context() {
    log_info "Setting up AWS context..."
    
    # Configure AWS CLI
    aws configure set region us-east-1
    
    # Update kubeconfig
    aws eks update-kubeconfig --region us-east-1 --name "aiml-platform-${ENVIRONMENT}"
    
    # Verify connection
    if ! kubectl cluster-info &> /dev/null; then
        error_exit "Failed to connect to Kubernetes cluster"
    fi
    
    log_success "AWS context configured"
}

# Deploy infrastructure with Terraform
deploy_infrastructure() {
    log_info "Deploying infrastructure with Terraform..."
    
    cd "$PROJECT_ROOT/infrastructure/terraform"
    
    # Initialize Terraform
    terraform init -backend-config="key=infrastructure/${ENVIRONMENT}/terraform.tfstate"
    
    # Plan deployment
    terraform plan -var="environment=${ENVIRONMENT}" -out=tfplan
    
    # Apply if not in dry-run mode
    if [[ "${DRY_RUN:-false}" != "true" ]]; then
        terraform apply tfplan
        log_success "Infrastructure deployed successfully"
    else
        log_info "Dry run mode - infrastructure plan generated"
    fi
    
    cd "$PROJECT_ROOT"
}

# Build and push Docker images
build_and_push_images() {
    log_info "Building and pushing Docker images..."
    
    local registry="ghcr.io/aiml-platform"
    
    # Login to registry
    echo "$GITHUB_TOKEN" | docker login ghcr.io -u "$GITHUB_ACTOR" --password-stdin
    
    # Build backend image
    log_info "Building backend image..."
    docker build -t "${registry}/backend:${VERSION}" \
        --target production \
        --build-arg VERSION="$VERSION" \
        "$PROJECT_ROOT/backend"
    
    # Build frontend image
    log_info "Building frontend image..."
    docker build -t "${registry}/frontend:${VERSION}" \
        --target production \
        --build-arg VERSION="$VERSION" \
        "$PROJECT_ROOT/frontend"
    
    if [[ "${DRY_RUN:-false}" != "true" ]]; then
        # Push images
        docker push "${registry}/backend:${VERSION}"
        docker push "${registry}/frontend:${VERSION}"
        log_success "Docker images built and pushed"
    else
        log_info "Dry run mode - images built but not pushed"
    fi
}

# Create namespace and secrets
setup_kubernetes_resources() {
    log_info "Setting up Kubernetes resources..."
    
    # Create namespace
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    # Create secrets from environment file
    if [[ -f "$PROJECT_ROOT/.env.${ENVIRONMENT}" ]]; then
        kubectl create secret generic aiml-platform-secrets \
            --from-env-file="$PROJECT_ROOT/.env.${ENVIRONMENT}" \
            --namespace="$NAMESPACE" \
            --dry-run=client -o yaml | kubectl apply -f -
    else
        log_warning "Environment file .env.${ENVIRONMENT} not found"
    fi
    
    # Create config map
    kubectl create configmap aiml-platform-config \
        --from-literal=environment="$ENVIRONMENT" \
        --from-literal=version="$VERSION" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "Kubernetes resources configured"
}

# Deploy with Helm
deploy_with_helm() {
    log_info "Deploying application with Helm..."
    
    local helm_release="aiml-platform-${ENVIRONMENT}"
    local values_file="$PROJECT_ROOT/helm/values-${ENVIRONMENT}.yaml"
    
    # Check if values file exists
    if [[ ! -f "$values_file" ]]; then
        log_warning "Values file $values_file not found, using default values"
        values_file="$PROJECT_ROOT/helm/values.yaml"
    fi
    
    # Deploy or upgrade
    helm upgrade --install "$helm_release" "$PROJECT_ROOT/helm/aiml-platform" \
        --namespace="$NAMESPACE" \
        --values="$values_file" \
        --set image.backend.tag="$VERSION" \
        --set image.frontend.tag="$VERSION" \
        --set environment="$ENVIRONMENT" \
        --timeout=10m \
        --wait
    
    log_success "Application deployed with Helm"
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Wait for backend pods to be ready
    kubectl wait --for=condition=ready pod \
        -l app=aiml-platform-backend \
        -n "$NAMESPACE" \
        --timeout=300s
    
    # Run migrations
    kubectl exec -n "$NAMESPACE" \
        deployment/aiml-platform-backend \
        -- alembic upgrade head
    
    log_success "Database migrations completed"
}

# Health checks
run_health_checks() {
    log_info "Running health checks..."
    
    local backend_url="http://$(kubectl get svc aiml-platform-backend-service -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')/health"
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "$backend_url" &> /dev/null; then
            log_success "Backend health check passed"
            break
        fi
        
        log_info "Health check attempt $attempt/$max_attempts failed, retrying in 10s..."
        sleep 10
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        error_exit "Health checks failed after $max_attempts attempts"
    fi
}

# Smoke tests
run_smoke_tests() {
    log_info "Running smoke tests..."
    
    # Run basic API tests
    kubectl run smoke-test-pod \
        --image=curlimages/curl:latest \
        --rm -i --restart=Never \
        --namespace="$NAMESPACE" \
        -- sh -c "
            curl -f http://aiml-platform-backend-service/health &&
            curl -f http://aiml-platform-frontend-service/ &&
            echo 'Smoke tests passed'
        "
    
    log_success "Smoke tests completed"
}

# Rollback function
rollback_deployment() {
    log_warning "Rolling back deployment..."
    
    local helm_release="aiml-platform-${ENVIRONMENT}"
    
    # Rollback Helm release
    helm rollback "$helm_release" --namespace="$NAMESPACE"
    
    # Wait for rollback to complete
    kubectl rollout status deployment/aiml-platform-backend -n "$NAMESPACE"
    kubectl rollout status deployment/aiml-platform-frontend -n "$NAMESPACE"
    
    log_success "Rollback completed"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary resources..."
    
    # Remove temporary files
    rm -f "$PROJECT_ROOT/infrastructure/terraform/tfplan"
    
    # Logout from Docker registry
    docker logout ghcr.io &> /dev/null || true
    
    log_success "Cleanup completed"
}

# Main deployment function
main() {
    log_info "Starting deployment of AI/ML Platform"
    log_info "Environment: $ENVIRONMENT"
    log_info "Version: $VERSION"
    log_info "Namespace: $NAMESPACE"
    
    # Set trap for cleanup
    trap cleanup EXIT
    trap 'rollback_deployment; exit 1' ERR
    
    # Deployment steps
    check_prerequisites
    validate_environment
    setup_aws_context
    
    if [[ "$ENVIRONMENT" == "production" ]] || [[ "${DEPLOY_INFRASTRUCTURE:-false}" == "true" ]]; then
        deploy_infrastructure
    fi
    
    build_and_push_images
    setup_kubernetes_resources
    deploy_with_helm
    run_migrations
    run_health_checks
    run_smoke_tests
    
    log_success "Deployment completed successfully!"
    log_info "Application is available at: https://${ENVIRONMENT}.aiml-platform.com"
}

# Script usage
usage() {
    echo "Usage: $0 <environment> [version]"
    echo ""
    echo "Arguments:"
    echo "  environment    Target environment (development|staging|production)"
    echo "  version        Docker image version (default: latest)"
    echo ""
    echo "Environment Variables:"
    echo "  DRY_RUN               Set to 'true' for dry run mode"
    echo "  DEPLOY_INFRASTRUCTURE Set to 'true' to deploy infrastructure"
    echo "  GITHUB_TOKEN          GitHub token for Docker registry"
    echo "  GITHUB_ACTOR          GitHub username"
    echo ""
    echo "Examples:"
    echo "  $0 staging v1.2.3"
    echo "  DRY_RUN=true $0 production latest"
    echo "  DEPLOY_INFRASTRUCTURE=true $0 production v2.0.0"
}

# Check if help is requested
if [[ "${1:-}" == "-h" ]] || [[ "${1:-}" == "--help" ]]; then
    usage
    exit 0
fi

# Check if environment is provided
if [[ $# -lt 1 ]]; then
    log_error "Environment argument is required"
    usage
    exit 1
fi

# Run main function
main "$@"
