#!/bin/bash

# Enterprise AI/ML Platform - Production Deployment Script
# ========================================================
# 
# This script deploys the complete AI/ML platform to production
# with all optimizations and enterprise features enabled.

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="aiml-platform"
ENVIRONMENT="production"
VERSION="2.0.0"
REGISTRY="your-registry.com"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "docker is not installed"
        exit 1
    fi
    
    # Check if helm is installed
    if ! command -v helm &> /dev/null; then
        log_error "helm is not installed"
        exit 1
    fi
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

create_namespace() {
    log_info "Creating namespace: $NAMESPACE"
    
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    kubectl label namespace $NAMESPACE environment=$ENVIRONMENT --overwrite
    
    log_success "Namespace created/updated"
}

setup_secrets() {
    log_info "Setting up secrets..."
    
    # Database secrets
    kubectl create secret generic db-secrets \
        --from-literal=postgres-password="$(openssl rand -base64 32)" \
        --from-literal=mongodb-password="$(openssl rand -base64 32)" \
        --from-literal=redis-password="$(openssl rand -base64 32)" \
        --namespace=$NAMESPACE \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # JWT secrets
    kubectl create secret generic jwt-secrets \
        --from-literal=secret-key="$(openssl rand -base64 64)" \
        --from-literal=refresh-secret="$(openssl rand -base64 64)" \
        --namespace=$NAMESPACE \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # API keys
    kubectl create secret generic api-secrets \
        --from-literal=openai-api-key="${OPENAI_API_KEY:-}" \
        --from-literal=huggingface-token="${HUGGINGFACE_TOKEN:-}" \
        --namespace=$NAMESPACE \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "Secrets configured"
}

build_and_push_images() {
    log_info "Building and pushing Docker images..."
    
    # Build backend image
    log_info "Building backend image..."
    docker build -t $REGISTRY/aiml-platform-backend:$VERSION -f backend/Dockerfile backend/
    docker push $REGISTRY/aiml-platform-backend:$VERSION
    
    # Build frontend image
    log_info "Building frontend image..."
    docker build -t $REGISTRY/aiml-platform-frontend:$VERSION -f frontend/Dockerfile frontend/
    docker push $REGISTRY/aiml-platform-frontend:$VERSION
    
    log_success "Images built and pushed"
}

deploy_infrastructure() {
    log_info "Deploying infrastructure components..."
    
    # Deploy PostgreSQL
    helm repo add bitnami https://charts.bitnami.com/bitnami
    helm repo update
    
    helm upgrade --install postgresql bitnami/postgresql \
        --namespace $NAMESPACE \
        --set auth.existingSecret=db-secrets \
        --set auth.secretKeys.adminPasswordKey=postgres-password \
        --set primary.persistence.size=100Gi \
        --set primary.resources.requests.memory=2Gi \
        --set primary.resources.requests.cpu=1000m \
        --set metrics.enabled=true \
        --wait
    
    # Deploy MongoDB
    helm upgrade --install mongodb bitnami/mongodb \
        --namespace $NAMESPACE \
        --set auth.existingSecret=db-secrets \
        --set auth.secretKeys.rootPasswordKey=mongodb-password \
        --set persistence.size=100Gi \
        --set resources.requests.memory=2Gi \
        --set resources.requests.cpu=1000m \
        --set metrics.enabled=true \
        --wait
    
    # Deploy Redis
    helm upgrade --install redis bitnami/redis \
        --namespace $NAMESPACE \
        --set auth.existingSecret=db-secrets \
        --set auth.secretKeys.authPasswordKey=redis-password \
        --set master.persistence.size=20Gi \
        --set replica.replicaCount=2 \
        --set metrics.enabled=true \
        --wait
    
    log_success "Infrastructure deployed"
}

deploy_monitoring() {
    log_info "Deploying monitoring stack..."
    
    # Deploy Prometheus
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo update
    
    helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace monitoring \
        --create-namespace \
        --set prometheus.prometheusSpec.retention=30d \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=100Gi \
        --set grafana.adminPassword="$(openssl rand -base64 32)" \
        --set grafana.persistence.enabled=true \
        --set grafana.persistence.size=10Gi \
        --wait
    
    # Deploy custom monitoring configs
    kubectl apply -f monitoring/ -n monitoring
    
    log_success "Monitoring stack deployed"
}

deploy_application() {
    log_info "Deploying application..."
    
    # Update image tags in manifests
    sed -i "s|IMAGE_TAG|$VERSION|g" kubernetes/manifests/*.yaml
    sed -i "s|REGISTRY|$REGISTRY|g" kubernetes/manifests/*.yaml
    
    # Apply Kubernetes manifests
    kubectl apply -f kubernetes/manifests/ -n $NAMESPACE
    
    # Wait for deployments
    kubectl rollout status deployment/aiml-platform-backend -n $NAMESPACE --timeout=600s
    kubectl rollout status deployment/aiml-platform-frontend -n $NAMESPACE --timeout=600s
    
    log_success "Application deployed"
}

setup_ingress() {
    log_info "Setting up ingress..."
    
    # Deploy NGINX Ingress Controller
    helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
    helm repo update
    
    helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
        --namespace ingress-nginx \
        --create-namespace \
        --set controller.metrics.enabled=true \
        --set controller.podAnnotations."prometheus\.io/scrape"="true" \
        --set controller.podAnnotations."prometheus\.io/port"="10254" \
        --wait
    
    # Apply ingress manifests
    kubectl apply -f kubernetes/ingress/ -n $NAMESPACE
    
    log_success "Ingress configured"
}

run_health_checks() {
    log_info "Running health checks..."
    
    # Wait for services to be ready
    sleep 30
    
    # Check backend health
    BACKEND_URL=$(kubectl get ingress aiml-platform-ingress -n $NAMESPACE -o jsonpath='{.spec.rules[0].host}')
    if curl -f "https://$BACKEND_URL/health" > /dev/null 2>&1; then
        log_success "Backend health check passed"
    else
        log_error "Backend health check failed"
        exit 1
    fi
    
    # Check frontend
    if curl -f "https://$BACKEND_URL" > /dev/null 2>&1; then
        log_success "Frontend health check passed"
    else
        log_error "Frontend health check failed"
        exit 1
    fi
    
    # Check database connectivity
    kubectl exec -n $NAMESPACE deployment/aiml-platform-backend -- python -c "
import asyncio
from app.core.database import init_db
asyncio.run(init_db())
print('Database connection successful')
"
    
    log_success "All health checks passed"
}

setup_autoscaling() {
    log_info "Setting up autoscaling..."
    
    # Deploy HPA
    kubectl apply -f kubernetes/autoscaling/ -n $NAMESPACE
    
    # Deploy VPA (if available)
    if kubectl get crd verticalpodautoscalers.autoscaling.k8s.io > /dev/null 2>&1; then
        kubectl apply -f kubernetes/vpa/ -n $NAMESPACE
        log_success "VPA configured"
    else
        log_warning "VPA not available in cluster"
    fi
    
    log_success "Autoscaling configured"
}

setup_backup() {
    log_info "Setting up backup..."
    
    # Deploy backup CronJobs
    kubectl apply -f kubernetes/backup/ -n $NAMESPACE
    
    log_success "Backup configured"
}

print_deployment_info() {
    log_success "Deployment completed successfully!"
    echo ""
    echo "=== Deployment Information ==="
    echo "Namespace: $NAMESPACE"
    echo "Version: $VERSION"
    echo "Environment: $ENVIRONMENT"
    echo ""
    
    # Get ingress URL
    INGRESS_URL=$(kubectl get ingress aiml-platform-ingress -n $NAMESPACE -o jsonpath='{.spec.rules[0].host}' 2>/dev/null || echo "Not configured")
    echo "Application URL: https://$INGRESS_URL"
    
    # Get Grafana URL
    GRAFANA_URL=$(kubectl get ingress grafana -n monitoring -o jsonpath='{.spec.rules[0].host}' 2>/dev/null || echo "Not configured")
    echo "Grafana URL: https://$GRAFANA_URL"
    
    # Get admin passwords
    echo ""
    echo "=== Admin Credentials ==="
    echo "Grafana Admin Password:"
    kubectl get secret prometheus-grafana -n monitoring -o jsonpath='{.data.admin-password}' | base64 -d
    echo ""
    
    echo ""
    echo "=== Useful Commands ==="
    echo "View logs: kubectl logs -f deployment/aiml-platform-backend -n $NAMESPACE"
    echo "Scale backend: kubectl scale deployment aiml-platform-backend --replicas=5 -n $NAMESPACE"
    echo "Port forward: kubectl port-forward svc/aiml-platform-backend-service 8000:80 -n $NAMESPACE"
    echo ""
}

# Main execution
main() {
    log_info "Starting production deployment of Enterprise AI/ML Platform v$VERSION"
    
    check_prerequisites
    create_namespace
    setup_secrets
    build_and_push_images
    deploy_infrastructure
    deploy_monitoring
    deploy_application
    setup_ingress
    setup_autoscaling
    setup_backup
    run_health_checks
    print_deployment_info
    
    log_success "Production deployment completed successfully!"
}

# Run main function
main "$@"
