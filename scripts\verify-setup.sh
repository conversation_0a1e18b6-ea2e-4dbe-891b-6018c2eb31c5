#!/bin/bash

# Enterprise AI/ML Platform Setup Verification Script
# This script verifies that the modernized setup is working correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check versions
check_versions() {
    print_status "Checking tool versions..."
    
    # Check Node.js
    if command_exists node; then
        node_version=$(node -v)
        print_success "Node.js: $node_version"
    else
        print_error "Node.js not found"
        return 1
    fi
    
    # Check Python
    if command_exists python3; then
        python_version=$(python3 --version)
        print_success "Python: $python_version"
    else
        print_error "Python 3 not found"
        return 1
    fi
    
    # Check pnpm
    if command_exists pnpm; then
        pnpm_version=$(pnpm --version)
        print_success "pnpm: v$pnpm_version"
    else
        print_warning "pnpm not found - install with: npm install -g pnpm"
    fi
    
    # Check uv (optional)
    if command_exists uv; then
        uv_version=$(uv --version)
        print_success "uv: $uv_version"
    else
        print_warning "uv not found - install from: https://github.com/astral-sh/uv"
    fi
    
    # Check Docker
    if command_exists docker; then
        docker_version=$(docker --version)
        print_success "Docker: $docker_version"
    else
        print_warning "Docker not found"
    fi
}

# Function to verify backend setup
verify_backend() {
    print_status "Verifying backend setup..."
    
    if [ ! -d "backend/venv" ]; then
        print_error "Backend virtual environment not found at backend/venv"
        return 1
    fi
    
    if [ ! -f "backend/requirements.txt" ]; then
        print_error "Backend requirements.txt not found"
        return 1
    fi
    
    # Check if virtual environment has packages installed
    if [ -d "backend/venv/lib" ] || [ -d "backend/venv/Lib" ]; then
        print_success "Backend virtual environment appears to be set up"
    else
        print_warning "Backend virtual environment may not have packages installed"
    fi
    
    # Check main application file
    if [ -f "backend/app/main.py" ]; then
        print_success "Backend main application file found"
    else
        print_error "Backend main.py not found"
        return 1
    fi
}

# Function to verify frontend setup
verify_frontend() {
    print_status "Verifying frontend setup..."
    
    if [ ! -f "frontend/package.json" ]; then
        print_error "Frontend package.json not found"
        return 1
    fi
    
    # Check for Next.js
    if grep -q "next" "frontend/package.json"; then
        print_success "Next.js found in package.json"
    else
        print_error "Next.js not found in package.json"
        return 1
    fi
    
    # Check for shadcn/ui
    if [ -f "frontend/components.json" ]; then
        print_success "shadcn/ui configuration found"
    else
        print_warning "shadcn/ui configuration not found"
    fi
    
    # Check for Tailwind CSS
    if [ -f "frontend/postcss.config.mjs" ]; then
        print_success "PostCSS configuration found"
    else
        print_warning "PostCSS configuration not found"
    fi
    
    # Check for TypeScript
    if [ -f "frontend/tsconfig.json" ]; then
        print_success "TypeScript configuration found"
    else
        print_warning "TypeScript configuration not found"
    fi
    
    # Check if node_modules exists
    if [ -d "frontend/node_modules" ]; then
        print_success "Frontend dependencies appear to be installed"
    else
        print_warning "Frontend node_modules not found - run 'pnpm install' in frontend directory"
    fi
}

# Function to verify configuration files
verify_config() {
    print_status "Verifying configuration files..."
    
    # Check environment file
    if [ -f ".env.example" ]; then
        print_success "Environment example file found"
    else
        print_warning "Environment example file not found"
    fi
    
    # Check Docker configuration
    if [ -f "docker-compose.yml" ]; then
        print_success "Docker Compose configuration found"
    else
        print_warning "Docker Compose configuration not found"
    fi
    
    # Check development scripts
    if [ -f "scripts/dev.sh" ]; then
        print_success "Development script found"
    else
        print_warning "Development script not found"
    fi
}

# Function to test basic functionality
test_basic_functionality() {
    print_status "Testing basic functionality..."
    
    # Test backend import (if virtual environment is activated)
    if [ -f "backend/venv/bin/activate" ] || [ -f "backend/venv/Scripts/activate" ]; then
        print_status "Testing backend imports..."
        cd backend
        
        # Try to activate virtual environment and test import
        if [ -f "venv/bin/activate" ]; then
            source venv/bin/activate
        elif [ -f "venv/Scripts/activate" ]; then
            source venv/Scripts/activate
        fi
        
        # Test basic Python import
        if python3 -c "import fastapi; print('FastAPI import successful')" 2>/dev/null; then
            print_success "Backend dependencies are working"
        else
            print_warning "Backend dependencies may not be properly installed"
        fi
        
        cd ..
    fi
    
    # Test frontend package.json
    if [ -f "frontend/package.json" ]; then
        print_status "Checking frontend dependencies..."
        cd frontend
        
        if command_exists pnpm; then
            if pnpm list --depth=0 >/dev/null 2>&1; then
                print_success "Frontend dependencies are properly installed"
            else
                print_warning "Frontend dependencies may have issues"
            fi
        else
            print_warning "pnpm not available for dependency check"
        fi
        
        cd ..
    fi
}

# Function to show next steps
show_next_steps() {
    echo
    print_status "Setup verification completed!"
    echo
    print_status "Next steps:"
    echo "1. If any warnings were shown, address them first"
    echo "2. Copy .env.example to .env and configure your settings"
    echo "3. Start the development environment:"
    echo "   - Backend: cd backend && source venv/bin/activate && uvicorn app.main:app --reload"
    echo "   - Frontend: cd frontend && pnpm dev"
    echo "   - Or use: ./scripts/dev.sh start"
    echo "4. Access the application:"
    echo "   - Frontend: http://localhost:3000"
    echo "   - Backend API: http://localhost:8000"
    echo "   - API Docs: http://localhost:8000/docs"
    echo
    print_success "Happy coding! 🚀"
}

# Main function
main() {
    echo "🔍 Enterprise AI/ML Platform Setup Verification"
    echo "=============================================="
    echo
    
    check_versions
    echo
    
    verify_backend
    echo
    
    verify_frontend
    echo
    
    verify_config
    echo
    
    test_basic_functionality
    echo
    
    show_next_steps
}

# Run main function
main "$@"
