#!/usr/bin/env python3
"""
Enterprise AI/ML Platform Verification Script
=============================================

Comprehensive verification script to test all platform components.

Features:
- Module initialization tests
- API endpoint tests
- Performance benchmarks
- Integration tests
- Health checks
- Security validation
"""

import asyncio
import sys
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Any
import numpy as np

# Add backend to path
sys.path.append(str(Path(__file__).parent.parent / "backend"))

# Import platform components
from app.services.enterprise_ml_service import EnterpriseMLService
from modules.computer_vision_enterprise import ComputerVisionModule
from modules.nlp_enterprise import NLPModule
from modules.time_series_enterprise import TimeSeriesModule
from modules.automl_enterprise import AutoMLModule
from modules.deep_learning_enterprise import DeepLearningModule
from modules.reinforcement_learning_enterprise import ReinforcementLearningModule
from modules.generative_ai_enterprise import GenerativeAIModule

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PlatformVerifier:
    """Comprehensive platform verification."""
    
    def __init__(self):
        self.results = {
            'module_tests': {},
            'integration_tests': {},
            'performance_tests': {},
            'security_tests': {},
            'overall_status': 'unknown'
        }
        self.ml_service = None
    
    async def run_all_tests(self):
        """Run all verification tests."""
        logger.info("🚀 Starting Enterprise AI/ML Platform Verification")
        logger.info("=" * 60)
        
        try:
            # Initialize ML service
            await self._initialize_ml_service()
            
            # Run test suites
            await self._test_modules()
            await self._test_integration()
            await self._test_performance()
            await self._test_security()
            
            # Generate report
            self._generate_report()
            
        except Exception as e:
            logger.error(f"Verification failed: {e}")
            self.results['overall_status'] = 'failed'
        
        finally:
            # Cleanup
            if self.ml_service:
                await self.ml_service.cleanup()
    
    async def _initialize_ml_service(self):
        """Initialize ML service."""
        logger.info("🔧 Initializing ML Service...")
        
        try:
            self.ml_service = EnterpriseMLService()
            await self.ml_service.initialize()
            
            logger.info("✅ ML Service initialized successfully")
            self.results['ml_service_init'] = True
            
        except Exception as e:
            logger.error(f"❌ ML Service initialization failed: {e}")
            self.results['ml_service_init'] = False
            raise
    
    async def _test_modules(self):
        """Test individual modules."""
        logger.info("🧪 Testing Individual Modules...")
        
        modules_to_test = [
            ('computer_vision', ComputerVisionModule),
            ('nlp', NLPModule),
            ('time_series', TimeSeriesModule),
            ('automl', AutoMLModule),
            ('deep_learning', DeepLearningModule),
            ('reinforcement_learning', ReinforcementLearningModule),
            ('generative_ai', GenerativeAIModule)
        ]
        
        for module_name, module_class in modules_to_test:
            await self._test_single_module(module_name, module_class)
    
    async def _test_single_module(self, module_name: str, module_class):
        """Test a single module."""
        logger.info(f"  Testing {module_name} module...")
        
        try:
            # Initialize module
            module = module_class()
            await module.initialize()
            
            # Test basic functionality
            info = await module.get_info()
            health = await module.health_check()
            
            # Module-specific tests
            if module_name == 'computer_vision':
                await self._test_computer_vision(module)
            elif module_name == 'nlp':
                await self._test_nlp(module)
            elif module_name == 'time_series':
                await self._test_time_series(module)
            elif module_name == 'automl':
                await self._test_automl(module)
            elif module_name == 'deep_learning':
                await self._test_deep_learning(module)
            
            # Cleanup
            await module.cleanup()
            
            self.results['module_tests'][module_name] = {
                'status': 'passed',
                'info': info,
                'health': health
            }
            
            logger.info(f"  ✅ {module_name} module passed")
            
        except Exception as e:
            logger.error(f"  ❌ {module_name} module failed: {e}")
            self.results['module_tests'][module_name] = {
                'status': 'failed',
                'error': str(e)
            }
    
    async def _test_computer_vision(self, module):
        """Test computer vision module."""
        # Create test image
        test_image = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
        
        # Test image classification
        result = await module.execute_task('classify_image', {
            'image': test_image,
            'model': 'resnet50',
            'top_k': 5
        })
        
        assert result['success'], "Image classification failed"
    
    async def _test_nlp(self, module):
        """Test NLP module."""
        # Test sentiment analysis
        result = await module.execute_task('analyze_sentiment', {
            'text': 'This is a great platform!'
        })
        
        assert result['success'], "Sentiment analysis failed"
    
    async def _test_time_series(self, module):
        """Test time series module."""
        # Create test time series
        test_data = np.sin(np.linspace(0, 10, 100)).tolist()
        
        # Test forecasting
        result = await module.execute_task('forecast', {
            'data': test_data,
            'forecast_periods': 10,
            'method': 'linear_regression'
        })
        
        assert result['success'], "Time series forecasting failed"
    
    async def _test_automl(self, module):
        """Test AutoML module."""
        # Create test dataset
        from sklearn.datasets import make_classification
        X, y = make_classification(n_samples=50, n_features=5, random_state=42)
        
        # Test model selection
        result = await module.execute_task('select_model', {
            'X': X.tolist(),
            'y': y.tolist(),
            'task_type': 'classification',
            'cv_folds': 3
        })
        
        assert result['success'], "AutoML model selection failed"
    
    async def _test_deep_learning(self, module):
        """Test deep learning module."""
        # Test custom network creation
        result = await module.execute_task('create_custom_network', {
            'network_type': 'mlp',
            'architecture_config': {
                'input_size': 10,
                'hidden_sizes': [32, 16],
                'output_size': 2
            }
        })
        
        assert result['success'], "Custom network creation failed"
    
    async def _test_integration(self):
        """Test integration between components."""
        logger.info("🔗 Testing Integration...")
        
        try:
            # Test ML service module listing
            modules = await self.ml_service.list_modules()
            assert len(modules) > 0, "No modules found"
            
            # Test health check
            health = await self.ml_service.health_check()
            assert health['status'] in ['healthy', 'degraded'], "Service unhealthy"
            
            # Test task execution through service
            result = await self.ml_service.execute_task(
                'computer_vision',
                'classify_image',
                {'image': np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)}
            )
            
            self.results['integration_tests'] = {
                'status': 'passed',
                'modules_count': len(modules),
                'health_status': health['status']
            }
            
            logger.info("✅ Integration tests passed")
            
        except Exception as e:
            logger.error(f"❌ Integration tests failed: {e}")
            self.results['integration_tests'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    async def _test_performance(self):
        """Test performance benchmarks."""
        logger.info("⚡ Testing Performance...")
        
        try:
            performance_results = {}
            
            # Test image classification performance
            test_image = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
            
            start_time = time.time()
            for _ in range(5):
                await self.ml_service.execute_task(
                    'computer_vision',
                    'classify_image',
                    {'image': test_image}
                )
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 5
            performance_results['image_classification_avg_time'] = avg_time
            
            # Test text analysis performance
            start_time = time.time()
            for _ in range(10):
                await self.ml_service.execute_task(
                    'nlp',
                    'analyze_sentiment',
                    {'text': 'This is a test sentence for performance testing.'}
                )
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 10
            performance_results['text_analysis_avg_time'] = avg_time
            
            self.results['performance_tests'] = {
                'status': 'passed',
                'results': performance_results
            }
            
            logger.info("✅ Performance tests passed")
            logger.info(f"  Image classification: {performance_results['image_classification_avg_time']:.3f}s avg")
            logger.info(f"  Text analysis: {performance_results['text_analysis_avg_time']:.3f}s avg")
            
        except Exception as e:
            logger.error(f"❌ Performance tests failed: {e}")
            self.results['performance_tests'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    async def _test_security(self):
        """Test security features."""
        logger.info("🔒 Testing Security...")
        
        try:
            security_results = {}
            
            # Test input validation
            try:
                await self.ml_service.execute_task(
                    'nlp',
                    'analyze_sentiment',
                    {'text': ''}  # Empty text should be handled
                )
                security_results['input_validation'] = 'passed'
            except:
                security_results['input_validation'] = 'failed'
            
            # Test error handling
            try:
                await self.ml_service.execute_task(
                    'invalid_module',
                    'invalid_task',
                    {}
                )
                security_results['error_handling'] = 'failed'  # Should have failed
            except:
                security_results['error_handling'] = 'passed'  # Expected to fail
            
            self.results['security_tests'] = {
                'status': 'passed',
                'results': security_results
            }
            
            logger.info("✅ Security tests passed")
            
        except Exception as e:
            logger.error(f"❌ Security tests failed: {e}")
            self.results['security_tests'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    def _generate_report(self):
        """Generate verification report."""
        logger.info("📊 Generating Verification Report...")
        logger.info("=" * 60)
        
        # Calculate overall status
        all_passed = True
        
        # Check module tests
        module_failures = [
            name for name, result in self.results['module_tests'].items()
            if result['status'] != 'passed'
        ]
        
        if module_failures:
            all_passed = False
            logger.warning(f"❌ Failed modules: {', '.join(module_failures)}")
        else:
            logger.info(f"✅ All {len(self.results['module_tests'])} modules passed")
        
        # Check integration tests
        if self.results['integration_tests'].get('status') != 'passed':
            all_passed = False
            logger.warning("❌ Integration tests failed")
        else:
            logger.info("✅ Integration tests passed")
        
        # Check performance tests
        if self.results['performance_tests'].get('status') != 'passed':
            all_passed = False
            logger.warning("❌ Performance tests failed")
        else:
            logger.info("✅ Performance tests passed")
        
        # Check security tests
        if self.results['security_tests'].get('status') != 'passed':
            all_passed = False
            logger.warning("❌ Security tests failed")
        else:
            logger.info("✅ Security tests passed")
        
        # Set overall status
        self.results['overall_status'] = 'passed' if all_passed else 'failed'
        
        # Print summary
        logger.info("=" * 60)
        if all_passed:
            logger.info("🎉 PLATFORM VERIFICATION SUCCESSFUL!")
            logger.info("✅ All tests passed - Platform is ready for production")
        else:
            logger.error("❌ PLATFORM VERIFICATION FAILED!")
            logger.error("🔧 Please fix the issues before deploying to production")
        
        logger.info("=" * 60)
        
        # Save detailed report
        report_path = Path(__file__).parent.parent / "verification_report.json"
        with open(report_path, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        logger.info(f"📄 Detailed report saved to: {report_path}")

async def main():
    """Main verification function."""
    verifier = PlatformVerifier()
    await verifier.run_all_tests()
    
    # Exit with appropriate code
    if verifier.results['overall_status'] == 'passed':
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
